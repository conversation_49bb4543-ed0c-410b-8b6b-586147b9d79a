import React from "react"
import PropTypes from "prop-types"
import { useSelector } from "react-redux"

import { allAmazonMarketplacesSelector } from "selectors/mainStateSelectors"

import { ExportValue } from "components/TableGridLayout/components/ExportValue"
import Link from "components/shared/Link"

import { getAmazonOrderNumberLink } from "utils/links"

export const OrderNumberLink = ({ marketplaceId, orderId }) => {
  const marketPlaces = useSelector(allAmazonMarketplacesSelector) || []

  const marketPlace = marketPlaces.find(({ id }) => id === marketplaceId)

  if (!marketPlace) {
    return null
  }

  const { sales_channel } = marketPlace

  return (
    <ExportValue>
      <Link
        internal={false}
        url={getAmazonOrderNumberLink({
          salesChannel: sales_channel,
          orderId,
        })}
        styleType="primary"
        type="span"
        variant="textSmall"
        text={orderId}
        target="_blank"
        rel="noopener noreferrer"
      />
    </ExportValue>
  )
}

OrderNumberLink.propTypes = {
  marketplaceId: PropTypes.string,
  orderId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
}
