import React from "react"
import { useSelector } from "react-redux"

import { customerRepricerSubscriptionInfoSelector } from "selectors/customerSelectors"

import { EditCostModalColumn } from "components/Grid/components/editCostModalColumn"

import { buildRepricerActivationPopoverModifier } from "utils/customRestrictions"

import { FORM_TYPES } from "constants/productCost"

import { SynchronizeWithRepricerColumnProps } from "./SynchronizeWithRepricerColumnTypes"

export const SynchronizeWithRepricerColumn = ({
  product,
}: SynchronizeWithRepricerColumnProps) => {
  const { isRepricerToBASyncAllowed, isUpgradeRepricerSubscription } =
    useSelector(customerRepricerSubscriptionInfoSelector)

  return (
    <EditCostModalColumn
      formType={FORM_TYPES.synchronize_with_repricer}
      isDisabled={!isRepricerToBASyncAllowed}
      managePermission={isRepricerToBASyncAllowed}
      product={product}
      customPopoverModifier={buildRepricerActivationPopoverModifier({
        isCustomerProductAllowed: !isRepricerToBASyncAllowed,
        isUpgradeRepricerSubscription: isUpgradeRepricerSubscription,
      })}
    />
  )
}
