import { UpdateProductHandlerReturn } from "../ProductTagColumn/ProductTagColumnTypes"

import { Tag } from "types"

export type DropdownProductTagBodyProps = {
  productTags: Tag[]
  errorMessage: string
  onUpdateProduct: UpdateProductHandlerReturn
  onSetErrorMessage: (value: string) => void
  isVisible: boolean
  onClose: () => void
}

export type CreateProductTagHandlerParams = {
  title: string
  successCallback?: () => void
  errorCallback?: () => void
}
