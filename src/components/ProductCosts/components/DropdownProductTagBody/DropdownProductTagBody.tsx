import React, { useCallback, useEffect, useMemo, useRef, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Box, Button, Icon, Select, Typography } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"

import productTagsActions from "actions/productTagsActions"

import { productTagColorsSelector } from "selectors/productTagColorsSelectors"
import { productTagsSelector } from "selectors/productTagsSelectors"

import Link from "components/shared/Link"
import { RestrictedPrimaryButton } from "components/shared/RestrictedButtons"

import { checkIsArray } from "utils/arrayHelpers"
import { generateRandomColor } from "utils/colors"
import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "constants/permissions"

import { Tag } from "types"

import {
  CreateProductTagHandlerParams,
  DropdownProductTagBodyProps,
} from "./DropdownProductTagBodyTypes"

const { getProductTags, createProductTag } = productTagsActions

export const DropdownProductTagBody = ({
  productTags = [],
  errorMessage,
  onUpdateProduct,
  onSetErrorMessage,
  isVisible,
  onClose,
}: DropdownProductTagBodyProps) => {
  const tags: Tag[] = useSelector(productTagsSelector)
  const productTagColors = useSelector(productTagColorsSelector)

  const [selectValue, setSelectValue] = useState<number[]>(() => {
    return checkIsArray(productTags) ? productTags.map(({ id }) => id) : []
  })
  const [searchSelectValue, setSearchSelectValue] = useState<string>("")

  const selectRef = useRef(null)
  const dispatch = useDispatch()

  const selectTagsOptions = useMemo(() => {
    return tags?.map(({ title, id, color }: Tag) => {
      return {
        value: id,
        label: title,
        tagColor: color,
      }
    })
  }, [tags])

  const createProductTagHandler = ({
    title,
    successCallback,
    errorCallback,
  }: CreateProductTagHandlerParams): void => {
    const randomColor = generateRandomColor(productTagColors)

    dispatch(
      createProductTag({
        payload: { title, color: randomColor },
        successCallback: (tag: Tag) => {
          if (tag.id) {
            setSelectValue((prevValue) => {
              return [...prevValue, tag.id]
            })
          }
          dispatch(getProductTags({}))
          successCallback?.()
        },
        failureCallback: (error) => {
          onSetErrorMessage(error?.map(({ message }) => message))
          errorCallback?.()
        },
      }),
    )
  }

  const searchedOption = selectTagsOptions?.find(
    (option) => option.label === searchSelectValue,
  )

  const keyPressEnterHandler = (
    event: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    const hasPressEnterKeyAndValueWithSomeSymbols: boolean = !!(
      event.key === "Enter" &&
      searchSelectValue &&
      searchSelectValue?.trim()?.length
    )

    if (!hasPressEnterKeyAndValueWithSomeSymbols) {
      return
    }

    if (searchedOption) {
      /* We check what the user entered in the Select field. If the tag name matches and there is already one, we will not create 
      it and will select it from the list of available tags. If such a tag has already been selected, we will not allow you to select a duplicate.  */
      const resultTagIds: number[] = selectValue.includes(searchedOption.value)
        ? selectValue
        : [...selectValue, searchedOption.value]

      setSelectValue(resultTagIds)
      setSearchSelectValue("")

      return
    }

    if (hasPressEnterKeyAndValueWithSomeSymbols) {
      event.stopPropagation()
      createProductTagHandler({
        title: searchSelectValue,
        successCallback: () => {
          setSearchSelectValue("")
        },
      })
    }
  }

  const saveTagsHandler = useCallback((): void => {
    return onUpdateProduct({
      productTagsIds: selectValue,
      successCallback: onClose,
    })
  }, [selectValue])

  useEffect(() => {
    if (isVisible) {
      setTimeout(() => selectRef?.current?.focus(), 100)
    }
  }, [isVisible])

  useEffect(() => {
    return () => {
      onClose()
      setSelectValue([])
      onSetErrorMessage("")
      setSearchSelectValue("")
    }
  }, [])

  return (
    <>
      <Box flexDirection="column" padding="m" width={320}>
        <Box justify="space-between" marginBottom="m">
          <Typography variant="--font-body-text-7">
            {l(`{count} ${productTags.length === 1 ? "tag" : "tags"}`, {
              count: productTags.length,
            })}
          </Typography>
          <Link
            internal
            styleType="primary"
            text={<Icon name="icnSetting" size="--icon-size-2" />}
            type="span"
            url={ROUTES.BAS_ROUTES.PATH_BAS_PRODUCT_TAGS}
            variant="text"
          />
        </Box>
        <Select
          ref={selectRef}
          hasSearch
          isContentWidthMatchedWithTrigger
          isFullWidth
          isGlobal
          isMultiLine
          isMultiSelect
          shouldOpenOnFocus
          errorMessage={errorMessage}
          label={l("Product tags")}
          options={selectTagsOptions}
          searchValue={searchSelectValue}
          tagsMode="multiline"
          value={selectValue}
          onChangeValue={setSelectValue}
          onKeyDown={keyPressEnterHandler}
          onSearch={setSearchSelectValue}
        />
      </Box>
      <Box gap="m" justify="flex-end" marginBottom="m" marginRight="m">
        <RestrictedPrimaryButton
          iconOnly
          disabled={!checkIsArray(selectValue) && !checkIsArray(productTags)}
          icon="icnCheck"
          managePermission={permissionKeys.basMyProductsManage}
          popoverMessage={restrictPopoverMessages.alter}
          popoverPlacement="top"
          onClick={saveTagsHandler}
        />
        <Button
          iconOnly
          icon="icnClose"
          variant="secondary"
          onClick={onClose}
        />
      </Box>
    </>
  )
}
