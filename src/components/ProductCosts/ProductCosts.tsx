import React from "react"

import { ProductCostDrawer } from "components/ProductCostDrawer/ProductCostDrawer"
import { TableWrapper } from "components/shared/TableWrapper"

import { TABLE_SETTINGS_KEY } from "constants/productCost"

import { useProductCosts, useProductCostsColumns } from "./hooks"

import { ProductsProps } from "./ProductCostsTypes"

export const ProductCosts = ({ pageName = "" }: ProductsProps) => {
  const {
    getDataHandler,
    getAdditionalData,
    dataSource,
    filtersOptions,
    searchOptions,
    totalCount,
    customDefaultSort,
    getAdditionalTableSettingsDataHandler,
    successGetDataHandler,
    columns,
    selectUnselectProductHandler,
    isBasSubscriptionExpired,
    isGridDataLoading,
  } = useProductCosts()

  const { memoizedTableColumns } = useProductCostsColumns({
    filtersOptions,
    columns,
    successGetDataHandler,
    searchOptions,
    selectUnselectProductHandler,
    isBasSubscriptionExpired,
  })

  return (
    <>
      <TableWrapper
        // @ts-expect-error
        isCustomTableColumns
        isNeedSort
        isStartFromColumnZeroIndex
        actionsColumn={false}
        componentTableSettings={TABLE_SETTINGS_KEY}
        customDefaultSort={customDefaultSort}
        customTableColumns={memoizedTableColumns}
        dataSource={dataSource}
        getAdditionalData={getAdditionalData}
        getAdditionalTableSettingsData={getAdditionalTableSettingsDataHandler}
        getData={getDataHandler}
        isExportRestricted={isBasSubscriptionExpired}
        pageTableSettings={TABLE_SETTINGS_KEY}
        searchOptions={searchOptions}
        selectFiltersOptions={filtersOptions}
        tableClassName="gridView"
        tableGridTitle={pageName}
        tableLoading={isGridDataLoading}
        totalCount={totalCount}
      />

      <ProductCostDrawer onClose={successGetDataHandler} />
    </>
  )
}
