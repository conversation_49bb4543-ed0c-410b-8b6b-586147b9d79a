import React, { useMemo } from "react"
import { useSelector } from "react-redux"
import { Box, EmptyImage, Popover } from "@develop/fe-library"

import { gridSelector } from "selectors/gridSelectors"

import FormattedMessageRedux from "components/FormattedMessage"
import { ProductBulkEdit } from "components/Grid/components"
import AmazonCustomerAccountColumn from "components/Grid/components/amazonCustomerAccountColumn/AmazonCustomerAccountColumn"
import AsinColumn from "components/Grid/components/asinColumn/AsinColumn"
import { ConditionsColumn } from "components/Grid/components/conditionColumn/ConditionsColumn"
import { EditCostModalColumn } from "components/Grid/components/editCostModalColumn"
import SelectionColumn from "components/Grid/components/selectionColumn/SelectionColumn"
import {
  ProductTagColumn,
  SynchronizeWithRepricerColumn,
} from "components/ProductCosts/components"
import { Restrict } from "components/Restrict"
import TextEllipsis from "components/shared/maxTextLines/TextEllipsis"
import { ExportValue } from "components/TableGridLayout/components/ExportValue"

import { checkIsArray } from "utils/arrayHelpers"
import { buildCustomTableColumns } from "utils/buildCustomTableColumns"
import l from "utils/intl"
import ln from "utils/localeNumber"

import { STOCK_TYPES } from "constants/formConstants"
import {
  COLUMN_INPUT_TYPE_ACTION,
  COLUMN_INPUT_TYPE_INPUT,
  COLUMN_INPUT_TYPE_NUMBER,
  COLUMN_INPUT_TYPE_SELECT,
  COLUMN_INPUT_TYPE_SELECT_WITH_SEARCH_MULTIPLE,
} from "constants/grid"
import { restrictPopoverMessages } from "constants/permissions"
import { FORM_TYPES } from "constants/productCost"

import GridProduct from "models/GridProduct"

export const useProductCostsColumns = ({
  filtersOptions,
  successGetDataHandler,
  columns,
  searchOptions,
  selectUnselectProductHandler,
  isBasSubscriptionExpired,
}) => {
  const {
    data: dataSource,
    selectedAll,
    selectedTotalCount,
  } = useSelector(gridSelector)

  const memoizedTableColumns = useMemo(() => {
    const tableColumns = [
      {
        title: "ASIN",
        dataIndex: "asin",
        key: "asin",
        min: 80,
        width: 100,
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        ellipsis: true,
        render: (_, productObject) => (
          <AsinColumn productObject={new GridProduct(productObject)} />
        ),
      },
      {
        title: "SKU",
        dataIndex: "sku",
        key: "sku",
        sorter: true,
        width: 100,
        min: 80,
        type: COLUMN_INPUT_TYPE_INPUT,
        ellipsis: true,
      },
      {
        title: "Image",
        dataIndex: "image",
        key: "image",
        onCell: () => ({
          style: { padding: "5px 0" },
        }),
        width: 70,
        min: 80,
        render: (_, object) => {
          const product = new GridProduct(object)
          const imageUrl = product.getImageUrl()

          return (
            <Box justify="center">
              <EmptyImage height={53} url={imageUrl} width={64} />
              <ExportValue isHidden>{imageUrl}</ExportValue>
            </Box>
          )
        },
      },
      {
        title: "Title",
        dataIndex: "title",
        key: "title",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 300,
        min: 80,
        class: "left",
        onCell: () => ({
          className: "left title",
          style: { textAlign: "left" },
        }),
        render: (title) => (
          <TextEllipsis
            ellipsisChars="..."
            fontSize={12}
            lines={3}
            tooltip={title}
          >
            <ExportValue textAlign="left">
              {!title ? l("N/A") : title}
            </ExportValue>
          </TextEllipsis>
        ),
      },
      {
        title: "Fulfillment method",
        dataIndex: "stock_type",
        key: "stock_type",
        width: 80,
        min: 80,
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT,
        ellipsis: true,
      },
      {
        title: "Cost of goods",
        dataIndex: "buying_price",
        key: "buying_price",
        width: 70,
        min: 80,
        sorter: true,
        type: COLUMN_INPUT_TYPE_NUMBER,
        className: "flex-string",
        render: (_, product) => (
          <ExportValue>
            <EditCostModalColumn
              formType={FORM_TYPES.buying_price}
              product={product}
              successCallback={successGetDataHandler}
            />
          </ExportValue>
        ),
      },
      {
        title: "FBM shipping costs",
        dataIndex: "shipping_cost",
        key: "shipping_cost",
        width: 70,
        min: 80,
        sorter: true,
        type: COLUMN_INPUT_TYPE_NUMBER,
        className: "flex-string",

        render: (_, product) => {
          const {
            shipping_cost,
            stock_type,
            currency_code: currencyCode,
            is_multiple_stock_type,
          } = product

          const isEditable =
            stock_type === STOCK_TYPES.fbm || is_multiple_stock_type

          return (
            <ExportValue>
              {!isEditable ? (
                <Popover content={l("Calculated by Amazon")} placement="top">
                  <Box display="block">
                    {shipping_cost
                      ? ln(shipping_cost, 2, { currency: currencyCode })
                      : l("N/A")}
                  </Box>
                </Popover>
              ) : (
                <EditCostModalColumn
                  formType={FORM_TYPES.shipping_cost}
                  product={product}
                  successCallback={successGetDataHandler}
                />
              )}
            </ExportValue>
          )
        },
      },
      {
        title: "Other fees",
        dataIndex: "other_fees",
        key: "other_fees",
        width: 70,
        min: 80,
        sorter: true,
        type: COLUMN_INPUT_TYPE_NUMBER,
        className: "flex-string",
        render: (_, product) => (
          <ExportValue>
            <EditCostModalColumn
              formType={FORM_TYPES.other_fees}
              product={product}
              successCallback={successGetDataHandler}
            />
          </ExportValue>
        ),
      },
      {
        title: "VAT(%)",
        dataIndex: "vat",
        key: "vat",
        width: 80,
        min: 80,
        sorter: true,
        type: COLUMN_INPUT_TYPE_NUMBER,
        className: "flex-string",
        render: (value, product) => {
          return (
            <ExportValue>
              <EditCostModalColumn
                isPercent
                formType="vat"
                formType={FORM_TYPES.vat}
                product={product}
                successCallback={successGetDataHandler}
                title="VAT"
              />
            </ExportValue>
          )
        },
      },
      {
        title: "Amazon account name",
        dataIndex: "seller_id",
        key: "seller_id",
        width: 100,
        min: 80,
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT,
        ellipsis: true,
        className: "flex-string",
        render: (_, object) => (
          <ExportValue>
            <AmazonCustomerAccountColumn object={object} />
          </ExportValue>
        ),
      },
      {
        title: "Condition",
        dataIndex: "condition",
        key: "condition",
        width: 70,
        min: 80,
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT,
        onCell: () => ({ className: "condition" }),
        render: (condition) => (
          <ExportValue>
            <ConditionsColumn
              conditionId={condition}
              conditions={filtersOptions.productConditions || []}
            />
          </ExportValue>
        ),
      },
      {
        title: "Marketplace",
        dataIndex: "marketplace_id",
        key: "marketplace_id",
        sorter: true,
        width: 120,
        min: 120,
        type: COLUMN_INPUT_TYPE_SELECT,
        ellipsis: true,
        onCell: () => ({ className: "condition" }),
        render: (marketplace_id) => {
          const marketplace = checkIsArray(filtersOptions.marketplace_id)
            ? filtersOptions.marketplace_id?.find(
                ({ value }) => value === marketplace_id,
              )
            : null

          return (
            <ExportValue>
              {marketplace ? (
                marketplace.label
              ) : (
                <FormattedMessageRedux id="N/A" />
              )}
            </ExportValue>
          )
        },
      },
      {
        title: "Synchronize with Repricer",
        dataIndex: "is_enabled_sync_with_repricer",
        key: "is_enabled_sync_with_repricer",
        sorter: true,
        width: 85,
        min: 80,
        type: COLUMN_INPUT_TYPE_SELECT,
        ellipsis: true,
        render: (_, product) => {
          return (
            <ExportValue>
              <SynchronizeWithRepricerColumn product={product} />
            </ExportValue>
          )
        },
      },
      {
        title: "Tags",
        dataIndex: "tags",
        key: "tag_id",
        width: 135,
        min: 100,
        sorter: true,
        className: "flex-string",
        type: COLUMN_INPUT_TYPE_SELECT_WITH_SEARCH_MULTIPLE,
        canTransformArrayTypeValueToStringWithCommas: true,
        canAddEllipsisToOptionInDropdownWithSelect: true,
        render: (_, product) => {
          return (
            <ExportValue title="Tags">
              <ProductTagColumn id={product?.id} productTags={product?.tags} />
            </ExportValue>
          )
        },
      },
    ]

    return [
      {
        key: "selection_column",
        sorter: false,
        type: COLUMN_INPUT_TYPE_ACTION,
        dataIndex: "selection_column",
        width: 88,
        className: "action-cell",
        onCell: () => ({
          className: "action-cell",
        }),
        render: (_, object) => {
          const isSelectionColumnChecked = !!(
            object?.selected ||
            selectedAll ||
            selectedTotalCount
          )

          return (
            <SelectionColumn
              checked={isSelectionColumnChecked}
              disabled={selectedTotalCount}
              object={object}
              restrictBulkCheckboxProps={{
                managePermission: !isBasSubscriptionExpired,
                popoverMessage: restrictPopoverMessages.action,
                popoverPlacement: "topLeft",
              }}
              onSelect={selectUnselectProductHandler}
            />
          )
        },
        title: () => (
          <Restrict
            managePermission={!isBasSubscriptionExpired}
            popoverMessage={restrictPopoverMessages.alter}
            popoverPlacement="topLeft"
          >
            <ProductBulkEdit
              products={dataSource}
              searchOptions={searchOptions}
            />
          </Restrict>
        ),
      },
      ...buildCustomTableColumns({ columns, tableColumns }),
    ]
  }, [
    columns,
    filtersOptions?.productConditions,
    filtersOptions?.marketplace_id,
  ])

  return {
    memoizedTableColumns,
  }
}
