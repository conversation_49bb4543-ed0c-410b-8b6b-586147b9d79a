import { useEffect, useMemo, useRef } from "react"
import { batch, useDispatch, useSelector } from "react-redux"
import { useHistory } from "react-router-dom"
import cn from "classnames"
import findLastIndex from "lodash/findLastIndex"

import gridActions from "actions/gridActions"
import productsCostActions from "actions/productCostActions"
import productTagColorActions from "actions/productTagColorActions"
import productTagsActions from "actions/productTagsActions"

import {
  getGridSearchOptionsSelector,
  gridSelector,
  selectFiltersOptions,
  tableSettingsSelector,
} from "selectors/gridSelectors"
import {
  basAmazonCustomerAccountsSelector,
  isBasSubscriptionExpiredSelector,
} from "selectors/mainStateSelectors"

import getMainApp from "components/MainAppProvider"

import { useTableColumnsVisibility } from "hooks/useTableColumnsVisibility"

import { DEFAULT_SORT } from "constants/grid"
import {
  FORM_FIELDS,
  FORM_TYPES,
  OPEN_PRODUCT_COST_DRAWER_HASH,
  TABLE_SETTINGS_KEY,
} from "constants/productCost"

import { AmazonCustomerAccount } from "types/Models"

const { getProductTags } = productTagsActions

const { startEditProduct } = productsCostActions
const { getProductTagColors } = productTagColorActions

// @ts-ignore
const { getProducts, init, selectUnselectProduct, selectUnselectProductRange } =
  gridActions

export const useProductCosts = () => {
  const dispatch = useDispatch()

  const connectedAmazonCustomerAccounts: AmazonCustomerAccount[] = useSelector(
    basAmazonCustomerAccountsSelector,
  )
  const {
    data: dataSource,
    totalCount,
    loading: isGridDataLoading,
  } = useSelector(gridSelector)
  const tableSettings = useSelector(tableSettingsSelector)
  const searchOptions = useSelector(getGridSearchOptionsSelector)
  const isBasSubscriptionExpired = useSelector(isBasSubscriptionExpiredSelector)
  const filtersOptions = useSelector(selectFiltersOptions)

  const history = useHistory()
  const { hash } = history.location

  const isProductCostsDrawerOpen = useMemo(() => {
    return hash.includes(OPEN_PRODUCT_COST_DRAWER_HASH)
  }, [])

  const getDataHandler = <Type>(params: Type) => {
    dispatch(getProducts(params))
  }

  const initHandler = <Type>(params: Type) => {
    dispatch(init(params))
  }

  const getAdditionalData = (): void => {
    const { actions: mainAppActions } = getMainApp()

    batch(() => {
      mainAppActions?.getAmazonMarketplaces?.()
      mainAppActions?.getCurrencies?.()
      mainAppActions?.getAmazonAccounts?.(false)
      dispatch(getProductTags({}))
      dispatch(getProductTagColors({}))
    })
  }

  const getAdditionalTableSettingsDataHandler = () => {
    initHandler(undefined)
  }

  const selectUnselectProductHandler = <Type>(id: Type) => {
    dispatch(selectUnselectProduct(id))
  }

  const selectUnselectProductRangeHandler = <Type>(products: Type) => {
    dispatch(selectUnselectProductRange(products))
  }

  // TODO: Need refact. Legacy from RRP-1332
  const rowClickHandler = <Type>(event: KeyboardEvent, object: Type): void => {
    const isNotShiftKeyAndIsCtrlAndMetaKey =
      !event.shiftKey && (event.ctrlKey || event.metaKey)

    if (isNotShiftKeyAndIsCtrlAndMetaKey) {
      selectUnselectProductHandler(object)
    }
    if (event.shiftKey) {
      let firstSelected = dataSource.findIndex(
        (product: any) => product.selected === true,
      )
      let lastSelected = findLastIndex(
        dataSource,
        (product: any) => product.selected === true,
      )

      if (lastSelected === -1) {
        return
      }
      let userSelected = dataSource.findIndex(
        // @ts-ignore
        (product: any) => product.id === object.id,
      )

      const isLastSelectedAndUserSelected =
        lastSelected > userSelected && userSelected > firstSelected

      if (isLastSelectedAndUserSelected) {
        const firstIndex = lastSelected

        lastSelected = userSelected
        userSelected = firstIndex
      }
      if (userSelected < firstSelected) {
        lastSelected = userSelected
        userSelected = firstSelected
      }
      const productsToSelect = []

      for (let index = lastSelected; index < userSelected + 1; index++) {
        if (!dataSource[index].selected) {
          productsToSelect.push(dataSource[index])
        }
      }

      selectUnselectProductRangeHandler(productsToSelect)
      document?.getSelection()?.removeAllRanges()
    }
  }

  const rowHandler = <Type>(object: Type) => ({
    onClick: (event: KeyboardEvent) => {
      rowClickHandler(event, object)
    },
    className: cn(
      "product-grid--row",
      // @ts-ignore
      Boolean(object.selected) ? "selected" : "",
    ),
  })

  const { settings } = tableSettings
  const columns = settings[TABLE_SETTINGS_KEY]
    ? settings[TABLE_SETTINGS_KEY].settings
    : undefined

  const isOneAccount = connectedAmazonCustomerAccounts?.length === 1

  useTableColumnsVisibility({
    canUpdateSettings: !isOneAccount,
    visibleColumns: [FORM_FIELDS.sellerId],
    tableSettingsKey: TABLE_SETTINGS_KEY,
  })

  const isDrawerStateHashProcessed = useRef(false)

  useEffect(() => {
    if (!isProductCostsDrawerOpen) {
      isDrawerStateHashProcessed.current = true

      return
    }

    if (isGridDataLoading || isDrawerStateHashProcessed.current) {
      return
    }

    const product = dataSource[0]

    if (!product) {
      return
    }

    const payload = {
      product,
      order: null,
      formType: FORM_TYPES.buying_price,
    }

    dispatch(startEditProduct(payload))

    isDrawerStateHashProcessed.current = true

    history.replace({
      ...history.location,
      hash: history.location.hash.replace(OPEN_PRODUCT_COST_DRAWER_HASH, ""),
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isProductCostsDrawerOpen, isGridDataLoading])

  const successGetDataHandler = (): void => getDataHandler(searchOptions)

  return {
    getDataHandler,
    dataSource,
    filtersOptions,
    searchOptions,
    totalCount,
    getAdditionalData,
    customDefaultSort: DEFAULT_SORT,
    getAdditionalTableSettingsDataHandler,
    columns,
    selectUnselectProductHandler,
    rowHandler,
    successGetDataHandler,
    isBasSubscriptionExpired,
    isGridDataLoading,
  }
}
