
@import 'assets/styles/variables';


.container {
    display: flex;
    align-items: center;
    flex-direction: row;
    margin: 0 -16px;
    padding-left: 53px;
    overflow-x: hidden;
    
    &:not(:first-child) {
        padding-top: 10px;
    }
    
    &:not(:last-child) {
        border-bottom: 1px solid var(--color-border-main);
        padding-bottom: 10px;
    }
}

.marketplaces {
    max-width: 450px;
    width: 100%;
}

.homeMarketplace {
    max-width: 300px;
    width: 100%;
    padding-left: 10px;
    border-left: 1px solid var(--color-grid-hover);

    @media screen and (max-width: $lg) {
        max-width: 200px;
    }

    @media screen and (max-width: 1120px) {
        max-width: 246px;
    }
}

.account {
    padding-left: 10px;
    border-left: 1px solid var(--color-grid-hover);
    width: auto;
}

.section {
    display: flex;
    align-items: center;
    flex-direction: row;
    margin-right: 10px;
    min-height: 32px;
}

.flagIcon {
    margin-left: 10px;
    min-width: 24px;
}

.accountIdContainer {
    max-width: 380px;

    @media screen and (max-width: 1300px) {
        max-width: 290px;
    }

    @media screen and (max-width: 1210px) {
        max-width: 195px;
    }

    @media screen and (max-width: 1120px) {
        max-width: 165px;
    }

    @media screen and (max-width: 1024px) {
        max-width: 180px;
    }
}

.accountId {
    margin-left: 10px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.flags {
    display: grid;
    gap: 5px;
    grid-template-columns: repeat(11, minmax(24px, 1fr));
    margin-left: 10px;
}
