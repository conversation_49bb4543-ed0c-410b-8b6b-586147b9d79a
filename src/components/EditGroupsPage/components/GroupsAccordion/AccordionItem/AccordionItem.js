import React from 'react'
import PropTypes from 'prop-types'
import cn from 'classnames'
import { Typography, Flag } from "@develop/fe-library"
import FormattedMessage from 'components/FormattedMessage'
import { countryCode } from 'utils/countryCode'

import styles from './accordionItem.module.scss'

const AccordionItem = ({ marketplace, marketplaces, accounts = [] }) => {
  const homeMarketplace = marketplaces.find(
    (item) => item.id === marketplace.homeMarketplaceId,
  )
  const marketplaceAccount = accounts.find(({ id }) => {
    return id === marketplace?.amazonCustomerAccountId
  })

  return (
    <div className={styles.container}>
      <div className={cn(styles.marketplaces, styles.section)}>
        <Typography variant="--font-body-text-7" color="--color-text-main">
          <FormattedMessage id="Marketplaces" />:
        </Typography>
        <div className={styles.flags}>
          {marketplace.amazonMarketplaceIds.map((marketplaceId) => {
            const item = marketplaces.find((item) => item.id === marketplaceId)

            return (
              <Flag
                key={marketplaceId}
                size={24}
                locale={countryCode(item?.country)}
                borderRadius="--border-radius-circle"
              />
            )
          })}
        </div>
      </div>
      <div className={cn(styles.homeMarketplace, styles.section)}>
        <Typography variant="--font-body-text-7" color="--color-text-main">
          <FormattedMessage id="Home Marketplace" />:
        </Typography>
        {homeMarketplace ? (
          <Flag
            size={24}
            className={styles.flagIcon}
            locale={countryCode(homeMarketplace.country)}
            borderRadius="--border-radius-circle"
          />
        ) : null}
      </div>
      <div className={cn(styles.account, styles.section)}>
        <Typography variant="--font-body-text-7" color="--color-text-main">
          <FormattedMessage id="Account" />:
        </Typography>
        <span
          className={styles.accountIdContainer}
          title={marketplaceAccount?.customerAccount?.title}
        >
          <Typography variant="--font-body-text-7" color="--color-text-second" className={styles.accountId}>
            {marketplaceAccount?.customerAccount?.title}
          </Typography>
        </span>
      </div>
    </div>
  )
}

AccordionItem.propTypes = {
  marketplace: PropTypes.object.isRequired,
  marketplaces: PropTypes.array.isRequired,
  accounts: PropTypes.array.isRequired,
}

export default AccordionItem
