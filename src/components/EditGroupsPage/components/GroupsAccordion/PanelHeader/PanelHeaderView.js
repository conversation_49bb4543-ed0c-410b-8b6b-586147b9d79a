import React from "react"
import PropTypes from "prop-types"
import Text<PERSON>llipsis from "react-text-ellipsis/src"
import { Typography, Button, Popover, Box } from "@develop/fe-library"

import l from "utils/intl"
import setConfirm from "utils/confirm"

import styles from "./panelHeader.module.scss"

const PanelHeader = ({
  group,
  deleteMarketplaceGroup,
  getMarketplaceGroups,
  searchOptions,
  onEdit,
}) => {
  const handleGroupDelete = (event) => {
    event.stopPropagation()

    setConfirm({
      title: l("Delete item"),
      message: l(`Are you sure you want to delete this entry?`),
      onOk: () =>
        deleteMarketplaceGroup(group, () => {
          getMarketplaceGroups(searchOptions)
        }),
      cancelText: l("Cancel"),
      okText: l("Delete"),
    })
  }

  const handleGroupEdit = (event) => {
    event.stopPropagation()
    event.preventDefault()

    onEdit(group)
  }

  return (
    <header className={styles.header}>
      <div className={styles.marketplaceSection}>
        <Typography variant="--font-body-text-3" title={group.name}>
          <TextEllipsis
            tagClass={styles.panelTitle}
            ellipsisChars="..."
            lines={1}
            tooltip={group.name}
          >
            {group.name}
          </TextEllipsis>
        </Typography>
      </div>

      <Box gap="m" className={styles.controls}>
        <Popover content={l("Edit")} placement="topRight">
          <Button
            icon="icnEdit"
            variant="secondary"
            onClick={handleGroupEdit}
            iconOnly
          />
        </Popover>

        <Popover content={l("Delete")} placement="topRight">
          <Button
            icon="icnDeleteOutlined"
            variant="secondary"
            onClick={handleGroupDelete}
            iconOnly
          />
        </Popover>
      </Box>
    </header>
  )
}

PanelHeader.propTypes = {
  group: PropTypes.object.isRequired,
  deleteMarketplaceGroup: PropTypes.func.isRequired,
  searchOptions: PropTypes.object,
  getMarketplaceGroups: PropTypes.func.isRequired,
}

export default PanelHeader
