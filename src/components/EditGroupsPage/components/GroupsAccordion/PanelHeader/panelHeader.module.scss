@import 'assets/styles/variables';


.panelTitle {
    width: calc(100% - 10px);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 0;
    line-height: inherit;
}

.header {
    position: relative;
    display: flex;
    align-items: center;
    flex-direction: row;
    width: 100%;
    height: 32px;
    padding-left: 10px;
    border-left: 1px solid $hover_grid;
}

.headerSection {
    display: flex;
    align-items: center;
    height: 100%;
}

.marketplaceSection {
    width: 100%;
    max-width: 460px;
}

.headerBorderSection {
    display: flex;
    align-items: center;
    padding-left: 10px;
    border-left: 1px solid $hover_grid;
    height: 100%;
}

.controls {
    position: absolute;
    right: 0;
}
