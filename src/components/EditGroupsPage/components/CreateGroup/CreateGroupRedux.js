import { connect } from "react-redux"

import marketplaceGroupsActions from "actions/marketplaceGroupsActions"

import { currenciesSelector } from "selectors/mainStateSelectors"
import { userGroupedMarketplaceSelector } from "selectors/marketplaceSelectors"

import CreateGroupModal from "./CreateGroupModal"

const { add, update } = marketplaceGroupsActions

const mapStateToProps = (state) => {
  const { currencies = [] } = currenciesSelector(state)
  const {
    groupedMarketplaces,
    marketplaces,
    selectedMarketplaces,
    amazonCustomerAccounts,
  } = userGroupedMarketplaceSelector(state)

  return {
    currencies,
    groupedMarketplaces,
    marketplaces,
    selectedMarketplaces,
    amazonCustomerAccounts,
  }
}

const mapDispatchToProps = (dispatch) => ({
  addMarketplaceGroup: (payload, successCallback, failureCallback) => {
    return dispatch(add(payload, successCallback, failureCallback))
  },

  updateMarketplaceGroup: (payload, successCallback, failureCallback) => {
    return dispatch(update(payload, successCallback, failureCallback))
  },
})

export default connect(mapStateToProps, mapDispatchToProps)(CreateGroupModal)
