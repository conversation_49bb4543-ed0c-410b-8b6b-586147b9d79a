import React, { useEffect, useRef, useState } from "react"
import PropTypes from "prop-types"
import { debounce } from "lodash"
import { Input, Form, Select } from "antd"
import cn from "classnames"
import { useLocation } from "react-router"
import { Button, Box, Icon, Popover } from "@develop/fe-library"

import l from "utils/intl"

import FormattedMessage from "components/FormattedMessage"
import CreateGroupModal from "../CreateGroup"
import withGridFiltersNav from "../../../withGridFiltersNav/withGridFiltersNav"
import withScrollRerender from "../../../hocs/withScrollRerender/withScrollRerender"
import withOutlineLabel from "../../../hocs/withOutlineLabel"

import "./groupsFilter.scss"
import styles from "./groupFilters.module.scss"

const formItemStyle = { marginBottom: 0 }

const SelectWithNav = withGridFiltersNav(
  withScrollRerender(withOutlineLabel(Select), "select"),
)

const { Option } = Select

const GroupFiltersView = ({
  groups,
  expandAccordion,
  searchOptions,
  getMarketplaceGroups,
}) => {
  const form = useRef()
  const groupIds = groups.map((item) => item.id)
  const [isVisible, setVisibility] = useState(false)
  const location = useLocation()

  useEffect(() => {
    if (location.state?.createGroup) {
      setVisibility(true)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    form.current.resetFields()
  }, [searchOptions])

  const onSearch = debounce(({ target }) => {
    getMarketplaceGroups({ ...searchOptions, name: target.value })
  }, 1000)

  const onChange = (value) => {
    getMarketplaceGroups({ ...searchOptions, sort: value })
  }

  const onChangeSort = (a, b, sorting) => {
    if (sorting.order === undefined) {
      getMarketplaceGroups({ ...searchOptions, sort: undefined })
    } else {
      getMarketplaceGroups({
        ...searchOptions,
        sort:
          sorting.order === "descend"
            ? `-${sorting.columnKey}`
            : `${sorting.columnKey}`,
      })
    }
  }

  const handleGroupAdd = () => setVisibility(true)

  const handleCreateGroupCancel = () => {
    setVisibility(false)
  }

  const handleCreateGroupSuccess = () => {
    getMarketplaceGroups(searchOptions)
  }

  const sortDirection =
    searchOptions.sort && searchOptions.sort.startsWith("-")
      ? "descend"
      : "ascend"

  const sortOrder = !!searchOptions.sort && sortDirection
  const sortQuery = searchOptions.sort?.replace("-", "")

  return (
    <div className={styles.container}>
      <div className={styles.filters}>
        <Form
          initialValues={{
            search: searchOptions.name,
            sort: sortQuery,
          }}
          ref={form}
          noValidate
          className={styles.filtersForm}
        >
          <Form.Item
            name="sort"
            className={cn(styles.sortContainer, "groups-sorting-field")}
            style={formItemStyle}
          >
            <SelectWithNav
              allowClear
              sorter
              columnKey={sortQuery || "name"}
              sortOrder={sortOrder}
              sortDirection={sortDirection}
              onChangeSort={onChangeSort}
              placeholder={l("Sort by")}
              className={styles.sortField}
              onChange={onChange}
            >
              <Option value="name">{l("Group Name")}</Option>
              {/*<Option value="id">Id</Option>*/}
            </SelectWithNav>
          </Form.Item>
          <Form.Item
            name="search"
            style={{ ...formItemStyle, width: "100%" }}
            className={styles.searchInputContainer}
          >
            <Input
              className={styles.searchInput}
              placeholder={l("Search")}
              onInput={onSearch}
              prefix={
                <Icon
                  name="icnSearch"
                  size="--icon-size-3"
                  color="--color-icon-static"
                  className={styles.searchIcon}
                />
              }
            />
          </Form.Item>
        </Form>
      </div>
      <Box gap="m" className={styles.controls}>
        <Box
          gap="m"
          justify="center"
          className={styles.desktopButtonsContainer}
        >
          <Button
            icon="icnExpand"
            onClick={() => expandAccordion(groupIds)}
            variant="secondary"
            className={styles.icon}
          >
            <FormattedMessage id="Expand all" />
          </Button>
          <Button
            icon="icnCollapse"
            onClick={() => expandAccordion([])}
            variant="secondary"
            className={styles.icon}
          >
            <FormattedMessage id="Collapse all" />
          </Button>
        </Box>

        <Box gap="m" justify="center" className={styles.mobileButtonsContainer}>
          <Button
            title={l("Expand all")}
            icon="icnExpand"
            onClick={() => expandAccordion(groupIds)}
            variant="secondary"
            iconOnly
            className={styles.icon}
          />
          <Button
            title={l("Collapse all")}
            icon="icnCollapse"
            onClick={() => expandAccordion([])}
            variant="secondary"
            iconOnly
            className={styles.icon}
          />
        </Box>

        <Popover
          className={styles.addGroupButton}
          content={l("Add new group")}
          placement="top"
        >
          <Button icon="icnPlus" onClick={handleGroupAdd} iconOnly />
        </Popover>
      </Box>

      <CreateGroupModal
        onCancel={handleCreateGroupCancel}
        onSuccess={handleCreateGroupSuccess}
        isVisible={isVisible}
      />
    </div>
  )
}

GroupFiltersView.propTypes = {
  groups: PropTypes.array.isRequired,
  expandAccordion: PropTypes.func.isRequired,
  searchOptions: PropTypes.object,
  getMarketplaceGroups: PropTypes.func,
}

export default GroupFiltersView
