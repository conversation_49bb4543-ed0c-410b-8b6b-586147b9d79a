import React from "react"
import PropTypes from "prop-types"
import { FormKeypressHandler } from "components/KeypressHandler"
import FormikWithChangeValidation from "components/shared/FormikWithChangeValidation"
import { RecurrentSettingForm } from "./RecurrentSettingForm"

export const RecurrentSettingFormik = ({
  initialValues,
  onSubmit,
  onFormChange,
  permissionCode,
  ...formProps
}) => {
  const handleFormKeypressSubmit = (formikProps) => () => {
    if (!formikProps.isSubmitting) {
      formikProps.submitForm()
    }
  }

  const handleFormSubmit = (payload, { setErrors, setSubmitting }) => {
    onSubmit(payload, (errors = []) => {
      const hasErrors = errors?.length > 0
      const errorsMapped = hasErrors
        ? errors.reduce(
            (acc, { field, message }) => ({
              ...acc,
              [field]: message,
            }),
            {},
          )
        : {}

      setErrors(errorsMapped)
      setSubmitting(false)
    })
  }

  return (
    <FormikWithChangeValidation
      canSubmitAfterChange
      onFormIsChanged={onFormChange}
      initialValues={{ ...initialValues }}
      onSubmit={handleFormSubmit}
    >
      {(formikProps) => (
        <>
          <FormKeypressHandler
            managePermission={permissionCode}
            onSubmit={handleFormKeypressSubmit(formikProps)}
            onClose={formProps.onCancel}
          />
          <RecurrentSettingForm
            {...{ ...formikProps, ...formProps }}
            permissionCode={permissionCode}
          />
        </>
      )}
    </FormikWithChangeValidation>
  )
}

RecurrentSettingFormik.propsTypes = {
  initialValues: PropTypes.object,
  onCancel: PropTypes.func.isRequired,
  onFormChange: PropTypes.func.isRequired,
  permissionCode: PropTypes.string.isRequired,
}

RecurrentSettingFormik.defaultProps = {
  onCancel: () => {},
}
