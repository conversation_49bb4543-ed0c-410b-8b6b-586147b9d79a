import React from "react"
import { Select } from "antd"
import { Box } from "@develop/fe-library"
import { Field } from "formik"
import PropTypes from "prop-types"

import withOutlineLabel from "components/hocs/withOutlineLabel"
import {
  CheckBox,
  SelectField,
  TextField,
  with<PERSON><PERSON><PERSON><PERSON>ield,
} from "components/shared/FormFields"

import l from "utils/intl"

import { OPTIONS_FILE_HANDLER_NAMES } from "constants/productImport"

import styles from "./recurrentSettingModal.module.scss"

const TextFieldWithError = withErrorField(TextField)

const SelectFieldWithError = withOutlineLabel(
  withErrorField(SelectField),
  "select",
)

export const RecurrentImportSettingFormFields = ({ hasAuth }) => {
  return (
    <>
      <Box marginBottom="m">
        <Field
          className={styles.selectField}
          component={SelectFieldWithError}
          errorClassName={styles.selectError}
          name="handler_name"
          placeholder={l("Select a file type")}
        >
          {OPTIONS_FILE_HANDLER_NAMES.map(({ label, value }) => (
            <Select.Option key={value} value={value}>
              {l(label)}
            </Select.Option>
          ))}
        </Field>
      </Box>

      <Field
        component={TextFieldWithError}
        name="url"
        placeholder={l("Import URL")}
      />
      <div className={styles.protectedField}>
        <Field
          isNumeric
          component={CheckBox}
          label={l("Password protected")}
          name="should_use_auth"
        />
      </div>
      {hasAuth ? (
        <>
          <div className={styles.protectedField}>
            <Field
              component={TextFieldWithError}
              name="auth_login"
              placeholder={l("Login *")}
            />
          </div>
          <div className={styles.protectedField}>
            <Field
              component={TextFieldWithError}
              name="auth_password"
              placeholder={l("Password *")}
            />
          </div>
        </>
      ) : null}
    </>
  )
}

RecurrentImportSettingFormFields.propTypes = {
  hasAuth: PropTypes.bool,
}

RecurrentImportSettingFormFields.defaultProps = {
  hasAuth: false,
}
