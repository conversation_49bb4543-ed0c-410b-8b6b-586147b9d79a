import React, { useMemo } from "react"
import { Modal } from "@develop/fe-library"
import PropTypes from "prop-types"

import l from "utils/intl"

import { RecurrentSettingFormik } from "./RecurrentSettingFormik"

import styles from "./recurrentSettingModal.module.scss"

const importInitialValues = {
  handler_name: "",
  should_use_auth: false,
  auth_login: "",
  auth_password: "",
  url: "",
  cron_expr: "0 * ? * *",
}

export const RecurrentSettingModal = ({
  onSave,
  onClose,
  templates,
  recurrentSetting,
  onFormChange,
  type,
  hasExportTemplates,
  permissionCode,
}) => {
  const initialValues = useMemo(() => {
    const templateFieldName = hasExportTemplates
      ? "template_id"
      : "output_file_format"

    const exportInitialValues = {
      cron_expr: "0 * ? * *",
      [templateFieldName]: templates?.[0]?.id,
    }

    switch (type) {
      case "import":
        return recurrentSetting && recurrentSetting.cron_expr
          ? recurrentSetting
          : importInitialValues
      case "export":
        return recurrentSetting && recurrentSetting.cron_expr
          ? recurrentSetting
          : exportInitialValues
      default:
        return {}
    }
  }, [type, recurrentSetting, hasExportTemplates, templates])

  const modalTitle = l(
    `${
      recurrentSetting && recurrentSetting.cron_expr ? "Update" : "Create"
    } auto ${type} settings`,
  )

  return (
    <Modal
      visible
      className={styles.modalWrapper}
      isKeyboard={false}
      title={modalTitle}
      width="--modal-size-m"
      onCancel={onClose}
    >
      <RecurrentSettingFormik
        hasExportTemplates={hasExportTemplates}
        initialValues={initialValues}
        permissionCode={permissionCode}
        templates={templates}
        type={type}
        onCancel={onClose}
        onFormChange={onFormChange}
        onSubmit={onSave}
      />
    </Modal>
  )
}

RecurrentSettingModal.propTypes = {
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  templates: PropTypes.array.isRequired,
  recurrentSetting: PropTypes.object,
  onFormChange: PropTypes.func.isRequired,
  type: PropTypes.oneOf(["import", "export"]).isRequired,
  hasExportTemplates: PropTypes.bool,
  permissionCode: PropTypes.string.isRequired,
}

RecurrentSettingModal.defaultProps = {
  hasExportTemplates: false,
}
