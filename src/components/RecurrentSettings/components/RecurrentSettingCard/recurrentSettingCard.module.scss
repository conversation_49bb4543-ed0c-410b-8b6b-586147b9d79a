@import "assets/styles/variables.scss";

.container {
  border: 1px solid $border_main;
  border-radius: 5px;
  margin-bottom: 20px;
  max-width: 770px;
  padding: 20px;
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }

  @media (max-width: $xs) {
    margin-bottom: 10px;
  }
}

.fieldLabel.fieldLabel {
  margin-right: 20px;
  max-width: 163px;
  min-width: 163px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.field.field {
  align-items: center;
  display: flex;
  margin-top: 15px;

  &:first-child {
    margin-top: 0;
  }
}

.fieldWrapper {
  display: flex;
  flex-grow: 1;
  position: relative;
  max-width: 450px;

  :global(.ant-input-group-addon) {
    height: 30px;
    padding: 0;
  }
}

.input.input {
  flex-grow: 1;
  height: 32px;

  &.hasErrors {
    border-color: $error_border;
  }

  &[disabled] {
    color: $text_disable;
  }

  input {
    height: 32px;
  }
}

.selectsContainer {
  display: flex;
  flex-grow: 1;
  max-width: 450px;
}

.selectWrapper {
  display: flex;
  flex-grow: 1;
  margin-right: 20px;
  position: relative;

  .errorIcon {
    right: 30px;
  }

  &:last-child {
    margin-right: 0;
  }
}

.select.select {
  flex-grow: 1;
  height: 40px;
  max-width: 100%;

  :global(.ant-select-selector) {
    height: 40px;
  }

  :global(.ant-select-selection-item) {
    line-height: 40px;
  }

  &.hasErrors {
    :global(.ant-select-selector) {
      border-color: $error_border;
    }
  }
}

.checkboxContainer {
  margin-left: 170px;
  margin-top: 30px;
}

.checkbox {
  margin-right: 10px;
}

.checkboxLabel.checkboxLabel {
  color: $text_second;
}

.iconContainer {
  @media (min-width: $md) {
    gap: var(--gap-l);
  }
}

.toggle {
  margin-right: 10px;
}

.errorIcon {
  position: absolute;
  right: 10px;
  top: 13px;
}

.errorTextClass.errorTextClass {
  bottom: -2px;
  color: $error_text;
  display: none;
  height: 0;
  position: absolute;
}

.disabledOverlay {
  background-color: $disable_bg;
  height: 100%;
  left: 0;
  opacity: 0.6;
  position: absolute;
  top: 0;
  width: 100%;
}

.fieldValue {
  color: $text_second;
}

.fullWidth {
  max-width: 100%;
  width: 100%;
}

@media (max-width: $lg) {
  .errorIcon {
    display: none;
  }

  .errorTextClass.errorTextClass {
    display: block;
  }
}

@media (max-width: $xs) {
  .container {
    padding: 10px 10px 15px;
  }

  .checkboxContainer {
    margin-left: 0;
    margin-top: 15px;
  }

  .field.field {
    align-items: flex-start;
    flex-direction: column;
    height: auto;
    margin-top: 10px;
  }

  .fieldWrapper {
    flex-direction: column;
    width: 230px;

    @media (max-width: $xs) {
      width: 100%;
    }
  }

  .fieldLabel {
    line-height: 24px;
  }

  .selectWrapper {
    flex-direction: column;
    margin-right: 10px;

    &:last-child {
      margin-right: 0;
    }
  }

  .select {
    height: 32px;

    :global(.ant-select-selection) {
      height: 32px;
    }

    :global(.ant-select-selection-selected-value) {
      line-height: 32px;
    }
  }

  .input {
    height: 32px;

    input {
      height: 40px;
    }
  }

  .selectsContainer {
    width: 230px;
  }

  .errorTextClass.errorTextClass {
    height: auto;
    position: static;
  }
}

.capitalize {
  &::first-letter {
    text-transform: uppercase;
  }
}

.brokenUrlPopover {
  max-width: 250px;
}
