import React from "react"
import { InputGroup, TextInput } from "@develop/fe-library"
import cn from "classnames"
import { capitalize } from "lodash"
import PropTypes from "prop-types"

import { CopyToClipboard } from "components/shared/CopyToClipboard"

import { getMessage } from "utils/cronHelpers"
import l from "utils/intl"

import { FILE_TYPES } from "constants/productExport"
import { FILE_HANDLER_NAME_TITLES } from "constants/productImport"

import { RecurrentSettingCardControls } from "./RecurrentSettingCardControls"
import { RecurrentSettingCardDescriptionLabel } from "./RecurrentSettingCardDescriptionLabel"
import { RecurrentSettingCardDescriptionRow } from "./RecurrentSettingCardDescriptionRow"

import styles from "./recurrentSettingCard.module.scss"

const urlPlaceholder =
  "A link to the export file will be here after export file is ready"

export const RecurrentSettingCard = ({
  templates,
  onDelete,
  recurrentSetting,
  disabled,
  triggerModal,
  onSetActiveItem,
  type,
  onStatusChange,
  permissionCode,
  viewPermissionCode,
  errors,
}) => {
  const {
    id,
    cron_expr,
    output_file_format,
    should_use_auth,
    is_enabled,
    template_id,
    url,
    auto_export_url,
    is_url_broken,
  } = recurrentSetting

  const template = templates?.find(
    (template) => template.id === (template_id || output_file_format),
  )

  const templateName = template
    ? `${template.title} (${FILE_TYPES[template.format].label})`
    : null

  const useAuthLabel = should_use_auth ? l("Yes") : l("No")

  const cronExpressionLabel = l(`${capitalize(type)} will run`)
  const cronExpressionValue =
    cron_expr && getMessage({ cronExpression: cron_expr })
  const templateUrl = url || auto_export_url
  const templateUrlValue = templateUrl || l(urlPlaceholder)
  const urlErrorMessage = errors?.find(
    (error) => error.field === "url",
  )?.message

  return (
    <>
      <div className={styles.container}>
        <RecurrentSettingCardControls
          isEnabled={!!is_enabled}
          isUrlBroken={is_url_broken}
          permissionCode={permissionCode}
          recurrentSettingId={id}
          triggerModal={triggerModal}
          viewPermissionCode={viewPermissionCode}
          onDelete={onDelete}
          onSetActiveItem={onSetActiveItem}
          onStatusChange={onStatusChange}
        />
        {type === "import" ? (
          <RecurrentSettingCardDescriptionRow
            label={l("Password protection")}
            value={useAuthLabel}
          />
        ) : (
          <RecurrentSettingCardDescriptionRow
            label={l("Export template")}
            value={templateName}
          />
        )}
        <RecurrentSettingCardDescriptionRow
          label={cronExpressionLabel}
          value={cronExpressionValue}
        />

        {type === "import" ? (
          <RecurrentSettingCardDescriptionRow
            label={l("File type")}
            value={l(FILE_HANDLER_NAME_TITLES[recurrentSetting?.handler_name])}
          />
        ) : null}

        <div className={styles.field}>
          <RecurrentSettingCardDescriptionLabel>
            {l(`${capitalize(type)} URL`)}
          </RecurrentSettingCardDescriptionLabel>
          <div className={cn(styles.fieldWrapper, styles.fullWidth)}>
            <InputGroup isFullWidth>
              <TextInput
                isDisabled
                errorMessage={urlErrorMessage}
                errorPopoverPlacement="bottomRight"
                value={templateUrlValue}
              />
              {!templateUrl ? null : (
                <CopyToClipboard
                  iconSize="--icon-size-4"
                  text={templateUrl}
                  variant="secondary"
                />
              )}
            </InputGroup>
          </div>
        </div>

        {!disabled ? null : <div className={styles.disabledOverlay} />}
      </div>
    </>
  )
}

RecurrentSettingCard.propTypes = {
  templates: PropTypes.array.isRequired,
  onDelete: PropTypes.func.isRequired,
  recurrentSetting: PropTypes.object.isRequired,
  disabled: PropTypes.bool,
  triggerModal: PropTypes.func.isRequired,
  onSetActiveItem: PropTypes.func.isRequired,
  type: PropTypes.oneOf(["import", "export"]).isRequired,
  onStatusChange: PropTypes.func.isRequired,
  hasExportTemplates: PropTypes.bool,
  permissionCode: PropTypes.string.isRequired,
  viewPermissionCode: PropTypes.string.isRequired,
  errors: PropTypes.array,
}

RecurrentSettingCard.defaultProps = {
  disabled: false,
  hasExportTemplates: false,
}
