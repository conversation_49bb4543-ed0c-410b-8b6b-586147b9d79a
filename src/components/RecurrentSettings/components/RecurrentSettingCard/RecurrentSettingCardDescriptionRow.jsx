import React from "react"
import PropTypes from "prop-types"
import Typography from "components/Typography"
import { RecurrentSettingCardDescriptionLabel } from "./RecurrentSettingCardDescriptionLabel"

import styles from "./recurrentSettingCard.module.scss"

export const RecurrentSettingCardDescriptionRow = ({ label, value }) => {
  return (
    <div className={styles.field}>
      <RecurrentSettingCardDescriptionLabel>
        {label}
      </RecurrentSettingCardDescriptionLabel>
      <div className={styles.fieldWrapper}>
        <Typography type="div" variant="text" className={styles.fieldValue}>
          {value}
        </Typography>
      </div>
    </div>
  )
}

RecurrentSettingCardDescriptionRow.propTypes = {
  label: PropTypes.node.isRequired,
  value: PropTypes.node.isRequired,
}
