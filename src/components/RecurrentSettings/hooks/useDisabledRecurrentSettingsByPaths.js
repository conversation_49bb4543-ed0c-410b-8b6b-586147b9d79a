import { ROUTES } from "@develop/fe-library/dist/routes"

export const useDisabledRecurrentSettingsByPaths = () => {
  const isCorrectPath = (path) => {
    return window.location.pathname === path
  }

  const isProductExportSetting = isCorrectPath(
    ROUTES.BAS_ROUTES.PATH_BAS_AUTO_EXPORT,
  )
  const isProductImportSetting = isCorrectPath(
    ROUTES.BAS_ROUTES.PATH_BAS_AUTO_IMPORT,
  )

  const isBasPages = isProductExportSetting || isProductImportSetting
  const isDisabledAddButton = !isBasPages
  const isDisabledCard = !isBasPages

  return {
    isDisabledCard,
    isDisabledAddButton,
  }
}
