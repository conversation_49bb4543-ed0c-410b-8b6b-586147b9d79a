import { ROUTES } from "@develop/fe-library/dist/routes"

export const useShowConnectLostAndFound = () => {
  const isCorrectPath = (path) => {
    return window.location.pathname === path
  }

  const isProductDimensionsExportSettings = isCorrectPath(
    ROUTES.LOST_ROUTES.PATH_LOST_DIMENSIONS_AUTO_EXPORT,
  )
  const isProductExportSetting = isCorrectPath(
    ROUTES.BAS_ROUTES.PATH_BAS_AUTO_EXPORT,
  )
  const isProductImportSetting = isCorrectPath(
    ROUTES.BAS_ROUTES.PATH_BAS_AUTO_IMPORT,
  )

  const isBasPages = isProductExportSetting || isProductImportSetting
  const isCurrentPage = isProductDimensionsExportSettings || isBasPages

  const isDisabledAddButton = !isCurrentPage

  const isDisabledCard = !isBasPages

  return {
    isDisabledCard,
    isDisabledAddButton,
  }
}
