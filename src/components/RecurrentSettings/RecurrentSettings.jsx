import React from "react"
import PropTypes from "prop-types"
import { Typography } from "@develop/fe-library"

import { RestrictedButtonPopover } from "components/shared/RestrictedButtonPopover"
import FormattedMessage from "components/FormattedMessage"
import { useDisabledRecurrentSettingsByPaths } from "components/RecurrentSettings/hooks"
import { RecurrentSettingCard, RecurrentSettingModal } from "./components"

import l from "utils/intl"

import { restrictPopoverMessages } from "constants/permissions"

import styles from "./recurrentSettingsView.module.scss"

export const RecurrentSettings = ({
  type,
  title,
  onDelete,
  onSave,
  recurrentSettings,
  modalVisible,
  triggerModal,
  activeItem,
  onSetActiveItem,
  onFormChange,
  onCancel,
  onStatusChange,
  templates,
  hasExportTemplates,
  permissionCode,
  viewPermissionCode,
  errors,
}) => {
  const { isDisabledAddButton, isDisabledCard } =
    useDisabledRecurrentSettingsByPaths()

  return (
    <>
      <div className={styles.container}>
        <Typography className={styles.title} type="div" variant="textLarge">
          <FormattedMessage id={title} />
        </Typography>
        <div className={styles.addButtonContainer}>
          <RestrictedButtonPopover
            managePermission={viewPermissionCode}
            popoverMessage={restrictPopoverMessages.view}
            content={l("Add")}
            onClick={triggerModal}
            icon="icnPlus"
            iconOnly
            disabled={isDisabledAddButton}
          />
        </div>
        <div>
          {recurrentSettings.map((recurrentSettingsItem) => (
            <RecurrentSettingCard
              type={type}
              templates={templates}
              key={recurrentSettingsItem.id}
              onDelete={onDelete}
              recurrentSetting={recurrentSettingsItem}
              disabled={isDisabledCard}
              triggerModal={triggerModal}
              onSetActiveItem={onSetActiveItem}
              onStatusChange={onStatusChange}
              permissionCode={permissionCode}
              viewPermissionCode={viewPermissionCode}
              errors={errors?.[recurrentSettingsItem.id]}
            />
          ))}
        </div>
        {!modalVisible ? null : (
          <RecurrentSettingModal
            type={type}
            onClose={onCancel}
            onSave={onSave}
            templates={templates}
            recurrentSetting={activeItem}
            onFormChange={onFormChange}
            hasExportTemplates={hasExportTemplates}
            permissionCode={permissionCode}
          />
        )}
      </div>
    </>
  )
}

RecurrentSettings.propTypes = {
  type: PropTypes.oneOf(["import", "export"]).isRequired,
  title: PropTypes.string.isRequired,
  onDelete: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  recurrentSettings: PropTypes.array.isRequired,
  modalVisible: PropTypes.bool,
  triggerModal: PropTypes.func.isRequired,
  activeItem: PropTypes.object,
  onSetActiveItem: PropTypes.func.isRequired,
  onFormChange: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  onStatusChange: PropTypes.func.isRequired,
  templates: PropTypes.array,
  hasExportTemplates: PropTypes.bool,
  permissionCode: PropTypes.string.isRequired,
  viewPermissionCode: PropTypes.string.isRequired,
  errors: PropTypes.object,
}

RecurrentSettings.defaultProps = {
  hasExportTemplates: false,
}
