@import 'assets/styles/variables.scss';

.container {
  padding: 10px 20px 20px;
}

.title {
  color: $text_main;
  font-size: 18px;
  font-weight: 700;
}

.addButtonContainer {
  margin: 20px 0;
  max-width: 770px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-height: 32px;
}

.buttonsContainer {
  align-items: flex-end;
  display: flex;
  flex-direction: column;
  max-width: 770px;
  width: 100%;
}

.addButton {
  margin-top: 20px;
}

.button {
  margin-top: 20px;
}

.buttonLabel.buttonLabel {
  color: inherit;
  font-weight: 500;
}

@media (max-width: $xs) {
  .container {
    padding: 10px;
  }

  .checkboxContainer {
    margin: 10px 0;
  }

  .addButton {
    height: 40px;
    margin-right: 10px;
    width: 40px;
  }

  .button {
    margin: 20px 10px 0;
    width: calc(100% - 20px);
  }
}

.exportInfoContainer {
  display: flex;
  justify-content: space-between;
  background-color: $second_bg;
  padding: 10px;
  margin-top: 20px;
}

.templateContainer {
  margin-bottom: 20px;
}

.devider {
  border-top: 1px solid $border_main;
  width: calc(100% + 40px);
  margin-left: -20px;
}
