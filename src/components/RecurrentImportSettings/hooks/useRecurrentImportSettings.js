import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"

import recurrentImportSettingsActions from "actions/recurrentImportSettingsActions"

import {
  modalVisibleSelector,
  recurrentImportSettingsSelector,
} from "selectors/recurrentImportSettingsSelectors"

import { FORM_IS_CHANGED, setConfirm } from "utils/confirm"

const {
  getRecurrentImportSettings,
  addRecurrentImportSetting,
  updateRecurrentImportSetting,
  changeStatusForRecurrentImportSetting,
  deleteRecurrentImportSetting,
  displayRecurrentImportSettingModal,
  showRecurrentImportSettingConfirmationModal,
} = recurrentImportSettingsActions

export const useRecurrentImportSettings = () => {
  const dispatch = useDispatch()

  const recurrentImportSettings = useSelector(recurrentImportSettingsSelector)
  const modalVisible = useSelector(modalVisibleSelector)

  // Local states
  const [activeItem, setActiveItem] = useState(undefined)
  const [isFormChanged, setIsFormChanged] = useState(false)
  const [errors, setErrors] = useState({})

  useEffect(() => {
    dispatch(getRecurrentImportSettings(() => {}))
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const triggerModal = () => {
    dispatch(displayRecurrentImportSettingModal(!modalVisible))
  }

  const closeModal = () => {
    setActiveItem(undefined)
    dispatch(displayRecurrentImportSettingModal(false))
  }

  const handleFormChange = (touched) => {
    setIsFormChanged(touched)
  }

  const handleSetActiveItem = (id) => {
    const item = recurrentImportSettings.find((item) => item.id === id)

    setActiveItem(item)
  }

  const handleDelete = (id) => {
    dispatch(deleteRecurrentImportSetting(id))
  }

  const handleStatusChange = (id) => {
    const item = recurrentImportSettings.find((item) => item.id === id)

    dispatch(
      changeStatusForRecurrentImportSetting(
        item,
        () => {
          setErrors({})
        },
        (errors) => {
          setErrors({
            [id]: errors,
          })
        },
      ),
    )
  }

  const handleSave = (values, failureCallback) => {
    const payload = {
      ...values,
      auth_login: values.should_use_auth ? values.auth_login : undefined,
      auth_password: values.should_use_auth ? values.auth_password : undefined,
    }

    if (values.id) {
      dispatch(
        updateRecurrentImportSetting(payload, closeModal, failureCallback),
      )
    } else {
      dispatch(addRecurrentImportSetting(payload, closeModal, failureCallback))
    }
  }

  const handleCancel = () => {
    if (!isFormChanged) {
      closeModal()
    } else {
      setConfirm({
        template: FORM_IS_CHANGED,
        onOk: () => {
          dispatch(showRecurrentImportSettingConfirmationModal(false))
          closeModal()
          setIsFormChanged(false)
        },
        onCancel: () => {
          dispatch(showRecurrentImportSettingConfirmationModal(false))
        },
      })
      dispatch(showRecurrentImportSettingConfirmationModal(true))
    }
  }

  return {
    recurrentImportSettings,
    modalVisible,
    showConnectLostAndFoundBlock: false,
    activeItem,
    triggerModal,
    errors,
    handleFormChange,
    handleSetActiveItem,
    handleDelete,
    handleStatusChange,
    handleSave,
    handleCancel,
  }
}
