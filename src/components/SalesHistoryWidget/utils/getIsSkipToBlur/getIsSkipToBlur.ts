// DESC: We should skip blur values (or generate them) for next categories:
// categories
// categories.revenue.id === "revenue"
// categories.revenue.children[0].id === "product_sales"
// categories.units.children[0].id === "orders"

import {
  CATEGORIES,
  COMMON_CATEGORIES,
} from "components/SalesHistoryWidget/constants"

export const getIsSkipToBlur = (value: string | undefined): boolean =>
  ![...COMMON_CATEGORIES, CATEGORIES.estimated_profit].includes(value || "")

export const getIsSkipToBlurFreemium = (value: string | undefined): boolean =>
  ![...COMMON_CATEGORIES, "estimated_profit"].includes(value || "")
