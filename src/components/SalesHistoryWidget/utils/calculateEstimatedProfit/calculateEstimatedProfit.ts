import { calculateToggledAmount } from "../calculatesToggledAmount"

import { CalculateEstimatedProfitArgs } from "./calculateEstimatedProfitTypes"

export const calculateEstimatedProfit = ({
  categories,
  settings,
}: CalculateEstimatedProfitArgs): number => {
  if (!categories) {
    return 0
  }

  const revenue = calculateToggledAmount({
    category: categories.revenue,
    settings,
    manageView: false,
  })
  const expenses = calculateToggledAmount({
    category: categories.expenses,
    settings,
    manageView: false,
  })
  const totalRevenue = (categories?.revenue?.amount || 0) - revenue
  const totalExpenses = (categories?.expenses?.amount || 0) - expenses

  return totalRevenue - Math.abs(totalExpenses)
}
