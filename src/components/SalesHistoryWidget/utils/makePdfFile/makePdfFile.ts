import * as htmlToImage from "html-to-image"
import { jsPDF } from "jspdf"

import { MakePdfFileProps } from "./makePdfFileTypes"

export const makePdfFile = ({
  element,
  fileName,
  onSuccessCallback = () => {},
  onErrorCallback = () => {},
}: MakePdfFileProps): void => {
  htmlToImage
    .toCanvas(element, { skipFonts: true })
    .then((canvas) => {
      const document = new jsPDF("landscape")
      const width = document.internal.pageSize.getWidth()
      const height = document.internal.pageSize.getHeight()
      const widthRatio: number = width / canvas.width
      const heightRatio: number = height / canvas.height
      const ratio: number = widthRatio > heightRatio ? heightRatio : widthRatio

      document.addImage(
        canvas.toDataURL("image/png", 1.0),
        "PNG",
        0,
        0,
        canvas.width * ratio,
        canvas.height * ratio,
      )

      document.save(fileName)
    })
    .catch(onErrorCallback)
    .finally(onSuccessCallback)
}
