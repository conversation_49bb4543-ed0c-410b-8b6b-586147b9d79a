import { getObjectKeys } from "@develop/fe-library/dist/utils"

import { CalculateToggledAmountArgs } from "./calculatesToggledAmountTypes"

export const calculateToggledAmount = ({
  category,
  settings,
  manageView = false,
}: CalculateToggledAmountArgs): number => {
  return (category?.children || [])
    .map((category: any) => {
      const isCategoryNotChecked: boolean = !manageView && !category.isChecked

      if (isCategoryNotChecked) {
        return category.amount
      }

      const isSettingsCategoryNotChecked: boolean =
        !getObjectKeys(settings).length || !settings?.[category.id]

      if (isSettingsCategoryNotChecked) {
        return category.amount
      }

      return 0
    })
    .reduce((sum: number, amount: number) => sum + amount, 0)
}
