import { getIsSkipToBlur, getIsSkipToBlurFreemium } from ".."
import { GetShouldBlurProps } from "./getShouldBlurTypes"

export const getShouldBlur = ({
  categoryId,
  isBasSubscriptionActive,
  isFreemiumBasModelActive,
}: GetShouldBlurProps): boolean => {
  if (isBasSubscriptionActive) {
    return false
  }

  if (isFreemiumBasModelActive) {
    return getIsSkipToBlurFreemium(categoryId)
  }

  return getIsSkipToBlur(categoryId)
}
