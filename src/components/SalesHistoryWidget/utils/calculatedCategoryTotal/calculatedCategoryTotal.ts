import { checkIsArray } from "utils/arrayHelpers"

import { CalculatedCategoryTotalProps } from "./calculatedCategoryTotalTypes"

export const calculatedCategoryTotal = ({
  categories,
  checkedSettings,
}: CalculatedCategoryTotalProps): number => {
  if (!checkIsArray(categories) || !checkedSettings) {
    return 0
  }

  return categories
    .map((category) => {
      return checkedSettings?.[category?.id] ? category?.amount : 0
    })
    .reduce((sum: number, amount: number) => sum + amount, 0)
}
