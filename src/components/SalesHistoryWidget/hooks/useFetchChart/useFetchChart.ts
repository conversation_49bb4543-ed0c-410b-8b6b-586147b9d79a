import { useCallback, useEffect, useRef, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useHistory } from "react-router"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { salesHistoryActions } from "actions/salesHistoryActions"
import userSettingsActions from "actions/userSettingsActions"

import { isMarketplaceGroupsSuccessSelector } from "selectors/marketplaceSelectors"

import { useSellerMarketplaceParams, useUrlParams } from "hooks"

import { getPeriod } from "utils/dateConverter"
import { removeNullAndUndefined } from "utils/objectHelpers"

import { ASYNC_STATUSES } from "constants/async"
import { SALES_CATEGORY_STRATEGIES } from "constants/general"
import { PAGE_VIEW } from "constants/pageView"
import { USER_SETTINGS_KEYS } from "constants/user"

import { SalesHistoryCategoriesSelectionResponse } from "components/SalesHistoryWidget/SalesHistoryWidgetTypes"
import { DashboardFiltersParams } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { AsyncStatus } from "types"
import { SalesHistoryChartRequestParams } from "types/RequestParams"

const { getChartData, updateCheckedCategoriesState } = salesHistoryActions
const { getUserSettings } = userSettingsActions

export const useFetchChart = () => {
  const dispatch = useDispatch()
  const history = useHistory()

  const isMarketplaceGroupsSuccess = useSelector(
    isMarketplaceGroupsSuccessSelector,
  )

  const { urlParams } = useUrlParams<DashboardFiltersParams>()
  const { getSellerMarketplaceParams } = useSellerMarketplaceParams()

  const [chartStatus, setChartStatus] = useState<AsyncStatus>(
    ASYNC_STATUSES.IDLE,
  )

  const fetchChartData = useCallback(() => {
    const { marketplaceSellerIds, marketplace_id, seller_id } =
      getSellerMarketplaceParams(urlParams)

    const requestParams: SalesHistoryChartRequestParams = {
      sellerId: seller_id,
      marketplaceId: marketplace_id,
      marketplaceSellerIds,
      dateStart: urlParams.from as string,
      dateEnd: urlParams.to as string,
      periodType: urlParams.period ?? "day",
      currencyId: urlParams.currency_code,
      isTransactionDateMode:
        urlParams.view === PAGE_VIEW.TRANSACTION ? "1" : "0",
      sellerSku: urlParams.productSku || urlParams.sku,
      asin: urlParams.asin,
      ean: urlParams.ean,
      upc: urlParams.upc,
      isbn: urlParams.isbn,
      brand: urlParams.brand,
      productType: urlParams.product_type,
      stockType: urlParams.stock_type,
      manufacturer: urlParams.manufacturer,
      adultProduct: urlParams.adult_product ? "1" : undefined,
      offerType: urlParams.offer_type,
      tagId: urlParams.tags,
      sales_category_strategy: SALES_CATEGORY_STRATEGIES.custom,
      maxDepth: 1, // get only 1 level of categories
    }

    setChartStatus(ASYNC_STATUSES.PENDING)

    const successCallback = (): void => {
      setChartStatus(ASYNC_STATUSES.FULFILLED)
    }

    const failureCallback = (): void => {
      setChartStatus(ASYNC_STATUSES.IDLE)
    }

    dispatch(
      getUserSettings({ key: USER_SETTINGS_KEYS.salesHistorySettings }, () => {
        dispatch(
          getUserSettings(
            { key: USER_SETTINGS_KEYS.salesHistoryCategoriesSelectionV1 },
            (
              salesHistoryCategoriesSelectionV1: SalesHistoryCategoriesSelectionResponse,
            ) => {
              // Update selected categories state with user settings
              if (salesHistoryCategoriesSelectionV1?.settings?.value) {
                dispatch(
                  updateCheckedCategoriesState(
                    salesHistoryCategoriesSelectionV1.settings.value,
                  ),
                )
              }

              dispatch(
                getChartData(
                  removeNullAndUndefined(requestParams),
                  successCallback,
                  failureCallback,
                ),
              )
            },
          ),
        )
      }),
    )
  }, [dispatch, getSellerMarketplaceParams, urlParams])

  const fromPrev = useRef(urlParams.from)
  const toPrev = useRef(urlParams.to)
  const periodPrev = useRef(urlParams.period)

  useEffect(() => {
    const isReadyToFetch: boolean = [
      urlParams.currency_code,
      urlParams.from,
      urlParams.to,
      urlParams.period,
      isMarketplaceGroupsSuccess,
      chartStatus !== ASYNC_STATUSES.PENDING,
    ].every(Boolean)

    if (!isReadyToFetch) {
      return
    }

    const hasDateRangeChanged: boolean =
      fromPrev.current !== urlParams.from || toPrev.current !== urlParams.to

    if (hasDateRangeChanged) {
      const nextPeriod = getPeriod(urlParams.from, urlParams.to)

      const shouldSkipFetchDataAndChangePeriod: boolean =
        nextPeriod !== urlParams.period

      if (shouldSkipFetchDataAndChangePeriod) {
        const nextUrlParams = getUrlSearchParamsString({
          params: { ...urlParams, period: nextPeriod },
        })

        history.replace({
          ...history.location,
          search: nextUrlParams,
        })

        fromPrev.current = urlParams.from
        toPrev.current = urlParams.to
        periodPrev.current = nextPeriod

        return
      }
    }

    // TODO: Add urlParams keys to watch
    fetchChartData()

    fromPrev.current = urlParams.from
    toPrev.current = urlParams.to
    periodPrev.current = urlParams.period
  }, [
    isMarketplaceGroupsSuccess,
    urlParams.seller_id,
    urlParams.marketplace_id,
    urlParams.offer_type,
    urlParams.productSku,
    urlParams.productSeller,
    urlParams.productMarketplace,
    urlParams.from,
    urlParams.to,
    urlParams.inputMode,
    urlParams.period,
    urlParams.currency_code,
    urlParams.asin,
    urlParams.sku,
    urlParams.ean,
    urlParams.upc,
    urlParams.isbn,
    urlParams.brand,
    urlParams.product_type,
    urlParams.stock_type,
    urlParams.manufacturer,
    urlParams.parent_asin,
    urlParams.adult_product,
    urlParams.view,
    urlParams.tags,
  ])
}
