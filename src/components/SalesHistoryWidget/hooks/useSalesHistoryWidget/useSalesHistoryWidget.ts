import { useCallback, useEffect, useMemo, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useHistory, useLocation } from "react-router"
import { Tab } from "@develop/fe-library"
import {
  getObjectEntries,
  getUrlSearchParams,
  getUrlSearchParamsString,
} from "@develop/fe-library/dist/utils"
import isEmpty from "lodash/isEmpty"
import values from "lodash/values"

import { salesHistoryActions } from "actions/salesHistoryActions"
import userSettingsActions from "actions/userSettingsActions"

import {
  checkedCategoriesStateSelector,
  isChartLoadingSelector,
  salesHistorySelector,
} from "selectors/salesHistorySelectors"
import {
  salesHistoryCategoriesSelectionV1Selector,
  salesHistorySettingsSelector,
} from "selectors/userSettingsSelectors"

import { ModalIdsValues } from "components/SalesHistoryWidget/constants"

import { useSubscription } from "hooks/useSubscription"

import { convertToLocalDate, getPeriod, PeriodTypes } from "utils/dateConverter"
import l from "utils/intl"

import { USER_SETTINGS_KEYS } from "constants/user"

import { useFetchChart } from "../useFetchChart"

import {
  SalesHistoryCategoriesSelection,
  SalesHistoryCategory,
  SalesHistorySettings,
} from "components/SalesHistoryWidget/SalesHistoryWidgetTypes"
import { DashboardFiltersParams } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

const { updateCheckedCategoriesState } = salesHistoryActions

const { saveUserSettings, updateUserSettings } = userSettingsActions

export const useSalesHistoryWidget = () => {
  const dispatch = useDispatch()
  const history = useHistory()
  const { search } = useLocation()

  const [isChartEmpty, setIsChartEmpty] = useState(false)
  const [visibleModalId, setVisibleModalId] = useState<ModalIdsValues | null>(
    null,
  )

  useFetchChart()

  const { isBasSubscriptionActive, isBasSubscriptionExpired } =
    useSubscription()

  const salesHistory = useSelector(salesHistorySelector)
  const checkedCategoriesState = useSelector(checkedCategoriesStateSelector)
  const isChartLoading = useSelector(isChartLoadingSelector)
  const salesHistorySettings: SalesHistorySettings = useSelector(
    salesHistorySettingsSelector,
  )
  const salesHistoryCategoriesSelectionV1 = useSelector(
    salesHistoryCategoriesSelectionV1Selector,
  )

  const { dataSeries, updatedAt } = salesHistory

  const isSalesHistorySettingsEmpty: boolean = isEmpty(salesHistorySettings)

  const urlParams = useMemo(
    () =>
      getUrlSearchParams<DashboardFiltersParams>({
        locationSearch: search,
      }),
    [search],
  )

  const handlePeriodChange = useCallback(
    ({ key = PeriodTypes.Day }: Tab): void => {
      const searchParams = getUrlSearchParams({
        locationSearch: search,
      })

      const nextUrlParams = getUrlSearchParamsString({
        params: { ...searchParams, period: key },
      })

      history.replace({
        ...history.location,
        search: nextUrlParams,
      })
    },
    [history, search],
  )

  const handleModalClose = (): void => {
    setVisibleModalId(null)
  }

  const updateSalesHistorySelection = useCallback(
    (settings: SalesHistoryCategoriesSelection): void => {
      dispatch(updateCheckedCategoriesState(settings))
      const shouldUpdateUserSettings: boolean =
        !!salesHistoryCategoriesSelectionV1?.id &&
        !!salesHistoryCategoriesSelectionV1?.value

      if (shouldUpdateUserSettings) {
        dispatch(
          updateUserSettings(
            {
              id: salesHistoryCategoriesSelectionV1.id,
              settings: {
                ...salesHistoryCategoriesSelectionV1.value,
                ...settings,
              },
            },
            null,
          ),
        )

        return
      }

      dispatch(
        saveUserSettings(
          {
            key: USER_SETTINGS_KEYS.salesHistoryCategoriesSelectionV1,
            settings,
          },
          null,
        ),
      )
    },
    [
      salesHistoryCategoriesSelectionV1?.id,
      salesHistoryCategoriesSelectionV1?.value,
      dispatch,
    ],
  )

  const toggleVisibleCategory = useCallback(
    (isChecked: boolean, category: SalesHistoryCategory): void => {
      updateSalesHistorySelection({
        [category.id]: isChecked,
      })
    },
    [updateSalesHistorySelection],
  )

  const toggleAllCategories = useCallback(
    (isChecked: boolean): void => {
      if (!checkedCategoriesState) {
        return
      }

      const newCategoriesState: SalesHistoryCategoriesSelection =
        Object.fromEntries(
          getObjectEntries(checkedCategoriesState).map(([key]) => [
            key,
            isChecked,
          ]),
        )

      updateSalesHistorySelection(newCategoriesState)
    },
    [checkedCategoriesState, updateSalesHistorySelection],
  )

  useEffect(() => {
    // We should always wait for search params to not override them
    const shouldInitPeriod: boolean =
      !!urlParams?.from && !!urlParams?.to && !urlParams?.period

    if (shouldInitPeriod) {
      const period = getPeriod(urlParams.from, urlParams.to)

      handlePeriodChange({ key: period })
    }
  }, [urlParams?.from, urlParams?.to, urlParams?.period])

  useEffect(() => {
    if (!dataSeries) {
      return
    }

    const hasValues = dataSeries.some(({ salesCategories, units }) => {
      const categories = Object.assign({}, ...values(salesCategories), units)
      const categoryValues: number[] = []

      for (let key in categories) {
        if (salesHistorySettings?.value?.[key]) {
          categoryValues.push(categories[key])
        }
      }

      return categoryValues.some((val) => val !== 0)
    })

    setIsChartEmpty(!hasValues)
  }, [dataSeries, salesHistorySettings])

  const chartTabsItems: Tab[] = [
    {
      key: PeriodTypes.Day,
      contents: "",
      label: l("Day"),
    },
    {
      key: PeriodTypes.Week,
      contents: "",
      label: l("Week"),
    },
    {
      key: PeriodTypes.Month,
      contents: "",
      label: l("Month"),
    },
  ]

  const currentDatePeriod: string =
    urlParams?.from && urlParams?.to
      ? `${convertToLocalDate(urlParams.from)} - ${convertToLocalDate(
          urlParams.to,
        )}`
      : ""

  return {
    visibleModalId,
    isChartEmptyOrSubscriptionExpired: isChartEmpty || isBasSubscriptionExpired,
    urlParams,
    isBasSubscriptionExpired,
    isBasSubscriptionActive,
    dataSeries,
    isChartLoading,
    updatedAt,
    isChartEmpty,
    salesHistorySettings,
    isSalesHistorySettingsEmpty,
    currentDatePeriod,
    chartTabsItems,
    handleModalClose,
    handlePeriodChange,
    setVisibleModalId,
    toggleAllCategories,
    toggleVisibleCategory,
  }
}
