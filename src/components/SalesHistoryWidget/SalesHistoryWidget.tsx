import React from "react"
import { Box, IconPopover, Tabs, Typography } from "@develop/fe-library"

import { convertToLocalDateTime } from "utils/dateConverter"
import l from "utils/intl"

import {
  ChartDropdownMenu,
  DownloadChartModal,
  ExportChartModal,
  ManageViewModal,
  PPCWidgets,
  SalesHistoryChart,
  SalesHistoryLegend,
} from "./components"

import { useSalesHistoryWidget } from "./hooks"

import { MODAL_IDS } from "./constants"

export const SalesHistoryWidget = () => {
  const {
    visibleModalId,
    isChartEmptyOrSubscriptionExpired,
    urlParams,
    isBasSubscriptionExpired,
    isBasSubscriptionActive,
    dataSeries,
    isChartLoading,
    updatedAt,
    isChartEmpty,
    salesHistorySettings,
    isSalesHistorySettingsEmpty,
    currentDatePeriod,
    chartTabsItems,
    handleModalClose,
    handlePeriodChange,
    setVisibleModalId,
    toggleAllCategories,
    toggleVisibleCategory,
  } = useSalesHistoryWidget()

  return (
    <Box
      hasBorder
      dSM={{ flexDirection: "row", minHeight: 500 }}
      flexDirection="column"
    >
      {visibleModalId === MODAL_IDS.ManageView ? (
        <ManageViewModal onCancel={handleModalClose} />
      ) : null}

      {visibleModalId === MODAL_IDS.ExportData ? (
        <ExportChartModal onCancel={handleModalClose} />
      ) : null}

      {visibleModalId === MODAL_IDS.Download ? (
        <DownloadChartModal onCancel={handleModalClose} />
      ) : null}

      <SalesHistoryLegend
        toggleAllCategories={toggleAllCategories}
        toggleVisibleCategory={toggleVisibleCategory}
      />

      <Box
        dSM={{ flexBasis: "75%", hasBorder: { top: false, left: true } }}
        flexBasis="100%"
        flexDirection="column"
        hasBorder={{ top: true }}
        mLG={{ padding: "m l" }}
        padding="m"
      >
        <Box
          align="flex-start"
          flexDirection="column"
          justify="space-between"
          marginBottom="l"
          marginTop="m"
          tb={{ flexDirection: "row", align: "center" }}
        >
          <Typography variant="--font-headline-5">
            {l("Sales history {dateRange}", {
              dateRange: currentDatePeriod,
            })}
          </Typography>

          <Box
            align="center"
            gap="m"
            justify="space-between"
            tb={{ gap: "l", justify: "flex-end", width: "auto" }}
            width="100%"
          >
            {!isChartEmpty ? (
              <Tabs
                activeKey={urlParams.period}
                defaultActiveKey={urlParams.period}
                items={chartTabsItems}
                onChange={handlePeriodChange}
              />
            ) : null}

            <IconPopover
              color="--color-icon-active"
              name="icnInfoCircle"
              placement="top"
              size="--icon-size-3"
              content={`${l("Last update")}: ${
                updatedAt ? convertToLocalDateTime(updatedAt) : ""
              }`}
            />

            <ChartDropdownMenu
              isBasSubscriptionActive={isBasSubscriptionActive}
              isBasSubscriptionExpired={isBasSubscriptionExpired}
              isSalesHistorySettingsEmpty={isSalesHistorySettingsEmpty}
              isChartEmptyOrSubscriptionExpired={
                isChartEmptyOrSubscriptionExpired
              }
              onDropdownItemSelect={setVisibleModalId}
            />
          </Box>
        </Box>

        <SalesHistoryChart
          dataSeries={dataSeries}
          height={600}
          isLoading={isChartLoading}
          minWidth={600}
          salesHistorySettings={salesHistorySettings}
          width="100%"
        />

        <PPCWidgets />
      </Box>
    </Box>
  )
}
