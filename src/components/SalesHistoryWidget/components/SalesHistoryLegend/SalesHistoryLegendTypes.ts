import { SalesHistoryCategory } from "components/SalesHistoryWidget/SalesHistoryWidgetTypes"

export type SalesHistoryLegendProps = {
  toggleVisibleCategory: (
    isChecked: boolean,
    category: SalesHistoryCategory,
  ) => void
  toggleAllCategories: (isChecked: boolean) => void
}

export type LegendSetting = SalesHistoryCategory & {
  parent: string
}

export type SalesHistoryLegendUrlParams = {
  currency_code: string
}
