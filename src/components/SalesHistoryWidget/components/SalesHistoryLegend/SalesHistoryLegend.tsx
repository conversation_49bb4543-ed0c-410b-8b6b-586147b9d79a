import React, { useMemo } from "react"
import { useSelector } from "react-redux"
import { Box, Checkbox } from "@develop/fe-library"
import { getObjectValues } from "@develop/fe-library/dist/utils"

import {
  checkedCategoriesStateSelector,
  checkedSalesCategoriesKeysSelector,
  salesCategoriesSelector,
} from "selectors/salesHistorySelectors"

import { SALES_HISTORY_CHART_CATEGORIES } from "components/SalesHistoryWidget/constants"
import { calculatedCategoryTotal } from "components/SalesHistoryWidget/utils"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import {
  CategoryHeader,
  LegendCategory,
  LegendCategoryType,
} from "./components"

import { SalesHistoryLegendProps } from "./SalesHistoryLegendTypes"

export const SalesHistoryLegend = ({
  toggleVisibleCategory,
  toggleAllCategories,
}: SalesHistoryLegendProps) => {
  const checkedCategoriesState = useSelector(checkedCategoriesStateSelector)
  const checkedSalesCategoriesKeys = useSelector(
    checkedSalesCategoriesKeysSelector,
  )
  const salesCategories = useSelector(salesCategoriesSelector)

  const categoriesValues = useMemo(() => {
    return salesCategories ? getObjectValues(salesCategories) : []
  }, [salesCategories])

  const isAllCategoriesChecked: boolean = useMemo(() => {
    if (!checkedCategoriesState) {
      return false
    }

    return getObjectValues(checkedCategoriesState).every(Boolean)
  }, [checkedCategoriesState])

  const isSomeCategoriesChecked: boolean = useMemo(() => {
    if (!checkedCategoriesState) {
      return false
    }

    return getObjectValues(checkedCategoriesState).some(Boolean)
  }, [checkedCategoriesState])

  const isPartiallyChecked: boolean =
    isSomeCategoriesChecked && !isAllCategoriesChecked

  const handleToggleAll = (): void => {
    toggleAllCategories(!isAllCategoriesChecked)
  }

  const estimatedProfitCalculated: number | null = useMemo(() => {
    if (!checkIsArray(checkedSalesCategoriesKeys)) {
      return null
    }

    return calculatedCategoryTotal({
      categories:
        salesCategories?.[SALES_HISTORY_CHART_CATEGORIES.default]?.children ||
        [],
      checkedSettings: checkedCategoriesState,
    })
  }, [salesCategories, checkedCategoriesState, checkedSalesCategoriesKeys])

  const defaultCategory: LegendCategoryType | null = salesCategories
    ? {
        ...salesCategories[SALES_HISTORY_CHART_CATEGORIES.default],
        isAmountCategoryVisible: false,
        isParentTitleVisible: false,
      }
    : null

  const estimatedProfitCategory: LegendCategoryType | null =
    salesCategories?.[SALES_HISTORY_CHART_CATEGORIES.estimatedProfit] || null

  const revenueCategory: LegendCategoryType | null =
    salesCategories?.[SALES_HISTORY_CHART_CATEGORIES.revenue] || null

  const productsCategory: LegendCategoryType | null = salesCategories?.[
    SALES_HISTORY_CHART_CATEGORIES.units
  ]
    ? {
        ...salesCategories[SALES_HISTORY_CHART_CATEGORIES.units],
        isAmountCategoryVisible: false,
      }
    : null

  return (
    <Box
      dSM={{ flexBasis: "25%" }}
      flexDirection="column"
      minWidth="320px"
      mSM={{ flexBasis: "auto" }}
    >
      {checkIsArray(categoriesValues) ? (
        <Box gap="m" hasBorder={{ bottom: true }} padding="l">
          <Checkbox
            checked={isSomeCategoriesChecked}
            label={isAllCategoriesChecked ? l("Deselect all") : l("Select all")}
            type={isPartiallyChecked ? "selectAll" : "default"}
            onChange={handleToggleAll}
          />
        </Box>
      ) : null}

      {defaultCategory ? (
        <LegendCategory
          category={defaultCategory}
          toggleVisibleCategory={toggleVisibleCategory}
        />
      ) : null}

      <Box
        flexDirection="column"
        gap="m"
        padding="l"
        hasBorder={{
          top: true,
          bottom: true,
        }}
      >
        {revenueCategory ? (
          <CategoryHeader isMoney category={revenueCategory} />
        ) : null}

        {estimatedProfitCategory ? (
          <CategoryHeader
            isMoney
            category={estimatedProfitCategory}
            recalculatedAmount={estimatedProfitCalculated}
          />
        ) : null}
      </Box>

      {productsCategory ? (
        <LegendCategory
          category={productsCategory}
          toggleVisibleCategory={toggleVisibleCategory}
        />
      ) : null}
    </Box>
  )
}
