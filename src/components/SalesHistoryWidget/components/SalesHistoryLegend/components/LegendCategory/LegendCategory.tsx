import React from "react"
import { Box } from "@develop/fe-library"

import { SALES_HISTORY_CHART_CATEGORIES } from "components/SalesHistoryWidget/constants"

import { checkIsArray } from "utils/arrayHelpers"

import { SALES_HISTORY_CATEGORY_VALUE_TYPE } from "../../constants"

import { CategoryHeader } from "../CategoryHeader"
import { LegendCategoryItem } from "../LegendCategoryItem"

import { LegendCategoryProps } from "./LegendCategoryTypes"

export const LegendCategory = ({
  category,
  recalculatedAmount,
  toggleVisibleCategory,
}: LegendCategoryProps) => {
  const {
    id,
    type,
    children: categoryChildren,
    isAmountCategoryVisible = true,
    isParentTitleVisible = true,
  } = category

  return (
    <Box flexDirection="column" gap="m" padding="l">
      {isParentTitleVisible ? (
        <CategoryHeader
          category={category}
          isAmountCategoryVisible={isAmountCategoryVisible}
          isMoney={type === SALES_HISTORY_CATEGORY_VALUE_TYPE.Money}
          recalculatedAmount={recalculatedAmount}
        />
      ) : null}

      <Box flexDirection="column" gap="m">
        {checkIsArray(categoryChildren)
          ? categoryChildren.map((childCategory) => (
              <LegendCategoryItem
                key={childCategory.id}
                category={childCategory}
                isUnits={id === SALES_HISTORY_CHART_CATEGORIES.units}
                toggleVisibleCategory={toggleVisibleCategory}
              />
            ))
          : null}
      </Box>
    </Box>
  )
}
