import { SalesHistoryCategory } from "components/SalesHistoryWidget/SalesHistoryWidgetTypes"

import { SalesHistoryLegendProps } from "../../SalesHistoryLegendTypes"

type PickedProps = Pick<SalesHistoryLegendProps, "toggleVisibleCategory">

export type LegendCategoryType = SalesHistoryCategory & {
  isAmountCategoryVisible?: boolean
  isParentTitleVisible?: boolean
}

export type LegendCategoryProps = PickedProps & {
  category: LegendCategoryType
  recalculatedAmount?: string | number
}
