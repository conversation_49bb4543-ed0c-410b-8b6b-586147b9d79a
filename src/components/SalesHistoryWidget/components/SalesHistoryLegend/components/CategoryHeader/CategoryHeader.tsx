import React from "react"
import { Box, Typography } from "@develop/fe-library"

import { getShouldBlur } from "components/SalesHistoryWidget/utils"
import { RestrictedBlurred } from "components/shared/RestrictedBlurred"

import { useSubscription, useUrlParams } from "hooks"

import l from "utils/intl"
import ln from "utils/localeNumber"

import { NOT_AVAILABLE } from "constants/common"

import { CategoryHeaderProps } from "./CategoryHeaderTypes"
import { SalesHistoryLegendUrlParams } from "../../SalesHistoryLegendTypes"

export const CategoryHeader = ({
  category,
  isAmountCategoryVisible = true,
  recalculatedAmount,
  isMoney,
}: CategoryHeaderProps) => {
  const { urlParams } = useUrlParams<SalesHistoryLegendUrlParams>()

  const { isFreemiumBasModelActive, isBasSubscriptionActive } =
    useSubscription()

  if (!category) {
    return null
  }

  const { id: categoryId, name: categoryName, amount = 0 } = category

  const getCategoryAmount = (amount: string | number): string => {
    if (recalculatedAmount === null) {
      return l(NOT_AVAILABLE)
    }

    return ln(amount, 2, {
      currency: isMoney ? urlParams.currency_code : undefined,
    })
  }

  const shouldBlur = getShouldBlur({
    categoryId,
    isBasSubscriptionActive,
    isFreemiumBasModelActive,
  })

  const categoryAmount: string | number = recalculatedAmount ?? amount

  return (
    <Box align="center" component="header" justify="space-between">
      <Typography variant="--font-headline-5">{l(categoryName)}</Typography>

      {isAmountCategoryVisible ? (
        <Typography variant="--font-headline-5">
          <RestrictedBlurred
            magnitude={100_000}
            shouldBlur={shouldBlur}
            variability={1}
          >
            {({ generatedValue, shouldBlur }) =>
              getCategoryAmount(shouldBlur ? generatedValue : categoryAmount)
            }
          </RestrictedBlurred>
        </Typography>
      ) : null}
    </Box>
  )
}
