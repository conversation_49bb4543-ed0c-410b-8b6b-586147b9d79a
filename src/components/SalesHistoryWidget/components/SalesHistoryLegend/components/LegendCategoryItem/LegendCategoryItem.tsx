import React, { useMemo } from "react"
import { useSelector } from "react-redux"
import {
  Box,
  Checkbox,
  IconPopover,
  InputMode,
  Typography,
} from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"
import {
  getObjectKeys,
  getUrlSearchParamsString,
} from "@develop/fe-library/dist/utils"
import { format } from "date-fns"

import { customerHasConnectedAmazonAdAccountsSelector } from "selectors/mainStateSelectors"
import { checkedCategoriesStateSelector } from "selectors/salesHistorySelectors"
import { salesHistorySettingsSelector } from "selectors/userSettingsSelectors"

import { ORDER_STATUSES_FOR_URL } from "components/KPIWidgets/components/KPIWidgetModern/components/SecondaryMetrics/hooks/useSecondaryMetrics/constants"
import { LegendColorIndicator } from "components/SalesHistoryWidget/components/SalesHistoryChart/components"
import { CATEGORIES } from "components/SalesHistoryWidget/constants"
import Link from "components/shared/Link"
import { RestrictedBlurred } from "components/shared/RestrictedBlurred"

import {
  useGroupAccount,
  useMinStatsDate,
  useSubscription,
  useUrlParams,
} from "hooks"

import { getProductPostedDate } from "utils/getProductPostedDate"
import l from "utils/intl"
import ln from "utils/localeNumber"

import { DATE_FNS_FORMATS } from "constants/dateTime"
import { SALES_HISTORY_BLURRED_CATEGORIES } from "constants/salesHistory"

import {
  SALES_HISTORY_CATEGORY_VALUE_TYPE,
  UNITS_CATEGORIES_ADDITIONAL_URL_PARAMS,
} from "../../constants"

import { SalesHistorySettings } from "components/SalesHistoryWidget/SalesHistoryWidgetTypes"

import {
  DashboardFiltersParams,
  OrdersUrlParams,
  TransactionsUrlParams,
} from "types"

import { LegendCategoryItemProps } from "./LegendCategoryItemTypes"

export const LegendCategoryItem = ({
  isUnits = false,
  category,
  toggleVisibleCategory,
}: LegendCategoryItemProps) => {
  const { id, color_hex: color, name, type, amount } = category

  const { urlParams } = useUrlParams<DashboardFiltersParams>()
  const { isBasSubscriptionActive } = useSubscription()
  const { minStatsDate, today } = useMinStatsDate()
  const { getOldSellerGroupId } = useGroupAccount()

  const checkedCategoriesState = useSelector(checkedCategoriesStateSelector)
  const hasConnectedAmazonAdAccounts: boolean = useSelector(
    customerHasConnectedAmazonAdAccountsSelector,
  )
  const salesHistorySettings: SalesHistorySettings = useSelector(
    salesHistorySettingsSelector,
  )

  const isChecked: boolean = checkedCategoriesState?.[id] || false

  const isDataEmpty: boolean =
    !getObjectKeys(salesHistorySettings.value).length ||
    !salesHistorySettings.value?.[id]

  const baseLinkUrlParams: TransactionsUrlParams | OrdersUrlParams =
    useMemo(() => {
      return {
        ...getOldSellerGroupId(urlParams.productSeller || urlParams.seller_id),
        from: urlParams.from,
        to: urlParams.to,
        currency_code: urlParams.currency_code,
        period: urlParams.period,
        inputMode: urlParams.inputMode,
        seller_sku: urlParams.productSku || urlParams.sku,
        marketplaces: urlParams.marketplace_id,
        asin: urlParams.productASIN || urlParams.asin,
        product_brand: urlParams.brand,
        product_type: urlParams.product_type,
        product_manufacturer: urlParams.manufacturer,
        product_stock_type: urlParams.stock_type,
        offer_type: urlParams.offer_type,
      }
    }, [
      getOldSellerGroupId,
      urlParams.productSeller,
      urlParams.seller_id,
      urlParams.from,
      urlParams.to,
      urlParams.currency_code,
      urlParams.period,
      urlParams.inputMode,
      urlParams.productSku,
      urlParams.sku,
      urlParams.marketplace_id,
      urlParams.productASIN,
      urlParams.asin,
      urlParams.brand,
      urlParams.product_type,
      urlParams.manufacturer,
      urlParams.stock_type,
      urlParams.offer_type,
    ])

  const transactionLink: string = useMemo(() => {
    const posted_date = getProductPostedDate({
      from: urlParams.from,
      to: urlParams.to,
    })

    const dateRange: TransactionsUrlParams =
      urlParams.view === "order"
        ? {
            from: format(minStatsDate, DATE_FNS_FORMATS.SERVER),
            to: format(today, DATE_FNS_FORMATS.SERVER),
            inputMode: "allTime" as InputMode,
            posted_date,
          }
        : {
            from: urlParams.from,
            to: urlParams.to,
            inputMode: urlParams.inputMode,
          }

    const searchParams = getUrlSearchParamsString({
      params: {
        ...baseLinkUrlParams,
        ...dateRange,
        sales_category_strategy: urlParams.sales_category_strategy,
        sales_category_depth_1: [id],
      },
    })

    return `${ROUTES.BAS_ROUTES.PATH_BAS_TRANSACTIONS}${searchParams}`
  }, [
    urlParams.from,
    urlParams.to,
    urlParams.view,
    urlParams.inputMode,
    urlParams.sales_category_strategy,
    minStatsDate,
    today,
    baseLinkUrlParams,
    id,
  ])

  const orderLink: string = useMemo(() => {
    const searchParams = getUrlSearchParamsString({
      params: {
        ...baseLinkUrlParams,
        sort: "-order_purchase_date",
        order_status: ORDER_STATUSES_FOR_URL,
        ...(UNITS_CATEGORIES_ADDITIONAL_URL_PARAMS?.[id] || {}),
      },
    })

    return `${ROUTES.BAS_ROUTES.PATH_BAS_ORDERS}${searchParams}`
  }, [baseLinkUrlParams, id])

  if (isDataEmpty) {
    return null
  }

  const isCategoryValueTypeMoney: boolean =
    type === SALES_HISTORY_CATEGORY_VALUE_TYPE.Money

  const shouldShowAmazonAdsAccountHint: boolean =
    isBasSubscriptionActive &&
    id === CATEGORIES.ppc_1 &&
    !hasConnectedAmazonAdAccounts

  const getCategoryValue = (amount: string | number): string =>
    isCategoryValueTypeMoney
      ? ln(amount, 2, {
          currency: urlParams.currency_code,
        })
      : ln(amount, 0)

  const handleToggleCategory = (isChecked: boolean): void => {
    toggleVisibleCategory(isChecked, category)
  }

  const handleClickCategoryName = (): void => {
    toggleVisibleCategory(!isChecked, category)
  }

  const shouldBlur: boolean = isBasSubscriptionActive
    ? false
    : !SALES_HISTORY_BLURRED_CATEGORIES.includes(id)

  return (
    <Box
      key={id}
      align="center"
      color={!isChecked ? "--color-text-disable" : undefined}
      flexDirection="row"
      gap="m"
      justify="space-between"
    >
      <Box align="center" gap="m">
        <Checkbox checked={isChecked} onChange={handleToggleCategory} />

        <LegendColorIndicator
          color={color}
          isChecked={isChecked}
          variant={isUnits ? "unit" : "category"}
        />

        <Typography
          color={!isChecked ? "--color-text-disable" : "--color-text-main"}
          variant="--font-body-text-7"
          whiteSpace={shouldShowAmazonAdsAccountHint ? "nowrap" : "normal"}
          onClick={handleClickCategoryName}
        >
          {l(name)}
        </Typography>
      </Box>

      <Typography
        color={!isChecked ? "--color-text-disable" : "--color-text-second"}
        textAlign="right"
        variant="--font-body-text-7"
        whiteSpace={shouldShowAmazonAdsAccountHint ? "normal" : "nowrap"}
      >
        {!shouldShowAmazonAdsAccountHint ? (
          <RestrictedBlurred
            magnitude={10_000}
            shouldBlur={shouldBlur}
            variability={2}
          >
            {({ generatedValue, shouldBlur }) => {
              if (shouldBlur) {
                return getCategoryValue(generatedValue)
              }

              if (!isChecked) {
                return getCategoryValue(amount)
              }

              return (
                <Link
                  internal
                  styleType="primary"
                  target="_blank"
                  text={getCategoryValue(amount)}
                  type="span"
                  url={isUnits ? orderLink : transactionLink}
                  variant="text"
                />
              )
            }}
          </RestrictedBlurred>
        ) : (
          <Box align="center" component="span" gap="s">
            <Link
              internal
              styleType="primary"
              text={l("Amazon Ads account")}
              type="span"
              url={ROUTES.GENERAL_ROUTES.PATH_AMAZON_ADS_ACCOUNTS}
              variant="text"
            />

            <IconPopover
              color="--color-icon-warning"
              name="icnWarning"
              size="--icon-size-3"
              content={l(
                "Connect Amazon Ads account to see a detailed Ads (PPC) breakdown.",
              )}
            />
          </Box>
        )}
      </Typography>
    </Box>
  )
}
