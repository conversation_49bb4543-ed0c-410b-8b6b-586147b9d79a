import { getObjectKeys } from "@develop/fe-library/dist/utils"

export const sumColumns = <Category extends object, Settings>(
  category: Category,
  settings: Settings,
): number => {
  const values = getObjectKeys(category)
    .map((categoryId) => {
      return settings[categoryId] ? category[categoryId] : 0
    })
    .filter((value) => !!value)

  return values.reduce((sum, next) => sum + next, 0)
}
