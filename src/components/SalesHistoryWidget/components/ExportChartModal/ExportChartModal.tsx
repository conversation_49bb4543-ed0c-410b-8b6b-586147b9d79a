import React, { useState } from "react"
import { useSelector } from "react-redux"
import { Box, Modal, Select, Typography } from "@develop/fe-library"
import { getObjectKeys, getObjectValues } from "@develop/fe-library/dist/utils"
import XLSX from "xlsx"

import { salesHistorySelector } from "selectors/salesHistorySelectors"
import { salesHistorySettingsSelector } from "selectors/userSettingsSelectors"

import { useUrlParams } from "hooks"

import l from "utils/intl"

import { sumColumns } from "./utils"

import { EXPORT_CHART_AVAILABLE_FORMATS } from "./constants"

import {
  ChartUrlParams,
  SalesHistorySettings,
} from "components/SalesHistoryWidget/SalesHistoryWidgetTypes"

import { ExportChartModalProps } from "./ExportChartModalTypes"

export const ExportChartModal = ({ onCancel }: ExportChartModalProps) => {
  const { urlParams } = useUrlParams<ChartUrlParams>()

  const [format, setFormat] = useState(
    EXPORT_CHART_AVAILABLE_FORMATS.XLSX.value,
  )

  const salesHistorySettings: SalesHistorySettings = useSelector(
    salesHistorySettingsSelector,
  )
  const { dataSeries } = useSelector(salesHistorySelector)

  const { value: settings, columns: settingColumns } =
    salesHistorySettings ?? {}

  const handleFormatChange = ({ value }): void => {
    setFormat(value)
  }

  const exportSheet = (): void => {
    if (!dataSeries || !settings) {
      return
    }

    const fileName = `${l("sales")}_${l("history")}_${urlParams.from}_${
      urlParams.to
    }.${format}`

    const jsonSheet = dataSeries.map(({ date, salesCategories, units }) => {
      const columns: Record<string, string | number> = {
        Day: date,
      }
      const revenueTotal = sumColumns(salesCategories.revenue, settings)
      const expensesTotal = sumColumns(salesCategories.expenses, settings)

      getObjectKeys(salesCategories.revenue).forEach((name) => {
        columns[name] = salesCategories.revenue[name]
      })

      columns["Revenue"] = revenueTotal

      getObjectKeys(salesCategories.expenses).forEach((name) => {
        columns[name] = -salesCategories.expenses[name]
      })

      columns["Expenses"] = -expensesTotal
      columns["Estimated margin"] = revenueTotal - expensesTotal

      getObjectKeys(units).forEach((name) => {
        columns[name] = units[name]
      })

      getObjectKeys(settings).forEach((c) => {
        if (!settings[c]) delete columns[c]
      })

      return columns
    })

    // Convert json to array of arrays, because you can't have duplicated keys in json, it's necessary for headers, we have same titles
    const arrayOfArrays: (string | number)[][] = [
      getObjectKeys(jsonSheet[0]).map((title) =>
        l(settingColumns?.[title] || title),
      ),
    ]

    jsonSheet.forEach((row) => {
      const nestedRow = getObjectValues(row)

      arrayOfArrays.push(nestedRow)
    })

    const workSheet = XLSX.utils.aoa_to_sheet(arrayOfArrays)
    const workBook = XLSX.utils.book_new()

    XLSX.utils.book_append_sheet(workBook, workSheet, "Table")
    XLSX.writeFile(workBook, fileName)

    onCancel?.()
  }

  return (
    <Modal
      visible
      cancelButtonText={l("Cancel")}
      okButtonText={l("Export")}
      title={l("Export data")}
      onCancel={onCancel}
      onClose={onCancel}
      onOk={exportSheet}
    >
      <Box flexDirection="column" gap="l">
        <Typography color="--color-text-main" variant="--font-body-text-7">
          {l("Choose how you want to export your Sales History data")}
        </Typography>

        <Select
          defaultValue={format}
          options={[
            EXPORT_CHART_AVAILABLE_FORMATS.XLSX,
            EXPORT_CHART_AVAILABLE_FORMATS.CSV,
          ]}
          onChange={handleFormatChange}
        />
      </Box>
    </Modal>
  )
}
