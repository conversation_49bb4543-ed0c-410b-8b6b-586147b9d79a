import l from "utils/intl"

import { TICK_COUNT_Y } from "constants/chart"

import { formatAxisValue } from "../formatAxisValue"

import { FormatAxisProps } from "./formatAxisTypes"

export const formatYAxis = ({
  value,
  index,
  axisPosition = "left",
  maxLeftNumber = 0,
  maxRightNumber = 0,
  locale,
  currencyCode = null,
  onMaxLeftNumberChange,
  onMaxRightNumberChange,
}: FormatAxisProps): string => {
  const isLeftAxisPosition: boolean = axisPosition === "left"
  const isLabelMoreMaxLeftNumber: boolean = value > maxLeftNumber && value > 0
  const isLabelMoreMaxRightNumber: boolean = value > maxRightNumber && value > 0
  const isSettingsMaxLeftNumber: boolean =
    isLabelMoreMaxLeftNumber && isLeftAxisPosition
  const isSettingsMaxRightNumber: boolean =
    isLabelMoreMaxRightNumber && axisPosition === "right"

  if (isSettingsMaxLeftNumber) {
    onMaxLeftNumberChange?.(value)
  }

  if (isSettingsMaxRightNumber) {
    onMaxRightNumberChange?.(value)
  }

  const axisLabelTitle: string =
    isLeftAxisPosition && currencyCode ? currencyCode : l("_Number_")
  const shouldFormatValue: boolean = index + 1 !== TICK_COUNT_Y
  const formattedValue: string = shouldFormatValue
    ? formatAxisValue({ value, locale })
    : axisLabelTitle

  return `${formattedValue}`
}
