import { useMemo, useState } from "react"
import { useSelector } from "react-redux"
import {
  checkIsArray,
  getObjectEntries,
  getObjectKeys,
} from "@develop/fe-library/dist/utils"

import { translationsSelector } from "selectors/mainStateSelectors"
import {
  checkedCategoriesStateSelector,
  salesCategoriesSelector,
  salesHistorySelector,
} from "selectors/salesHistorySelectors"

import { SALES_HISTORY_CHART_CATEGORIES } from "components/SalesHistoryWidget/constants"

import { useUrlParams } from "hooks"

import { Y_AXIS_WIDTHS } from "constants/chart"

import { formatYAxis } from "../../utils"

import { LegendSetting } from "components/SalesHistoryWidget/components/SalesHistoryLegend/SalesHistoryLegendTypes"
import { SalesHistoryCategory } from "components/SalesHistoryWidget/SalesHistoryWidgetTypes"
import { DashboardFiltersParams } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { UseSalesHistoryChartProps } from "./useSalesHistoryChartTypes"
import { AxisWidthsType, TickFormatterType } from "../../SalesHistoryChartTypes"

export const useSalesHistoryChart = ({
  salesHistorySettings,
  dataSeries,
}: UseSalesHistoryChartProps) => {
  const { urlParams } = useUrlParams<DashboardFiltersParams>()
  const [maxLeftNumber, setMaxLeftNumber] = useState(0)
  const [maxRightNumber, setMaxRightNumber] = useState(0)

  const { dataSeriesWithAbsoluteValues } = useSelector(salesHistorySelector)
  const { locale } = useSelector(translationsSelector)
  const salesCategories = useSelector(salesCategoriesSelector)
  const checkedCategoriesState = useSelector(checkedCategoriesStateSelector)

  const countWidth = (maxNumber: number): number => {
    const result =
      Y_AXIS_WIDTHS[String(Math.abs(maxNumber)).length as AxisWidthsType]

    return result ? result + 5 : 45
  }

  const formatLeftYAxis: TickFormatterType = (value, index): string =>
    formatYAxis({
      locale,
      maxLeftNumber,
      maxRightNumber,
      value,
      index,
      onMaxLeftNumberChange: setMaxLeftNumber,
      onMaxRightNumberChange: setMaxRightNumber,
    })

  const formatRightYAxis: TickFormatterType = (value, index): string =>
    formatYAxis({
      locale,
      maxLeftNumber,
      maxRightNumber,
      value,
      index,
      onMaxLeftNumberChange: setMaxLeftNumber,
      onMaxRightNumberChange: setMaxRightNumber,
      axisPosition: "right",
    })

  const isChartEmpty: boolean = useMemo(() => {
    if (!checkIsArray(dataSeries)) {
      return true
    }

    const hasValues = dataSeries.some((series) => {
      const { salesCategories, units } = series
      const categories = Object.assign({}, salesCategories, units)

      return getObjectKeys(categories).some((key) => {
        if (salesHistorySettings?.value?.[key]) {
          return categories[key] !== 0
        }

        return false
      })
    })

    return !hasValues
  }, [dataSeries, salesHistorySettings])

  const legendSettings: LegendSetting[] | null = useMemo(() => {
    if (!salesCategories) {
      return null
    }

    const newLegendSettings = getObjectEntries(salesCategories).reduce(
      (legendSettingsResult: LegendSetting[], [key, category]) => {
        if (!checkIsArray(category.children)) {
          return legendSettingsResult
        }

        category.children.forEach((item) => {
          const shouldDisplayLegend: boolean =
            !!checkedCategoriesState?.[item.id] &&
            !!salesHistorySettings.value?.[item.id]

          if (shouldDisplayLegend) {
            legendSettingsResult.push({
              ...item,
              parent: key,
              children: item.children || [],
              hasChildren: item.hasChildren ?? false,
            })
          }
        })

        return legendSettingsResult
      },
      [],
    )

    return newLegendSettings
  }, [salesCategories, checkedCategoriesState, salesHistorySettings])

  const tooltipCategories: SalesHistoryCategory[] = salesCategories
    ? [
        salesCategories?.[SALES_HISTORY_CHART_CATEGORIES.default],
        salesCategories?.[SALES_HISTORY_CHART_CATEGORIES.revenue],
        salesCategories?.[SALES_HISTORY_CHART_CATEGORIES.estimatedProfit],
        salesCategories?.[SALES_HISTORY_CHART_CATEGORIES.units],
      ]
    : []

  const isChartDataAvailable: boolean = !isChartEmpty && !!legendSettings

  return {
    isChartEmpty,
    formatLeftYAxis,
    countWidth,
    maxLeftNumber,
    maxRightNumber,
    tooltipCategories,
    locale,
    urlParams,
    formatRightYAxis,
    isChartDataAvailable,
    legendSettings,
    dataSeriesWithAbsoluteValues,
  }
}
