import React from "react"
import withSizes from "react-sizes"
import { <PERSON>, Spinner, Typography } from "@develop/fe-library"
import cn from "classnames"
import {
  Bar,
  Brush,
  CartesianGrid,
  ComposedChart,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
} from "recharts"

import l from "utils/intl"

import { TICK_COUNT_Y } from "constants/chart"
import { COMPANY_NAME } from "constants/general"

import { ChartTooltip } from "./components"

import { useSalesHistoryChart } from "./hooks"

import { SALES_HISTORY_CHART_CATEGORIES } from "../../constants"

import { CustomTick } from "./renders"

import { SalesHistoryChartProps } from "./SalesHistoryChartTypes"

import styles from "./salesHistoryChart.module.scss"

const SalesHistoryChart = ({
  width,
  height,
  minWidth,
  resizeKey,
  isLoading,
  salesHistorySettings,
  dataSeries,
}: SalesHistoryChartProps) => {
  const {
    isChartEmpty,
    formatLeftY<PERSON><PERSON>s,
    countWidth,
    maxLeftNumber,
    locale,
    urlParams,
    formatRightYAxis,
    isChartDataAvailable,
    legendSettings,
    maxRightNumber,
    tooltipCategories,
    dataSeriesWithAbsoluteValues,
  } = useSalesHistoryChart({ salesHistorySettings, dataSeries })

  return (
    <Box display="block" overflow="visible" position="relative">
      <Box
        data-selector-id="chart"
        overflow="auto"
        tb={{ overflow: "visible" }}
        width="100%"
      >
        {isLoading ? (
          <Box
            align="center"
            flexDirection="column"
            height="100%"
            justify="center"
            position="absolute"
            width="100%"
            zIndex={2}
          >
            <Spinner size="lg" />
          </Box>
        ) : null}

        {!isLoading && isChartEmpty ? (
          <Box
            align="center"
            flexDirection="column"
            gap="s"
            height="100%"
            justify="center"
            position="absolute"
            width="100%"
            zIndex={2}
          >
            <Typography color="--color-text-main" variant="--font-headline-3">
              {l("No data found for the selected period")}
            </Typography>

            <Typography
              color="--color-text-second"
              variant="--font-body-text-4"
            >
              {l(
                "Please select another period, another marketplace, or wait for {companyName} to synchronize with Amazon.",
                { companyName: COMPANY_NAME },
              )}
            </Typography>
          </Box>
        ) : null}

        <ResponsiveContainer
          key={resizeKey}
          height={height}
          minWidth={minWidth}
          width={width}
          className={cn(
            styles.chart,
            "chart-with-watermark",
            {
              [styles.emptyStateChart]: isChartEmpty,
            },
            styles.basChartWithWatermark,
          )}
        >
          <ComposedChart
            barCategoryGap="20%"
            barGap={0}
            data={dataSeriesWithAbsoluteValues}
          >
            <CartesianGrid horizontal={false} />
            {!isChartEmpty ? (
              <>
                <XAxis
                  axisLine={false}
                  dataKey="date"
                  height={50}
                  interval="preserveStartEnd"
                  stroke="var(--color-text-placeholders)"
                  tickLine={false}
                  xAxisId={0}
                />

                <XAxis
                  hide
                  axisLine={false}
                  dataKey="date"
                  height={50}
                  stroke="var(--color-text-placeholders)"
                  tickLine={false}
                  xAxisId={1}
                />

                <YAxis
                  allowDecimals={false}
                  axisLine={false}
                  orientation="left"
                  stroke="var(--color-text-second)"
                  tickCount={TICK_COUNT_Y}
                  tickFormatter={formatLeftYAxis}
                  tickLine={false}
                  type="number"
                  width={countWidth(maxLeftNumber)}
                  tick={CustomTick({
                    locale,
                    currencyCode: urlParams?.currency_code,
                  })}
                />

                <YAxis
                  allowDecimals={false}
                  axisLine={false}
                  orientation="right"
                  stroke="var(--color-text-second)"
                  tickCount={TICK_COUNT_Y}
                  tickFormatter={formatRightYAxis}
                  tickLine={false}
                  type="number"
                  width={countWidth(maxRightNumber)}
                  yAxisId="left"
                  tick={CustomTick({
                    locale,
                    currencyCode: urlParams?.currency_code,
                  })}
                />
              </>
            ) : null}

            {isChartDataAvailable
              ? legendSettings?.map(({ id, parent, name, color_hex }) => {
                  if (parent === SALES_HISTORY_CHART_CATEGORIES.units) {
                    return (
                      <Line
                        key={id}
                        dataKey={`${parent}.${id}`}
                        name={name}
                        stroke={color_hex || "var(--color-text-link)"}
                        type="monotone"
                        yAxisId="left"
                      />
                    )
                  }

                  return (
                    <Bar
                      key={id}
                      barSize={20}
                      dataKey={`salesCategories.${id}`}
                      fill={color_hex || "var(--color-text-link)"}
                      name={name}
                      stackId={parent}
                      xAxisId={0}
                    />
                  )
                })
              : null}

            <Tooltip
              wrapperStyle={{ zIndex: 10 }}
              content={
                <ChartTooltip
                  categories={tooltipCategories}
                  dataSeries={dataSeries}
                />
              }
            />

            <Brush dataKey="date" height={30} />
          </ComposedChart>
        </ResponsiveContainer>
      </Box>
    </Box>
  )
}

const mapSizesToProps = ({ width }: { width: number }) => ({
  resizeKey: `chart-${width}`,
})

const SalesHistoryChartWithSizes = withSizes(mapSizesToProps)(SalesHistoryChart)

export { SalesHistoryChartWithSizes as SalesHistoryChart }
