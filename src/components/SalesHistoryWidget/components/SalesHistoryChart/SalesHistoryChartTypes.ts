import { XAxisProps } from "recharts"

import { Y_AXIS_WIDTHS } from "constants/chart"

import {
  SalesHistoryDataItem,
  SalesHistoryDataItemExtended,
  SalesHistorySettings,
} from "components/SalesHistoryWidget/SalesHistoryWidgetTypes"

import { ChartTooltipCategory } from "./components/ChartTooltip/ChartTooltipTypes"

export type AxisWidthsType = keyof typeof Y_AXIS_WIDTHS

export type ChartLegendType = {
  id: string
  parent: string
  color: string
  name: string
}

export type CategoryBaseType = {
  amount?: number
  color?: string
  color_hex?: string
  currency?: string | number
  depth?: number
  hasChildren?: boolean
  id?: string
  name?: string
  type?: string
  value?: number
}

export type CategoryType = ChartTooltipCategory & {
  children: CategoryBaseType[]
  hasChildren: boolean
  amount: number
  date: string
  dateRange?: string
}

export type SalesHistoryChartProps = {
  resizeKey: string
  minWidth?: number
  period: string
  width?: number
  height?: number
  isLoading?: boolean
  salesHistorySettings: SalesHistorySettings
  isBasModuleStarted: boolean
  dataSeries: SalesHistoryDataItem[]
}

export type PayloadCategory = {
  id?: string
  name?: string
  dataKey?: string
  dateRange?: string
  value: number
  active?: boolean
  payload?: SalesHistoryDataItemExtended
  color?: string
}

export type ResultsByCategory = Record<string, number | null>

export type TotalsByCategory = Record<string, number[]>

export type TickFormatterType = XAxisProps["tickFormatter"]
