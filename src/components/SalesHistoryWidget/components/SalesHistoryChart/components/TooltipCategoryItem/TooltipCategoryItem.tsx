import React from "react"
import { Box, Typography } from "@develop/fe-library"

import {
  CATEGORIES,
  SALES_HISTORY_CHART_CATEGORIES,
  SALES_HISTORY_TOOLTIP_CATEGORIES,
} from "components/SalesHistoryWidget/constants"
import { getShouldBlur } from "components/SalesHistoryWidget/utils"
import { RestrictedBlurred } from "components/shared/RestrictedBlurred"

import { useSubscription, useUrlParams } from "hooks"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"
import ln from "utils/localeNumber"

import { NOT_AVAILABLE } from "constants/common"

import { TooltipSubCategoryItem } from "../TooltipSubCategoryItem"

import { DashboardFiltersMainFilterParams } from "types/UrlParams"

import { TooltipCategoryItemProps } from "./TooltipCategoryItemTypes"

export const TooltipCategoryItem = ({
  category,
  categoriesTotals,
  payload,
}: TooltipCategoryItemProps) => {
  const { id: categoryId, amount } = category
  const { isBasSubscriptionActive, isFreemiumBasModelActive } =
    useSubscription()
  const { urlParams } = useUrlParams<DashboardFiltersMainFilterParams>()

  const isEstimatedProfit: boolean = categoryId === CATEGORIES.estimated_profit
  const isRevenue: boolean = categoryId === CATEGORIES.revenue
  const isUnits: boolean = categoryId === SALES_HISTORY_CHART_CATEGORIES.units
  const isDefaultCategory: boolean = categoryId === null

  const categoryAmount: number | null = isRevenue
    ? categoriesTotals[categoryId]
    : amount

  const categoryTotal: number | null = isEstimatedProfit
    ? categoriesTotals[SALES_HISTORY_TOOLTIP_CATEGORIES.salesCategories]
    : categoryAmount

  const isValueVisible: boolean = !(
    isNaN(amount) ||
    isUnits ||
    isDefaultCategory
  )

  const shouldBlur = getShouldBlur({
    categoryId,
    isBasSubscriptionActive,
    isFreemiumBasModelActive,
  })

  return (
    <Box flexDirection="column">
      <Box align="center" justify="space-between">
        {isDefaultCategory ? null : (
          <Typography color="--color-text-main" variant="--font-body-text-5">
            {l(category.name)}
          </Typography>
        )}

        {isValueVisible ? (
          <Typography color="--color-text-main" variant="--font-body-text-5">
            <RestrictedBlurred
              magnitude={100_000}
              shouldBlur={shouldBlur}
              variability={1}
            >
              {({ generatedValue, shouldBlur }) =>
                categoryTotal !== null
                  ? ln(shouldBlur ? generatedValue : categoryTotal, 2, {
                      currency: urlParams?.currency_code,
                    })
                  : l(NOT_AVAILABLE)
              }
            </RestrictedBlurred>
          </Typography>
        ) : null}
      </Box>

      <Box flexDirection="column" gap="s">
        {checkIsArray(category.children)
          ? category.children.map((subCategory, index) => {
              return (
                <TooltipSubCategoryItem
                  // eslint-disable-next-line react/no-array-index-key
                  key={`${category.id}${index}`}
                  isUnits={isUnits}
                  payload={payload}
                  subcategory={subCategory}
                />
              )
            })
          : null}
      </Box>
    </Box>
  )
}
