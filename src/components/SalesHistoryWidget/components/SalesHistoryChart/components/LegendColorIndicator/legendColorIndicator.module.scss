.unitIndicator {
  position: relative;
  width: var(--margin-s);
  height: var(--margin-s);
  border-radius: calc(var(--margin-s) / 2);
  min-width: var(--margin-s);

  &::before,
  &::after {
    content: "";
    display: block;
    position: absolute;
    width: 3px;
    height: 1px;
    top: calc(50% - 0.5px);
    background-color: inherit;
  }

  &::after {
    left: -3px;
  }

  &::before {
    right: -3px;
  }
}
