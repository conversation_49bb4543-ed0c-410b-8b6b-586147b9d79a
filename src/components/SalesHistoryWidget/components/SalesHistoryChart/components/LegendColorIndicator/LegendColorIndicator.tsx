import React from "react"
import { Box } from "@develop/fe-library"
import cn from "classnames"

import { CATEGORY_COLORS } from "components/SalesHistoryWidget/constants"

import { LegendColorIndicatorProps } from "./LegendColorIndicatorTypes"

import styles from "./legendColorIndicator.module.scss"

export const LegendColorIndicator = ({
  color,
  isChecked = true,
  variant = "category",
}: LegendColorIndicatorProps) => {
  const isVariantCategory: boolean = variant === "category"
  const isVariantUnit: boolean = variant === "unit"
  const backgroundColor: string | null | undefined = isChecked
    ? color
    : CATEGORY_COLORS.fallback

  return (
    <Box
      // @ts-expect-error - backgroundColor expects the type of Colors, but we use custom colors
      backgroundColor={backgroundColor}
      borderRadius="--border-radius"
      component="span"
      height={8}
      minWidth={8}
      width={8}
      className={cn({
        [styles.unitIndicator]: isVariantUnit,
        [styles.categoryIndicator]: isVariantCategory,
      })}
    />
  )
}
