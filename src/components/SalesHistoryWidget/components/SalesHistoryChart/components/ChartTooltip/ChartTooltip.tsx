import React, { useMemo } from "react"
import { useSelector } from "react-redux"
import { Box, Typography } from "@develop/fe-library"
import { getObjectValues } from "@develop/fe-library/dist/utils"

import { checkedSalesCategoriesKeysSelector } from "selectors/salesHistorySelectors"

import { getValueByDataKey } from "components/SalesHistoryWidget/utils"

import { useSubscription, useUrlParams } from "hooks"

import { checkIsArray } from "utils/arrayHelpers"
import { PeriodTypes } from "utils/dateConverter"

import { TooltipCategoryItem } from "../TooltipCategoryItem"

import { DashboardFiltersMainFilterParams } from "types/UrlParams"

import { ChartTooltipCategory, ChartTooltipProps } from "./ChartTooltipTypes"
import {
  PayloadCategory,
  ResultsByCategory,
} from "../../SalesHistoryChartTypes"

export const ChartTooltip = ({
  active, // Comes from recharts
  payload, // Comes from recharts
  label, // Comes from recharts
  categories,
  dataSeries,
}: ChartTooltipProps) => {
  const checkedSalesCategoriesKeys = useSelector(
    checkedSalesCategoriesKeysSelector,
  )

  const formattedPayload: PayloadCategory[] | undefined = useMemo(() => {
    if (!payload?.length) {
      return
    }

    return payload.map((item) => {
      const categoryPayload = dataSeries?.find(
        ({ date }) => date === item.payload?.date,
      )

      return {
        ...item,
        payload: categoryPayload,
        value: item.dataKey
          ? getValueByDataKey({ data: categoryPayload, dataKey: item.dataKey })
          : 0,
      }
    })
  }, [dataSeries, payload])

  const { isBasSubscriptionExpired } = useSubscription()
  const { urlParams } = useUrlParams<DashboardFiltersMainFilterParams>()

  const getParentKey = (category: ChartTooltipCategory): string => {
    return category.dataKey?.split(".")?.at(0) || ""
  }

  const getCalculatedChildrenTotals = useMemo((): any => {
    const result: ResultsByCategory = {}

    getObjectValues(formattedPayload).forEach((formattedPayloadCategory) => {
      const parentKey = getParentKey(formattedPayloadCategory)

      if (result[parentKey]) {
        result[parentKey] += formattedPayloadCategory.value
      } else {
        result[parentKey] = formattedPayloadCategory.value
      }
    })

    // TODO: Refactor to not bound to isBasSubscriptionExpired directly
    // Correct revenue and estimated profit (margin)
    result.salesCategories = isBasSubscriptionExpired
      ? payload?.[0]?.payload?.expenses || 0
      : result.salesCategories

    result.salesCategories = checkIsArray(checkedSalesCategoriesKeys)
      ? result.salesCategories
      : null

    // @ts-expect-error
    result.revenue = payload?.[0]?.payload?.revenue

    return result
  }, [
    checkedSalesCategoriesKeys,
    formattedPayload,
    isBasSubscriptionExpired,
    payload,
  ])

  if (!active) {
    return null
  }

  const dateRange: string | undefined =
    formattedPayload?.[0]?.payload?.dateRange || label

  return (
    <Box
      backgroundColor="--color-main-background"
      borderRadius="--border-radius"
      boxShadow="--box-shadow-modal"
      display="block"
      gap="l"
      padding="l"
      width={295}
    >
      <Typography color="--color-text-main" variant="--font-body-text-2">
        {urlParams.period === PeriodTypes.Day ? label : dateRange}
      </Typography>

      <Box flexDirection="column" gap="m" tb={{ gap: "l" }}>
        {checkIsArray(categories)
          ? categories.map((category) => (
              <TooltipCategoryItem
                key={category?.id}
                categoriesTotals={getCalculatedChildrenTotals}
                category={category}
                payload={formattedPayload}
              />
            ))
          : null}
      </Box>
    </Box>
  )
}
