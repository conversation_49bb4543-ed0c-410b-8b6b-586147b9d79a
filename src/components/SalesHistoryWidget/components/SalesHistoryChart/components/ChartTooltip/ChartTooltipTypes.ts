import {
  SalesHistoryCategory,
  SalesHistoryDataItem,
} from "components/SalesHistoryWidget/SalesHistoryWidgetTypes"

import { PayloadCategory } from "../../SalesHistoryChartTypes"

export type ChartTooltipCategory = {
  id?: string
  name?: string
  dataKey?: string
  dateRange?: string
  value: number
  active?: boolean
  payload?: SalesHistoryDataItem
  color?: string
}

export type ChartTooltipProps = {
  active?: boolean
  payload?: PayloadCategory[] | null
  label?: string
  categories: SalesHistoryCategory[]
  dataSeries?: SalesHistoryDataItem[]
}
