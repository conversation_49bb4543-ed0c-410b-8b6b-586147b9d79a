@import "assets/styles/variables";

.emptyStateChart {
  height: 100%;
  min-width: 100% !important;
  overflow: hidden;

  ::-webkit-scrollbar {
    display: none;
  }
}

.chart {
  position: static;
  :global(.recharts-cartesian-grid-vertical) {
    :nth-last-child(2),
    :nth-last-child(1) {
      opacity: 0;
    }
  }
  :global(.recharts-yAxis .recharts-cartesian-axis-ticks) {
    :global(.recharts-cartesian-axis-tick-value) {
      font-size: 12px;
      height: 15px;
      color: var(--color-text-second);
    }

    :last-child {
      :global(.recharts-cartesian-axis-tick-value),
      :global(.recharts-cartesian-axis-tick-value) span {
        fill: var(--color-text-link);
        color: var(--color-text-link);
        line-height: 1.7;
        font-size: 11px;
        height: 13px;
      }
    }
  }

  :global(.recharts-xAxis .recharts-cartesian-axis-tick-value) {
    font-size: 12px;
    transform: translateY(10px);
  }

  :global(.recharts-brush > rect) {
    &:first-child {
      fill: var(--color-button-disable);
      stroke: none;
    }
  }
  :global(.recharts-brush-slide) {
    fill: var(--color-row-select);
    fill-opacity: 1;
  }
  :global(.recharts-brush-traveller > rect) {
    fill: var(--color-int-on-disable);
  }
  :global(.recharts-brush-traveller > line) {
    stroke: var(--color-int-on-active);
  }
  :global(.recharts-brush-texts text) {
    font: var(--font-body-text-9);
    fill: var(--color-text-second);

    &[text-anchor="end"] {
      transform: translateX(3px);
    }

    &[text-anchor="start"] {
      transform: translateX(-3px);
    }
  }

  :global(.recharts-surface) {
    overflow: visible;
  }
}

.basChartWithWatermark {
  &:after {
    width: 50%;
    left: 50%;
    bottom: 32px;
    transform: translateX(-50%);

    @media screen and (max-width: $xs) {
      width: 80%;
      bottom: 20px;
    }
  }
}

.emptyState {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 2;
}
