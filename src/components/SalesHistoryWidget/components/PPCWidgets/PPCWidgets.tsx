import React, { memo } from "react"
import { useSelector } from "react-redux"
import { Box, Grid } from "@develop/fe-library"

import {
  getPPCMetricsSelector,
  isSalesHistoryLoadingSelector,
} from "selectors/salesHistorySelectors"

import { SetupWizardLink } from "components/shared/SetupWizardLink"

import { useSubscription, useUrlParams } from "hooks"

import l from "utils/intl"

import { PPC_METRICS_ICONS, PPC_SUBMETRICS_ICONS } from "./constants"

import { PPCWidget } from "./components/PPCWidget"
import { PPCWidgetHeader } from "./components/PPCWidgetHeader"

import { DashboardFiltersParams } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { PPCMetrics, PPCWidgetsTypes } from "./PPCWidgetsTypes"

import styles from "./ppcWidgets.module.scss"

const PPCWidgetsComponent: PPCWidgetsTypes = () => {
  const ppcMetrics: PPCMetrics = useSelector(getPPCMetricsSelector)
  const isSalesHistoryLoading = useSelector(isSalesHistoryLoadingSelector)

  const { isBasSubscriptionActive } = useSubscription()

  const [brandsWidget, ...restWidget] = ppcMetrics

  const { urlParams } = useUrlParams<DashboardFiltersParams>()

  const isPPCMetricsAvailable: boolean = isBasSubscriptionActive

  // TODO: Should be refactored to Skeletons
  if (isSalesHistoryLoading) {
    return <PPCWidgetHeader />
  }

  return (
    <>
      <PPCWidgetHeader />

      {isPPCMetricsAvailable ? (
        <Box
          display="block"
          marginBottom="auto"
          width="100%"
          mXL={{
            marginBottom: "m",
          }}
        >
          <Grid container gapMSM="m" gapMXL="l">
            <Grid item dLG={3} dSM={4} mSM={12} tb={6}>
              <PPCWidget
                currency={urlParams.currency_code}
                icon={PPC_METRICS_ICONS.sponsored_brands}
                subMetricsIcons={PPC_SUBMETRICS_ICONS}
                widget={brandsWidget}
                mXL={{
                  padding: "l",
                  height: "100%",
                }}
              />
            </Grid>
            <Grid item dLG={9} dSM={8} mSM={12} tb={6}>
              <Grid
                container
                className={styles.sidewidgets}
                gapMSM="m"
                gapMXL="l"
              >
                {restWidget.map((widget, index) => {
                  const isTotalAcos: boolean = widget?.id === "total_acos"
                  const acosTooltip: string = isTotalAcos
                    ? l("The ratio of total Advertising costs to total sales")
                    : ""

                  return (
                    <Grid
                      key={index}
                      item
                      className={styles.subgridItem}
                      dLG={4}
                      dSM={6}
                      mSM={12}
                    >
                      <PPCWidget
                        currency={urlParams.currency_code}
                        height="92px"
                        icon={PPC_METRICS_ICONS?.[widget.id]}
                        widget={widget}
                        mXL={{
                          padding: "l",
                          height: "100%",
                        }}
                        nameTooltip={{
                          content: acosTooltip,
                          size: "--icon-size-3",
                        }}
                      />
                    </Grid>
                  )
                })}
              </Grid>
            </Grid>
          </Grid>
        </Box>
      ) : (
        <Box align="center" justify="center" padding="l">
          <SetupWizardLink />
        </Box>
      )}
    </>
  )
}

export const PPCWidgets = memo(PPCWidgetsComponent)
