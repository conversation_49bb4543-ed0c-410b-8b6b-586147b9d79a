import { IconProps } from "@develop/fe-library"

import {
  BaseMetric,
  BaseSubMetric,
} from "components/shared/Widget/components/SimpleWidget/SimpleWidgetTypes"

export type PPCWidgetsTypes = () => JSX.Element | null

export type PPCSubMetrics_SponsoredBrandsProductCollection = BaseSubMetric & {
  id: "sponsored_brands_product_collection"
  name: "Product collection"
  type: "money"
}

export type PPCSubMetrics_SponsoredBrandsStoreSpotlight = BaseSubMetric & {
  id: "sponsored_brands_store_spotlight"
  name: "Store spotlight"
  type: "money"
}

export type PPCSubMetrics_SponsoredBrandsVideo = BaseSubMetric & {
  id: "sponsored_brands_video"
  name: "Video"
  type: "money"
}

export type PPCSubMetrics = [
  PPCSubMetrics_SponsoredBrandsProductCollection,
  PPCSubMetrics_SponsoredBrandsStoreSpotlight,
  PPCSubMetrics_SponsoredBrandsVideo,
]

export type PPCMetrics_SponsoredBrands = BaseMetric & {
  id: "sponsored_brands"
  name: "Sponsored Brands"
  type: "money"
  subMetrics: PPCSubMetrics
}

export type PPCMetrics_SponsoredProducts = BaseMetric & {
  id: "sponsored_products"
  name: "Sponsored Products"
  type: "money"
}

export type PPCMetrics_SponsoredDisplay = BaseMetric & {
  id: "sponsored_display"
  name: "Sponsored Display"
  type: "money"
}

export type PPCMetrics_TotalACoS = BaseMetric & {
  id: "total_acos"
  name: "Total ACoS"
  type: "pct"
}

export type PPCMetrics = [
  PPCMetrics_SponsoredBrands,
  PPCMetrics_SponsoredProducts,
  PPCMetrics_SponsoredDisplay,
  PPCMetrics_TotalACoS,
]

type IconNames = IconProps["name"]

export type PPCMetricsIcons = Record<PPCMetrics[number]["id"], IconNames>

export type PPCSubMetricsIcons = Record<
  PPCMetrics_SponsoredBrands["subMetrics"][number]["id"],
  IconNames
>
