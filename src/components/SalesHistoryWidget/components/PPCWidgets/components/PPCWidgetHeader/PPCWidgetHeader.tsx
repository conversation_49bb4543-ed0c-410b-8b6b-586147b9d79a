import React, { ReactNode, useMemo } from "react"
import { Box, IconPopover, Typography } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"
import { useSelector } from "react-redux"

import FormattedMessage from "components/FormattedMessage"
import { DashboardFiltersParams } from "components/SegmentsSidebar/components/Filters/FiltersTypes"
import Link from "components/shared/Link"

import { useSubscription, useUrlParams } from "hooks"

import { customerHasConnectedAmazonAdAccountsSelector } from "selectors/mainStateSelectors"

import { convertToLocalDate } from "utils/dateConverter"

import { PPCWidgetHeaderTypes } from "./PPCWidgetHeaderTypes"

import styles from "./PPCWidgetHeader.module.scss"

export const PPCWidgetHeader: PPCWidgetHeaderTypes = () => {
  const { urlParams } = useUrlParams<DashboardFiltersParams>()
  const { isBasSubscriptionActive } = useSubscription()
  const hasConnectedAmazonAdAccounts = useSelector(
    customerHasConnectedAmazonAdAccountsSelector,
  )

  const dateRange = useMemo(() => {
    const from = convertToLocalDate(urlParams.from as string)
    const to = convertToLocalDate(urlParams.to as string)

    return `${from} - ${to}`
  }, [urlParams.from, urlParams.to])

  const shouldShowIconPopover: boolean =
    isBasSubscriptionActive && !hasConnectedAmazonAdAccounts

  return (
    <Box
      gap="m"
      align="center"
      margin="m 0"
      padding="m m 0"
      mXL={{
        marginTop: "l",
        padding: "l l 0",
      }}
      hasBorder={{
        top: true,
      }}
      borderTopColor="--color-border-main"
      className={styles.topline}
    >
      <Typography variant="--font-headline-5">
        <FormattedMessage
          defaultMessage="Ads (PPC) breakdown: {dateRange}"
          id="Ads (PPC) breakdown: {dateRange}"
          values={{
            dateRange,
          }}
        />
      </Typography>

      {shouldShowIconPopover ? (
        <IconPopover
          name="icnInfoCircle"
          placement="top"
          size="--icon-size-2"
          color="--color-icon-active"
          content={
            <FormattedMessage
              defaultMessage="Connect <a>Amazon Ads account</a> to see a detailed Ads (PPC) breakdown"
              id="Connect <a>Amazon Ads account</a> to see a detailed Ads (PPC) breakdown"
              values={{
                a: (chunks: ReactNode) => (
                  // We should create component Link this is legacy component
                  // It need to be refact
                  // @ts-expect-error
                  <Link
                    url={ROUTES.GENERAL_ROUTES.PATH_AMAZON_ADS_ACCOUNTS}
                    text={chunks}
                    styleType="primary"
                    type="span"
                    variant="text"
                  />
                ),
              }}
            />
          }
        />
      ) : null}
    </Box>
  )
}
