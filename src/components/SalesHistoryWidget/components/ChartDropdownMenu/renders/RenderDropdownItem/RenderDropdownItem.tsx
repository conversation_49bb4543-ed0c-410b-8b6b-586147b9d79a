import React from "react"
import { Box, Icon, Popover, Typography } from "@develop/fe-library"

export const RenderDropdownItem = ({ item }) => {
  const { data, label } = item
  const { popoverContent = null, iconName } = data

  return (
    <Popover content={popoverContent}>
      <Box align="center" gap="m" padding="s 0">
        <Icon
          color="--color-icon-static"
          name={iconName}
          size="--icon-size-3"
        />

        <Typography color="--color-text-main" variant="--font-body-text-9">
          {label}
        </Typography>
      </Box>
    </Popover>
  )
}
