import React from "react"
import { DropdownMenu } from "@develop/fe-library"

import { MODAL_IDS } from "components/SalesHistoryWidget/constants"
import { RestrictedIconPopover } from "components/shared/RestrictedIconPopover"
import { SetupWizardLink } from "components/shared/SetupWizardLink"

import l from "utils/intl"

import { restrictPopoverMessages } from "constants/permissions"

import { RenderDropdownItem } from "./renders"

import { ChartDropdownMenuProps } from "./ChartDropdownMenuTypes"

export const ChartDropdownMenu = ({
  isSalesHistorySettingsEmpty,
  isChartEmptyOrSubscriptionExpired,
  isBasSubscriptionExpired,
  onDropdownItemSelect,
  isBasSubscriptionActive,
}: ChartDropdownMenuProps) => {
  const options = [
    {
      label: l("Manage view"),
      value: MODAL_IDS.ManageView,
      disabled: isSalesHistorySettingsEmpty,
      data: {
        iconName: "icnSetting",
      },
    },
    {
      label: l("Export data"),
      value: MODAL_IDS.ExportData,
      disabled: isChartEmptyOrSubscriptionExpired,
      data: {
        popoverContent: isBasSubscriptionExpired
          ? l(restrictPopoverMessages.action)
          : null,
        iconName: "icnToTop",
      },
    },
    {
      label: l("Download chart"),
      value: MODAL_IDS.Download,
      disabled: isChartEmptyOrSubscriptionExpired,
      data: {
        popoverContent: isBasSubscriptionExpired
          ? l(restrictPopoverMessages.action)
          : null,
        iconName: "icnLineChart",
      },
    },
  ]

  const handleDropdownItemSelect = (index: number): void => {
    onDropdownItemSelect(options[index].value)
  }

  return (
    <DropdownMenu
      options={options}
      placement="bottomRight"
      renderItem={RenderDropdownItem}
      onSelect={handleDropdownItemSelect}
    >
      <RestrictedIconPopover
        managePermission={isBasSubscriptionActive}
        name="icnEllipsis"
        popoverMessage={<SetupWizardLink />}
      />
    </DropdownMenu>
  )
}
