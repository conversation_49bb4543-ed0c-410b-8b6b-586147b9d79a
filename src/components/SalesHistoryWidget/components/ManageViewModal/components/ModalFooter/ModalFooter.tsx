import React from "react"
import { <PERSON>, But<PERSON> } from "@develop/fe-library"

import l from "utils/intl"

import { ModalFooterProps } from "./ModalFooterTypes"

export const ModalFooter = ({
  onResetClick,
  onApplyClick,
}: ModalFooterProps) => {
  return (
    <Box
      gap="m"
      justify="flex-end"
      width="100%"
      tb={{
        width: "auto",
        marginLeft: "auto",
      }}
    >
      <Button fullWidth variant="secondary" onClick={onResetClick}>
        {l("Reset")}
      </Button>

      <Button fullWidth onClick={onApplyClick}>
        {l("Apply")}
      </Button>
    </Box>
  )
}
