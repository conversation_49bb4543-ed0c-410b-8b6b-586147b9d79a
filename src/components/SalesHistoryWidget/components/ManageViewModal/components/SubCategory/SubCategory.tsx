import React from "react"
import { Box, Checkbox, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { SubCategoryProps } from "./SubCategoryTypes"

import styles from "./subCategory.module.scss"

export const SubCategory = ({
  subCategory,
  onSubCategoryChange,
  isChecked,
}: SubCategoryProps) => {
  const handleSubCategoryWrapperClick = (): void => {
    onSubCategoryChange(subCategory.id)(!isChecked)
  }

  return (
    <Box
      key={subCategory.name}
      align="center"
      className={styles.settingItem}
      cursor="pointer"
      flexDirection="row"
      gap="m"
      padding="m"
      onClick={handleSubCategoryWrapperClick}
    >
      <Checkbox
        checked={isChecked}
        onChange={onSubCategoryChange(subCategory.id)}
      />

      <Typography color="--color-text-main" variant="--font-body-text-7">
        {l(subCategory.name)}
      </Typography>
    </Box>
  )
}
