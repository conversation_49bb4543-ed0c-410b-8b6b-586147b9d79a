import React, { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Box, Modal } from "@develop/fe-library"
import { checkIsArray, getObjectValues } from "@develop/fe-library/dist/utils"

import userSettingsActions from "actions/userSettingsActions"

import { salesCategoriesSelector } from "selectors/salesHistorySelectors"
import { salesHistorySettingsSelector } from "selectors/userSettingsSelectors"

import l from "utils/intl"

import { USER_SETTINGS_KEYS } from "constants/user"

import { Category, ModalFooter } from "./components"

import {
  SalesHistoryCategoriesSelection,
  SalesHistoryCategory,
  SalesHistorySettings,
} from "components/SalesHistoryWidget/SalesHistoryWidgetTypes"

import { ManageViewModalProps } from "./ManageViewModalTypes"

const { saveUserSettings, updateUserSettings } = userSettingsActions

export const ManageViewModal = ({ onCancel }: ManageViewModalProps) => {
  const dispatch = useDispatch()
  const [currentSettings, setCurrentSettings] =
    useState<SalesHistoryCategoriesSelection | null>(null)

  const salesHistorySettings: SalesHistorySettings = useSelector(
    salesHistorySettingsSelector,
  )
  const salesCategories = useSelector(salesCategoriesSelector)

  const { value: settings, defaultSettings } = salesHistorySettings

  const handleReset = () => {
    setCurrentSettings(defaultSettings)
  }

  const handleSubCategoryChange =
    (key: string) =>
    (isChecked: boolean): void => {
      if (!currentSettings) {
        return
      }

      setCurrentSettings({
        ...currentSettings,
        [key]: isChecked,
      })
    }

  const handleSettingsApply = (): void => {
    if (salesHistorySettings.id) {
      dispatch(
        updateUserSettings(
          {
            id: salesHistorySettings.id,
            settings: {
              ...salesHistorySettings.value,
              ...currentSettings,
            },
          },
          onCancel,
        ),
      )

      return
    }

    dispatch(
      saveUserSettings(
        { key: USER_SETTINGS_KEYS.salesHistorySettings, settings },
        onCancel,
      ),
    )
  }

  useEffect(() => {
    if (settings) {
      setCurrentSettings(settings)
    }
  }, [settings])

  const categories: SalesHistoryCategory[] | null = salesCategories
    ? getObjectValues(salesCategories)
    : null

  return (
    <Modal
      isKeyboard
      isWithoutBodyPadding
      visible
      title={l("Manage view")}
      footer={
        <ModalFooter
          onApplyClick={handleSettingsApply}
          onResetClick={handleReset}
        />
      }
      onClose={onCancel}
    >
      <Box
        flexDirection="column"
        gap="l"
        maxHeight={410}
        overflowY="scroll"
        padding="l"
      >
        {categories
          ? categories.map(({ id, children, name }) => {
              return checkIsArray(children) ? (
                <Category
                  children={children}
                  key={id}
                  currentSettings={currentSettings}
                  id={id}
                  name={name}
                  onSubCategoryChange={handleSubCategoryChange}
                />
              ) : null
            })
          : null}
      </Box>
    </Modal>
  )
}
