import React, { useState } from "react"
import { Box, Modal, Select, Typography } from "@develop/fe-library"

import { downloadFile, makePdfFile } from "components/SalesHistoryWidget/utils"

import { useUrlParams } from "hooks"

import l from "utils/intl"

import { AVAILABLE_FORMATS } from "./constants"

import { ChartUrlParams } from "components/SalesHistoryWidget/SalesHistoryWidgetTypes"

import { DownloadChartModalProps } from "./DownloadChartModalTypes"

export const DownloadChartModal = ({ onCancel }: DownloadChartModalProps) => {
  const chartElement = document.querySelector<HTMLElement>(
    '[data-selector-id="chart"]',
  )

  const { urlParams } = useUrlParams<ChartUrlParams>()
  const [isLoading, setLoading] = useState(false)
  const [format, setFormat] = useState<string>(AVAILABLE_FORMATS.PNG.value)

  const handleFormatChange = ({ value }): void => {
    setFormat(value)
  }

  const startDownload = (): void => {
    setLoading(true)

    const fileName = `${l("sales")}_${l("history")}_${urlParams.from}_${
      urlParams.to
    }.${format}`

    if (!chartElement) {
      setLoading(false)

      return
    }

    if (format === AVAILABLE_FORMATS.PDF.value) {
      makePdfFile({
        element: chartElement,
        fileName,
        onSuccessCallback: () => {
          onCancel()
          setLoading(false)
        },
      })

      return
    }

    downloadFile({
      element: chartElement,
      fileName,
      onSuccessCallback: () => {
        onCancel()
        setLoading(false)
      },
    })
  }

  return (
    <Modal
      visible
      cancelButtonText={l("Cancel")}
      okButtonProps={{ disabled: isLoading }}
      okButtonText={l("Export")}
      title={l("Download chart")}
      onCancel={onCancel}
      onClose={onCancel}
      onOk={startDownload}
    >
      <Box flexDirection="column" gap="l">
        <Typography color="--color-text-second" variant="--font-body-text-7">
          {l("Choose how you want to download your Sales History chart")}{" "}
        </Typography>

        <Select
          defaultValue={format}
          options={[AVAILABLE_FORMATS.PNG, AVAILABLE_FORMATS.PDF]}
          onChange={handleFormatChange}
        />
      </Box>
    </Modal>
  )
}
