import setConfirm from "utils/confirm"
import l from "utils/intl"

type ConfirmResetChangesParams = {
  isChanged: boolean
  callback: () => void
}

export const confirmResetChanges = ({
  isChanged,
  callback,
}: ConfirmResetChangesParams): void => {
  if (!isChanged) {
    callback?.()
    return
  }

  setConfirm({
    title: l("Are you sure you want to proceed?"),
    message: l(`Your changes will not be saved`),
    onOk: () => {
      callback?.()
    },
    cancelText: l("Cancel"),
    okText: l("Confirm"),
  })
}
