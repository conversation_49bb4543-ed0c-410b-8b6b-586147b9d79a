import { useCallback, useMemo } from "react"
import { useDispatch, useSelector } from "react-redux"

import productCostActions from "actions/productCostActions"

import {
  isGlobalMarketplaceFeatureAvailableForSelectedMarketplaceSelector,
  productCostDrawerStatesSelector,
} from "selectors/productCostSelectors"

import { confirmResetChanges } from "components/ProductCostDrawer/utils"

import l from "utils/intl"

import {
  FORM_TYPES,
  FORM_TYPES_TITLES,
  PRODUCT_COST_DRAWER_ITEMS_ORDER,
} from "constants/productCost"

const { endEditProduct, setIsFormEditing, changeFormType, endEditPeriod } =
  productCostActions

export const useProductCostDrawer = ({ onClose }) => {
  const dispatch = useDispatch()

  const {
    formType,
    isPeriods,
    isSynchronizeWithRepricer,
    hasShippingCostTabs,
    isFormChanged,
    isCreatePeriodModalVisible,
    isDisclaimerModalVisible,
    isTransferCostsModalVisible,
    isShippingCostTabEnabled,
    isSelectedMarketplaceGlobal,
  } = useSelector(productCostDrawerStatesSelector)

  const isGlobalMarketplaceFeatureAvailableForSelectedMarketplace = useSelector(
    isGlobalMarketplaceFeatureAvailableForSelectedMarketplaceSelector,
  )

  const hasLinkDetails: boolean =
    isGlobalMarketplaceFeatureAvailableForSelectedMarketplace &&
    !isSelectedMarketplaceGlobal

  const handleClose = useCallback(() => {
    const callback = () => {
      dispatch(endEditProduct())
      dispatch(setIsFormEditing(false))
      onClose?.()
    }

    confirmResetChanges({
      callback,
      isChanged: isFormChanged,
    })
  }, [isFormChanged, onClose])

  const items = useMemo(() => {
    return PRODUCT_COST_DRAWER_ITEMS_ORDER.map((key) => {
      const disabled: boolean =
        key === FORM_TYPES.shipping_cost && !isShippingCostTabEnabled

      return {
        label: l(FORM_TYPES_TITLES[key]),
        value: key,
        disabled,
      }
    })
  }, [isShippingCostTabEnabled])

  const selectedItemIndex = useMemo(() => {
    return items.findIndex((item) => item.value === formType)
  }, [formType])

  const handleSelect = useCallback(
    (index) => {
      const callback = () => {
        dispatch(endEditPeriod())
        dispatch(setIsFormEditing(false))
        dispatch(changeFormType(items[index].value))
      }

      confirmResetChanges({
        callback,
        isChanged: isFormChanged,
      })
    },
    [isFormChanged],
  )

  return {
    formType,
    items,
    selectedItemIndex,
    isPeriods,
    hasShippingCostTabs,
    isSynchronizeWithRepricer,
    isCreatePeriodModalVisible,
    isDisclaimerModalVisible,
    isTransferCostsModalVisible,
    onSelect: handleSelect,
    onClose: handleClose,
    hasLinkDetails,
  }
}
