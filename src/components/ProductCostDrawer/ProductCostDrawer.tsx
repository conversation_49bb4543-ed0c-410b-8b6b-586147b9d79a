import React, { memo } from "react"
import { Box, Drawer } from "@develop/fe-library"

import l from "utils/intl"

import {
  CreatePeriodModal,
  DisclaimerModal,
  ExpandableMenuWrapper,
  FormTypesMenu,
  LinkDetails,
  MarketplacesTabs,
  Periods,
  ProductDetails,
  ShippingCost,
  SynchronizeWithRepricer,
  TransferCostsModal,
} from "./components"

import { useProductCostDrawer } from "./hooks"

import styles from "./ProductCostDrawer.module.scss"

type ProductCostDrawerProps = {
  onClose?: () => void
}

export const ProductCostDrawer = memo(({ onClose }: ProductCostDrawerProps) => {
  const {
    formType,
    items,
    selectedItemIndex,
    isPeriods,
    hasShippingCostTabs,
    isSynchronizeWithRepricer,
    isCreatePeriodModalVisible,
    isDisclaimerModalVisible,
    isTransferCostsModalVisible,
    onSelect: handleSelect,
    onClose: handleClose,
    hasLinkDetails,
  } = useProductCostDrawer({ onClose })

  if (!formType) {
    return null
  }

  return (
    <>
      <Drawer
        isOpen
        hasHeaderBottomMargin={false}
        title={l("Product details")}
        width="min-content"
        zIndex={1002}
        onClose={handleClose}
      >
        <Box
          height="100%"
          zIndex={1}
          mLG={{
            width: "calc(100vw - 20px)",
            maxWidth: 984,
          }}
          mSM={{
            width: "100vw",
            maxWidth: "100vw",
          }}
        >
          <ExpandableMenuWrapper selectedItemIndex={selectedItemIndex}>
            <FormTypesMenu
              items={items}
              selectedIndex={selectedItemIndex}
              onSelect={handleSelect}
            />
          </ExpandableMenuWrapper>
          <Box
            className={styles.tabsContainer}
            flex={1}
            overflow="hidden"
            paddingTop={hasShippingCostTabs ? "m" : "0"}
          >
            <ShippingCost hasShippingCostTabs={hasShippingCostTabs}>
              <Box className={styles.tabsContainer} flex={1} paddingTop="m">
                <MarketplacesTabs>
                  {hasLinkDetails ? <LinkDetails /> : null}
                  <ProductDetails />
                  {isPeriods ? <Periods onClose={handleClose} /> : null}
                  {isSynchronizeWithRepricer ? (
                    <SynchronizeWithRepricer onClose={handleClose} />
                  ) : null}
                </MarketplacesTabs>
              </Box>
            </ShippingCost>
          </Box>
        </Box>
      </Drawer>

      {isDisclaimerModalVisible ? <DisclaimerModal /> : null}
      {isTransferCostsModalVisible ? <TransferCostsModal /> : null}
      {isCreatePeriodModalVisible ? <CreatePeriodModal /> : null}
    </>
  )
})
