import React from "react"
import { Box, IconPopover, Tabs, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { SHIPPING_COST_TAB_KEYS } from "constants/productCost"

import { useShippingCost } from "./hooks"
import { EditOrderCost, OrderDetails } from "./components"

import type { ShippingCostProps } from "./ShippingCostTypes"

export const ShippingCost = ({
  children,
  hasShippingCostTabs,
}: ShippingCostProps) => {
  const { activeKey, onChange, headerPadding, isMobile } = useShippingCost()

  if (!hasShippingCostTabs) {
    return <>{children}</>
  }

  return (
    <Tabs
      activeKey={activeKey as string}
      onChange={onChange}
      headerPadding={headerPadding}
      isFullWidth={isMobile}
      items={[
        {
          key: SHIPPING_COST_TAB_KEYS.product,
          label: l("Product shipping cost"),
          icon: "icnTag",
          contents: children,
        },
        {
          key: SHIPPING_COST_TAB_KEYS.order,
          label: l("Order shipping cost"),
          icon: "icnShoppingCart",
          contents: (
            <Box height="100%" flexDirection="column">
              <OrderDetails />
              <EditOrderCost />
            </Box>
          ),
        },
      ]}
      renderLabel={({ item }) => {
        return (
          <Box gap="s">
            <IconPopover
              name={item.icon}
              size="--icon-size-3"
              content={isMobile ? item.label : null}
            />
            {!isMobile ? (
              <Typography variant="--font-body-text-6">{item.label}</Typography>
            ) : null}
          </Box>
        )
      }}
      renderContents={(contents) => (
        <Box flexDirection="column" height="100%" align="stretch">
          {contents}
        </Box>
      )}
    />
  )
}
