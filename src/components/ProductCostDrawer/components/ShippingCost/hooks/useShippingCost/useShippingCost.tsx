import { useCallback } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useBreakpoint } from "@develop/fe-library/dist/hooks"
import { breakpoints } from "@develop/fe-library/dist/consts"

import { selectedShippingCostTabSelector } from "selectors/productCostSelectors"

import productCostActions from "actions/productCostActions"

const { selectShippingCostTab } = productCostActions

export const useShippingCost = () => {
  const dispatch = useDispatch()

  const activeKey = useSelector(selectedShippingCostTabSelector)

  const breakpoint = useBreakpoint()

  const isMobile: boolean =
    breakpoint === breakpoints.mSM ||
    breakpoint === breakpoints.mMD ||
    breakpoint === breakpoints.mLG

  const handleChange = useCallback((tab): void => {
    dispatch(selectShippingCostTab(tab.key))
  }, [])

  return {
    activeKey,
    onChange: handleChange,
    headerPadding: !isMobile ? "l" : undefined,
    isMobile,
  }
}
