import React from "react"
import { useSelector } from "react-redux"

import { orderDetailsStatesSelector } from "selectors/productCostSelectors"

import { DetailsLayout } from "components/ProductCostDrawer/components/DetailsLayout"

export const OrderDetails = () => {
  const {
    imageUrl,
    product_title,
    product_asin,
    seller_sku,
    order_id,
    amazonCustomerAccountName,
    product_stock_type,
    sales_channel,
    isBasSubscriptionExpired,
  } = useSelector(orderDetailsStatesSelector)

  return (
    <DetailsLayout
      amazonCustomerAccountName={amazonCustomerAccountName}
      asin={product_asin}
      hasTransferCostsButton={false}
      imageUrl={imageUrl}
      isBasSubscriptionExpired={isBasSubscriptionExpired}
      isLoading={false}
      orderId={order_id}
      salesChannel={sales_channel}
      sku={seller_sku}
      stockType={product_stock_type}
      title={product_title}
    />
  )
}
