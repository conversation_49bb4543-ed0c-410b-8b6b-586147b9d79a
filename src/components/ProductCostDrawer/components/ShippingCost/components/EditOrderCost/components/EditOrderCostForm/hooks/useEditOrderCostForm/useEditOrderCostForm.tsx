import React, { use<PERSON>allback, useMemo } from "react"
import { useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import {
  Box,
  EmptyImage,
  Flag,
  InputAddon,
  InputGroup,
} from "@develop/fe-library"
import { useFormReset } from "@develop/fe-library/dist/hooks"
import { getObjectKeys } from "@develop/fe-library/dist/utils"

import { ordersActions } from "actions/ordersActions"
import productCostActions from "actions/productCostActions"

import { amazonMarketplacesSelector } from "selectors/mainStateSelectors"
import {
  isFormChangedSelector,
  orderSelector,
  productSelector,
} from "selectors/productCostSelectors"

import { ConnectedField } from "components/ProductCostDrawer/components/ConnectedField"
import { confirmResetChanges } from "components/ProductCostDrawer/utils"

import { checkIsArray } from "utils/arrayHelpers"
import { getImageUrlFromProduct } from "utils/getImageUrlFromProduct"
import l from "utils/intl"

import { CURRENCIES_NAMES } from "constants/currencies"

import type { AmazonOrderItemAmountCost } from "types/Models"

import type {
  EditOrderCostFormProps,
  EditOrderCostFormValues,
  ServerErrors,
} from "../../EditOrderCostFormTypes"

const { setIsFormEditing, endEditProduct } = productCostActions
const { updateAmazonOrderAmountCosts } = ordersActions

export const useEditOrderCostForm = ({
  orderAmountCosts,
}: EditOrderCostFormProps) => {
  const dispatch = useDispatch()

  const isFormChanged = useSelector(isFormChangedSelector)
  const order = useSelector(orderSelector)
  const product = useSelector(productSelector)
  const { amazonMarketplaces } = useSelector(amazonMarketplacesSelector)

  const currencyId: string = orderAmountCosts?.currency_id || "EUR"
  const flagLocale: string =
    CURRENCIES_NAMES[currencyId].countryCode.toLowerCase()

  const initialValues = useMemo(() => {
    const sortedItems: Array<AmazonOrderItemAmountCost> = checkIsArray(
      orderAmountCosts?.items,
    )
      ? orderAmountCosts?.items
      : []

    const currentOrderItemId = order?.order_item_id

    sortedItems.sort((firstItem, secondItem) => {
      if (firstItem.order_item_id === currentOrderItemId) {
        return -1
      }

      return secondItem.order_item_id === currentOrderItemId ? 1 : 0
    })

    let total = 0

    const resultItems = sortedItems.map((item, index) => {
      total += item.amount

      return {
        ...item,
        id: index,
      }
    })

    return {
      items: resultItems,
      total,
    }
  }, [orderAmountCosts, order?.order_item_id])

  const { items } = initialValues

  const {
    control,
    formState,
    getValues,
    setValue,
    handleSubmit: formHandleSubmit,
    reset,
    setError,
  } = useForm<EditOrderCostFormValues>({
    defaultValues: initialValues,
  })

  useFormReset<EditOrderCostFormValues>({
    initialValues,
    reset,
  })

  const { isSubmitting, isValid } = formState

  const isSaveButtonDisabled: boolean =
    isSubmitting || !isFormChanged || !isValid

  const handleClose = useCallback(() => {
    const callback = () => {
      dispatch(endEditProduct())
    }

    confirmResetChanges({
      callback,
      isChanged: isFormChanged,
    })
  }, [isFormChanged])

  const handleSubmit = useCallback(
    formHandleSubmit(async ({ items }) => {
      if (!order?.order_id) {
        return
      }

      const mappedItems = orderAmountCosts.items.map((item) => {
        const { id, ...rest } =
          items.find(
            (itemUpdated) => itemUpdated.order_item_id === item.order_item_id,
          ) || {}

        return rest
      })

      if (!checkIsArray(mappedItems)) {
        return
      }

      const payload = {
        ...orderAmountCosts,
        items: mappedItems,
      }

      const successCallback = (): void => {
        dispatch(setIsFormEditing(false))
      }

      const failureCallback = (serverErrors: ServerErrors): void => {
        const formErrors = serverErrors.errors.reduce(
          (acc, { index, errors }) => {
            const keyPrefix = `items.${index}`
            const keys = getObjectKeys(errors)

            if (!checkIsArray(keys)) {
              return acc
            }

            return {
              ...acc,
              ...keys.reduce((fieldErrors, fieldKey) => {
                const errorKey = `${keyPrefix}.${fieldKey}`
                const errorMessage = errors[fieldKey].join("\n")

                return {
                  ...fieldErrors,
                  [errorKey]: errorMessage,
                }
              }, {}),
            }
          },
          {},
        )

        getObjectKeys(formErrors).forEach((field) => {
          setError(field, {
            type: "manual",
            message: formErrors[field],
          })
        })
      }

      await dispatch(
        updateAmazonOrderAmountCosts({
          params: {
            amazonOrderId: order.order_id,
            payload,
          },
          successCallback,
          failureCallback,
        }),
      )
    }),
    [order?.order_id, orderAmountCosts],
  )

  const handleChangeTotal = useCallback(
    (value) => {
      if (!checkIsArray(items)) {
        return
      }

      const amountPerItem = value / items.length

      const newFields =
        items.map((item) => ({
          ...item,
          amount: amountPerItem,
        })) || []

      setValue("items", newFields)
      dispatch(setIsFormEditing(true))
    },
    [items],
  )

  const columns = useMemo(() => {
    const buildHandleChange =
      (index: number) =>
      (value: number): void => {
        const items = getValues("items")

        const totalNew = items.reduce((acc, item, i) => {
          if (i === index) {
            return acc + value
          }

          return acc + item.amount
        }, 0)

        setValue("total", totalNew)
        dispatch(setIsFormEditing(true))
      }

    const getAmazonMarketplaceTitle = (marketplace_id: string): string => {
      const amazonMarketplace = amazonMarketplaces.find(
        (marketplace) => marketplace.id === marketplace_id,
      )

      return amazonMarketplace?.title || ""
    }

    return [
      {
        title: l("Order item shipping cost"),
        key: "amount",
        dataIndex: "amount",
        width: 200,
        renderCell: ({ item }) => {
          const index = item.id

          const name = `items.${index}.amount`

          return (
            <InputGroup>
              <ConnectedField
                control={control}
                name={name}
                type="numeric"
                inputProps={{
                  label: l("Value"),
                  isNegativeAllowed: false,
                  onChange: buildHandleChange(index),
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 6,
                }}
              />
              <InputAddon>
                <Box align="center" gap="s" height="100%" width="100%">
                  {currencyId}
                  <Flag
                    borderRadius="--border-radius-circle"
                    locale={flagLocale}
                    size={18}
                  />
                </Box>
              </InputAddon>
            </InputGroup>
          )
        },
      },
      {
        title: l("Image"),
        key: "image",
        dataIndex: "id",
        width: 48,
        hasPadding: false,
        renderCell: ({ item }) => {
          const { seller_id, product_asin } = item

          const productForImage = product
            ? {
                ...product,
                seller_id,
                asin: product_asin,
              }
            : product

          const imageUrl = getImageUrlFromProduct(productForImage)

          return (
            <Box align="center" justify="center" minHeight={48} minWidth={48}>
              <EmptyImage height={48} url={imageUrl} width={48} />
            </Box>
          )
        },
      },
      {
        title: l("Title"),
        key: "product_title",
        dataIndex: "product_title",
        width: 200,
        hasEllipsis: true,
      },
      {
        title: "ASIN",
        key: "product_asin",
        dataIndex: "product_asin",
        width: 90,
        hasEllipsis: true,
      },
      {
        title: "SKU",
        key: "seller_sku",
        dataIndex: "seller_sku",
        width: 90,
        hasEllipsis: true,
      },
      {
        title: l("Marketplace"),
        key: "marketplace_id",
        dataIndex: "marketplace_id",
        width: 90,
        hasEllipsis: true,
        renderCell: ({ item }) => {
          return getAmazonMarketplaceTitle(item.marketplace_id)
        },
      },
    ]
  }, [product?.asin, currencyId])

  return {
    control,
    items,
    columns,
    onChangeTotal: handleChangeTotal,
    onClose: handleClose,
    onSubmit: handleSubmit,
    isSubmitting,
    isSaveButtonDisabled,
    currencyId,
    flagLocale,
  }
}
