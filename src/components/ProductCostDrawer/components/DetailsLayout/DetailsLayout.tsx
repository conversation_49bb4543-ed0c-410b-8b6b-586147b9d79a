import React from "react"
import { Box, Ellipsis, EmptyImage, Icon, Skeleton } from "@develop/fe-library"

import { RestrictedButtonPopover } from "components/shared/RestrictedButtonPopover"

import { useDetailsLayout } from "./hooks"

import type { DetailsLayoutProps } from "./DetailsLayoutTypes"

export const DetailsLayout = ({
  title,
  imageUrl,
  salesChannel,
  asin,
  sku,
  amazonCustomerAccountName,
  stockType,
  orderId,
  hasTransferCostsButton,
  isLoading,
  isBasSubscriptionExpired,
  isTransferCostsButtonDisabled,
}: DetailsLayoutProps) => {
  const { items, isMobile, buttonProps } = useDetailsLayout({
    salesChannel,
    asin,
    sku,
    amazonCustomerAccountName,
    stockType,
    orderId,
    isBasSubscriptionExpired,
    isTransferCostsButtonDisabled,
  })

  return (
    <Box
      gap="m"
      hasBorder={{ bottom: true }}
      mSM={{ flexDirection: "column" }}
      mXL={{ flexDirection: "row" }}
      padding="l"
    >
      <Box flex={1} gap="m">
        <EmptyImage height={48} url={imageUrl} width={48} />

        <Box flex={1} flexDirection="column" gap="m">
          {isLoading ? (
            <Skeleton />
          ) : (
            <Ellipsis
              typographyProps={{
                variant: "--font-body-text-6",
              }}
            >
              {title}
            </Ellipsis>
          )}

          <Box
            mSM={{
              flexDirection: "column",
              gap: "m",
            }}
            mXL={{
              flexDirection: "row",
              columnGap: "xl",
              rowGap: "m",
              flexWrap: "wrap",
            }}
          >
            {items}
          </Box>
        </Box>
      </Box>
      {hasTransferCostsButton && isMobile ? (
        <RestrictedButtonPopover fullWidth {...buttonProps}>
          <Icon name="icnExport" size="--icon-size-3" />
        </RestrictedButtonPopover>
      ) : null}
      {hasTransferCostsButton && !isMobile ? (
        <RestrictedButtonPopover iconOnly icon="icnExport" {...buttonProps} />
      ) : null}
    </Box>
  )
}
