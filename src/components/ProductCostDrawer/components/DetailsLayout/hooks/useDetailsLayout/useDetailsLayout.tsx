import React, { useMemo } from "react"
import { useDispatch } from "react-redux"
import { Box, Typography } from "@develop/fe-library"
import { breakpoints } from "@develop/fe-library/dist/consts"
import { useBreakpoint } from "@develop/fe-library/dist/hooks"

import productCostActions from "actions/productCostActions"

import { LINKED_MARKETPLACE_DISABLED_POPOVER } from "components/ProductCostDrawer/constants"

import l from "utils/intl"
import { getAmazonOrderNumberLink, getAmazonProductLink } from "utils/links"

import { restrictPopoverMessages } from "constants/permissions"

import type { Item } from "./UseDetailsLayoutTypes"

const { openTransferCostsModal } = productCostActions

export const useDetailsLayout = ({
  salesChannel,
  asin,
  sku,
  amazonCustomerAccountName,
  stockType,
  orderId,
  isBasSubscriptionExpired,
  isTransferCostsButtonDisabled,
}) => {
  const dispatch = useDispatch()

  const breakpoint = useBreakpoint()

  const isMobile: boolean =
    breakpoint === breakpoints.mSM ||
    breakpoint === breakpoints.mMD ||
    breakpoint === breakpoints.mLG

  const items = useMemo(() => {
    const itemsToRender: Array<Item> = []

    const asinLink = getAmazonProductLink(salesChannel, asin)

    itemsToRender.push({
      label: "ASIN",
      value: (
        <a href={asinLink} rel="noopener noreferrer" target="_blank">
          {asin}
        </a>
      ),
      valueColor: "--color-text-link",
    })

    itemsToRender.push({
      label: "SKU",
      value: sku,
    })

    if (!!amazonCustomerAccountName) {
      itemsToRender.push({
        label: l("Account"),
        value: amazonCustomerAccountName,
      })
    }

    itemsToRender.push({
      label: l("Fulfilment method"),
      value: stockType,
    })

    if (!!orderId) {
      const orderLink = getAmazonOrderNumberLink({
        salesChannel: salesChannel,
        orderId: orderId,
      })

      itemsToRender.push({
        label: l("Order number"),
        value: (
          <a href={orderLink} rel="noopener noreferrer" target="_blank">
            {orderId}
          </a>
        ),
        valueColor: "--color-text-link",
      })
    }

    return itemsToRender.map(({ label, value, valueColor }) => {
      return (
        <Box key={label} gap="m">
          <Typography color="--color-text-second" variant="--font-body-text-9">
            {label}:
          </Typography>

          <Typography color={valueColor} variant="--font-body-text-9">
            {value}
          </Typography>
        </Box>
      )
    })
  }, [salesChannel, asin, sku, amazonCustomerAccountName, stockType, orderId])

  const buttonProps = useMemo(() => {
    const handleShowTransferCostsModal = (): void => {
      dispatch(openTransferCostsModal())
    }

    return {
      variant: "secondary",
      content: l("Transfer costs"),
      managePermission:
        !isBasSubscriptionExpired && !isTransferCostsButtonDisabled,
      popoverMessage: isTransferCostsButtonDisabled
        ? l(LINKED_MARKETPLACE_DISABLED_POPOVER)
        : restrictPopoverMessages.action,
      popoverPlacement: "top",
      onClick: handleShowTransferCostsModal,
    }
  }, [isBasSubscriptionExpired, isTransferCostsButtonDisabled])

  return {
    items,
    isMobile,
    buttonProps,
  }
}
