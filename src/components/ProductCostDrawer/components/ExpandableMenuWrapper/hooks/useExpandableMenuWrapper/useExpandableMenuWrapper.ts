import { useCallback, useEffect, useRef, useState } from "react"
import { useBreakpoint, useOutsideClick } from "@develop/fe-library/dist/hooks"

export const useExpandableMenuWrapper = ({ selectedItemIndex }) => {
  const ref = useRef(null)

  const [isExpanded, setIsExpanded] = useState(false)

  const breakpoint = useBreakpoint()

  const canBeMinified: boolean =
    breakpoint === "mSM" ||
    breakpoint === "mMD" ||
    breakpoint === "mLG" ||
    breakpoint === "mXL" ||
    breakpoint === "tb"

  const handleExpand = useCallback((): void => {
    setIsExpanded(true)
  }, [])

  const handleCollapse = useCallback((): void => {
    setIsExpanded(false)
  }, [])

  useEffect(() => {
    setIsExpanded(false)
  }, [selectedItemIndex])

  useOutsideClick({
    refs: [ref],
    isEnabled: isExpanded,
    callback: handleCollapse,
  })

  return {
    ref,
    canBeMinified,
    isExpanded,
    handleExpand,
    handleCollapse,
  }
}
