import { useCallback } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Box, Button, Typography } from "@develop/fe-library"

import productCostActions from "actions/productCostActions"

import { linkDetailsStatesSelector } from "selectors/productCostSelectors"

import setConfirm from "utils/confirm"
import l from "utils/intl"

export const LinkDetails = () => {
  const dispatch = useDispatch()

  const { homeMarketplaceTitle, isEnabledSynWithGlobalMarketplace, id } =
    useSelector(linkDetailsStatesSelector)

  const text = l(
    isEnabledSynWithGlobalMarketplace
      ? "The current marketplace is linked to the {homeMarketplaceTitle} and inherits all of its settings. To change the settings, please unlink the current marketplace from the Main one"
      : "The current marketplace is not linked to the {homeMarketplaceTitle} and stores costs independently. To synchronise its costs with the Main marketplace, please link the current marketplace with the Main one.",
    { homeMarketplaceTitle },
  )

  const handleToggleLink = useCallback(() => {
    const payload = {
      id,
      is_enabled_sync_with_global_marketplace: isEnabledSynWithGlobalMarketplace
        ? 0
        : 1,
    }

    const handleOk = (): void => {
      dispatch(productCostActions.toggleLinkOnProduct(payload))
    }

    setConfirm({
      title: l("Are you sure you want to proceed?"),
      message: l(
        "Please note, any edits to this field will re-write settings for all current costs presented on this page for selected products.",
      ),
      onOk: handleOk,
      cancelText: l("Cancel"),
      okText: l("Confirm"),
    })
  }, [id, isEnabledSynWithGlobalMarketplace])

  return (
    <Box gap="m" hasBorder={{ bottom: true }} padding="l">
      <Typography variant="--font-body-text-9">{text}</Typography>

      <Button variant="secondary" onClick={handleToggleLink}>
        {isEnabledSynWithGlobalMarketplace ? l("Unlink") : l("Link")}
      </Button>
    </Box>
  )
}
