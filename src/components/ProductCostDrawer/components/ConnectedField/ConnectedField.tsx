import React from "react"
import { use<PERSON><PERSON><PERSON><PERSON> } from "react-hook-form"
import { AdaptiveInput } from "@develop/fe-library"

import { ConnectedFieldProps } from "./ConnectedFieldTypes"

export const ConnectedField = ({
  control,
  name,
  type,
  inputProps = {},
}: ConnectedFieldProps) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  })

  return (
    <AdaptiveInput
      type={type}
      inputProps={{
        ...inputProps,
        errorMessage: error?.message,
      }}
      value={field.value}
      onBlur={field.onBlur}
      onChange={field.onChange}
    />
  )
}
