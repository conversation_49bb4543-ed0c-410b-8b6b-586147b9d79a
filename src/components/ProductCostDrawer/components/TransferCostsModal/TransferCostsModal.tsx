import React from "react"
import { But<PERSON>, <PERSON>, FormItems, Modal, Typography } from "@develop/fe-library"

import { RestrictedPrimaryButton } from "components/shared/RestrictedButtons"

import l from "utils/intl"
import { checkIsArray } from "utils/arrayHelpers"

import { permissionKeys, restrictPopoverMessages } from "constants/permissions"

import { useTransferCostsModal } from "./hooks"

import { TransferCostsModalFormValues } from "./TransferCostsModalTypes"

export const TransferCostsModal = () => {
  const {
    form,
    items,
    marketplaces,
    transferMessage,
    onCancel,
    onSubmit,
    isSubmitting,
    isSubmitButtonDisabled,
  } = useTransferCostsModal()

  if (!checkIsArray(marketplaces)) {
    return null
  }

  return (
    <Modal
      visible
      title={l("Transfer costs")}
      onCancel={onCancel}
      footer={
        <Box
          width="100%"
          mSM={{
            display: "grid",
            gap: "m",
            gridTemplateColumns: "1fr 1fr",
          }}
          tb={{
            display: "flex",
            gap: "m",
            justify: "end",
          }}
        >
          <Button
            variant="secondary"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {l("Cancel")}
          </Button>
          <RestrictedPrimaryButton
            managePermission={permissionKeys.basMyProductsManage}
            popoverMessage={restrictPopoverMessages.alter}
            disabled={isSubmitButtonDisabled}
            onClick={onSubmit}
          >
            {l("Transfer")}
          </RestrictedPrimaryButton>
        </Box>
      }
    >
      <Box flexDirection="column" gap="m">
        <Typography variant="--font-body-text-5" color="--color-text-main">
          {l(transferMessage)}
        </Typography>

        <Typography variant="--font-body-text-9" color="--color-text-main">
          {l(
            "All cost periods for this product’s marketplace will be transferred to the rest of the marketplaces. After a new Repricer synchronization new cost period will be created for those marketplaces that change their values for the current cost.",
          )}
        </Typography>

        <FormItems<TransferCostsModalFormValues>
          gridContainerProps={{ gap: "m" }}
          // @ts-expect-error
          form={form}
          items={items}
        />
      </Box>
    </Modal>
  )
}
