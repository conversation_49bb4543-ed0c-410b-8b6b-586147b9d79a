import React from "react"
import { useSelector } from "react-redux"

import {
  detailsStatesSelector,
  isGlobalMarketplaceFeatureAvailableForSelectedMarketplaceSelector,
} from "selectors/productCostSelectors"

import { DetailsLayout } from "../DetailsLayout"

export const ProductDetails = () => {
  const {
    title,
    asin,
    sku,
    stock_type,
    imageUrl,
    isBasSubscriptionExpired,
    hasTransferCostsButton,
    sales_channel,
    isLoading,
    isEnabledSyncWithGlobalMarketplace,
  } = useSelector(detailsStatesSelector)

  const isGlobalMarketplaceFeatureAvailable = useSelector(
    isGlobalMarketplaceFeatureAvailableForSelectedMarketplaceSelector,
  )

  return (
    <DetailsLayout
      amazonCustomerAccountName={null}
      asin={asin}
      hasTransferCostsButton={hasTransferCostsButton}
      imageUrl={imageUrl}
      isBasSubscriptionExpired={isBasSubscriptionExpired}
      isLoading={isLoading}
      orderId={null}
      salesChannel={sales_channel}
      sku={sku}
      stockType={stock_type}
      title={title}
      isTransferCostsButtonDisabled={
        isGlobalMarketplaceFeatureAvailable &&
        isEnabledSyncWithGlobalMarketplace
      }
    />
  )
}
