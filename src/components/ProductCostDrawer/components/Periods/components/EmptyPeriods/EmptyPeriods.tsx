import React, { memo, useCallback } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Box, Icon, Typography } from "@develop/fe-library"

import productCostActions from "actions/productCostActions"

import { isBasSubscriptionExpiredSelector } from "selectors/mainStateSelectors"
import {
  emptyPeriodsStatesSelector,
  formTypeSelector,
  selectedMarketplaceSelector,
} from "selectors/productCostSelectors"

import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "constants/permissions"
import { FORM_TYPES_LOWER_TITLES } from "constants/productCost"

import { RestrictedPrimaryButton } from "components/shared/RestrictedButtons"

const { openCreatePeriodModal, createPeriod } = productCostActions

export const EmptyPeriods = memo(() => {
  const dispatch = useDispatch()

  const {
    formType,
    marketplace_id,
    sku,
    seller_id,
    hasSyncedRepricerWithValue,
    isBasSubscriptionExpired,
  } = useSelector(emptyPeriodsStatesSelector)

  const costTypeTitle = `Please, add new ${FORM_TYPES_LOWER_TITLES[formType]}`

  const addButtonManagePermission = isBasSubscriptionExpired
    ? false
    : permissionKeys.basMyProductsManage

  const addButtonPopoverMessage = isBasSubscriptionExpired
    ? restrictPopoverMessages.action
    : restrictPopoverMessages.create

  const handleCreatePeriod = useCallback(async () => {
    if (hasSyncedRepricerWithValue) {
      dispatch(openCreatePeriodModal())

      return
    }

    const payload = {
      marketplace_id,
      seller_sku: sku,
      seller_id,
      date_start: null,
    }

    await dispatch(createPeriod(payload, null))
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasSyncedRepricerWithValue, marketplace_id, seller_id, sku, formType])

  return (
    <Box
      height="100%"
      flexDirection="column"
      gap="m"
      padding="m"
      align="center"
      justify="center"
    >
      <Icon
        name="icnCalendar"
        size="--icon-size-7"
        color="--color-icon-static"
      />

      <Typography
        variant="--font-headline-2"
        color="--color-text-main"
        textAlign="center"
      >
        {l("No data is found")}
      </Typography>

      <Typography
        variant="--font-body-text-4"
        color="--color-text-main"
        textAlign="center"
      >
        {l(costTypeTitle)}
      </Typography>

      <RestrictedPrimaryButton
        managePermission={addButtonManagePermission}
        popoverMessage={addButtonPopoverMessage}
        onClick={handleCreatePeriod}
      >
        {l("Add new")}
      </RestrictedPrimaryButton>
    </Box>
  )
})
