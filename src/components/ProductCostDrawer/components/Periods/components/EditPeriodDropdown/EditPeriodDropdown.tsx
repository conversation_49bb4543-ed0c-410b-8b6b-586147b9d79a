import React from "react"
import { Box, Dropdown } from "@develop/fe-library"
import { isTouchScreen } from "@develop/fe-library/dist/utils"

import { EditPeriodForm } from "./components/EditPeriodForm"

import { EditPeriodDropdownProps } from "./EditPeriodDropdownTypes"

export const EditPeriodDropdown = ({
  children,
  actionType = "createPeriod",
  period,
}: EditPeriodDropdownProps) => {
  return (
    <Dropdown
      trigger="mouseDown"
      isGlobal
      isVisibilityPassedToMenu
      isHiddenOnObscure={!isTouchScreen()}
      minWidth={280}
      menu={<EditPeriodForm period={period} actionType={actionType} />}
    >
      <Box flexDirection="column" align="flex-end">
        {children}
      </Box>
    </Dropdown>
  )
}
