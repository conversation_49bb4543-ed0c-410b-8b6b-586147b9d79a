import { useCallback, useMemo } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useForm } from "react-hook-form"
import { addDays, format } from "date-fns"
import { DATE_OUTPUT_FORMATS } from "@develop/fe-library/dist/consts"

import { userTimezoneSelector } from "selectors/timezoneSelectors"
import {
  languageSelector,
  translationsSelector,
} from "selectors/mainStateSelectors"

import productCostActions from "actions/productCostActions"

import l from "utils/intl"
import { buildDatePickerLabels } from "utils/buildDatePickerLabels"

import { LOCALES } from "constants/locales"

import { getDefaultDate } from "../../utils"

import {
  EditPeriodFormProps,
  EditPeriodFormValues,
} from "../../EditPeriodFormTypes"

export const useEditPeriodForm = ({
  actionType = "createPeriod",
  period,
  onClose,
}: EditPeriodFormProps) => {
  const dispatch = useDispatch()

  const userTimezone = useSelector(userTimezoneSelector)
  const language = useSelector(languageSelector)
  const { locale } = useSelector(translationsSelector)

  const { id, date_end: dateEnd, date_start: dateStart } = period

  const form = useForm<EditPeriodFormValues>({
    defaultValues: {
      date_start: actionType === "updatePeriod" ? getDefaultDate(period) : null,
    },
  })

  const { handleSubmit: formHandleSubmit, formState, watch } = form

  const date = watch("date_start")

  const { isDirty, isSubmitting } = formState

  const isSubmitButtonDisabled: boolean = isSubmitting || !isDirty || !date

  const items = useMemo(() => {
    const fromDate = dateStart ? addDays(new Date(dateStart), 1) : undefined
    const toDate = dateEnd ? new Date(dateEnd) : undefined

    return [
      {
        type: "date",
        name: "date_start",
        inputProps: {
          label: l("Date"),
          isGlobal: true,
          fromDate,
          toDate,
          timezone: userTimezone,
          labels: buildDatePickerLabels(),
          language: LOCALES[language],
          locale: LOCALES[locale],
        },
        gridItemProps: {
          mSM: 12,
        },
      },
    ]
  }, [dateStart, dateEnd, userTimezone, language, locale])

  const handleSubmit = useCallback(
    formHandleSubmit(async ({ date_start }) => {
      if (!date_start) {
        return
      }

      const payload = {
        id,
        date_start: format(
          date_start,
          DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT_WITH_TIME,
        ),
      }

      await dispatch(productCostActions[actionType](payload, onClose))
    }),
    [dispatch, id, actionType, formHandleSubmit, onClose, userTimezone],
  )

  return {
    form,
    items,
    onSubmit: handleSubmit,
    isSubmitButtonDisabled,
  }
}
