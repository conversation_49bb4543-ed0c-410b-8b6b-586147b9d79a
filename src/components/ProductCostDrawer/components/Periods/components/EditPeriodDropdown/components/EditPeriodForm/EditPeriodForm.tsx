import React from "react"
import { Box, FormItems, Typography } from "@develop/fe-library"

import l from "utils/intl"

import {
  RestrictedButton,
  RestrictedPrimaryButton,
} from "components/shared/RestrictedButtons"

import {
  EditPeriodFormProps,
  EditPeriodFormValues,
} from "./EditPeriodFormTypes"
import { useEditPeriodForm } from "./hooks"

export const EditPeriodForm = ({
  actionType = "createPeriod",
  period,
  onClose,
}: EditPeriodFormProps) => {
  const { form, items, onSubmit, isSubmitButtonDisabled } = useEditPeriodForm({
    actionType,
    period,
    onClose,
  })

  return (
    <Box flexDirection="column" gap="m" padding="m">
      <Typography variant="--font-body-text-6">{l("Choose date")}</Typography>

      <FormItems<EditPeriodFormValues>
        gridContainerProps={{ gap: "m" }}
        // @ts-expect-error
        form={form}
        items={items}
      />

      <Box flexDirection="row" gap="m" justify="end">
        <RestrictedButton icon="icnClose" iconOnly onClick={onClose} />

        <RestrictedPrimaryButton
          icon="icnCheck"
          iconOnly
          disabled={isSubmitButtonDisabled}
          onClick={onSubmit}
        />
      </Box>
    </Box>
  )
}
