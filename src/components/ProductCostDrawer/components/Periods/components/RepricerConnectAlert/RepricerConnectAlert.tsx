import React, { memo } from "react"
import { Alert, Box } from "@develop/fe-library"

import { LINKED_MARKETPLACE_DISABLED_POPOVER } from "components/ProductCostDrawer/constants"
import { RestrictedButton } from "components/shared/RestrictedButtons"

import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "constants/permissions"

import { useRepricerConnectAlert } from "./hooks"

import type { RepricerConnectAlertProps } from "./RepricerConnectAlertTypes"

export const RepricerConnectAlert = memo(
  ({ isDisabled }: RepricerConnectAlertProps) => {
    const { settings, isProductPresentInRepricer, onClick } =
      useRepricerConnectAlert()

    const managePermission: string | boolean = isDisabled
      ? false
      : permissionKeys.basMyProductsManage

    const popoverMessage: string = isDisabled
      ? l(LINKED_MARKETPLACE_DISABLED_POPOVER)
      : restrictPopoverMessages.alter

    return (
      <Box
        display="block"
        hasBorder={{ bottom: true }}
        mSM={{ padding: "m" }}
        tb={{ padding: "l" }}
      >
        <Alert
          alertType="info"
          description={settings.alertDescription}
          message={settings.alertMessage}
          action={
            isProductPresentInRepricer ? (
              <RestrictedButton
                managePermission={managePermission}
                popoverMessage={popoverMessage}
                onClick={onClick}
              >
                {settings.actionText}
              </RestrictedButton>
            ) : null
          }
        />
      </Box>
    )
  },
)
