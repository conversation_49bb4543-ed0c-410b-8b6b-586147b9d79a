import React, { useCallback, useMemo } from "react"
import { useDispatch, useSelector } from "react-redux"

import productCostActions from "actions/productCostActions"

import { repricerConnectAlertStatesSelector } from "selectors/productCostSelectors"

import setConfirm from "utils/confirm"
import l from "utils/intl"
import ln from "utils/localeNumber"

import {
  FORM_TYPES,
  FORM_TYPES_LOWER_TITLES,
  PERIOD_SOURCES,
} from "constants/productCost"

const { synchronizeWithRepricer } = productCostActions

export const useRepricerConnectAlert = () => {
  const dispatch = useDispatch()

  const {
    formType,
    id,
    isEnabledSyncWithRepricer,
    currencyCode,
    repricerId,
    repricerIsDeleted,
    productCostValue,
    currentPeriodSource,
    isProductInRepricer,
  } = useSelector(repricerConnectAlertStatesSelector)

  const formattedProductCostValue: string =
    productCostValue !== null
      ? ln(productCostValue, 2, {
          currency: currencyCode,
        })
      : l("N/A")

  const totalValue: string =
    formType === FORM_TYPES.vat
      ? `${productCostValue}%`
      : formattedProductCostValue

  const isSourceRepricer: boolean =
    currentPeriodSource === PERIOD_SOURCES.repricer
  const isRepricerValueNotProvided: boolean =
    !!isEnabledSyncWithRepricer && !isSourceRepricer
  const isRepricerConnected: boolean =
    !!productCostValue && !!isEnabledSyncWithRepricer && isSourceRepricer
  const isProductPresentInRepricer: boolean =
    !repricerIsDeleted && isProductInRepricer

  const handleClick = useCallback(() => {
    const payload = {
      id,
      is_enabled_sync_with_repricer: !isEnabledSyncWithRepricer,
    }

    const handleOk = (): void => {
      dispatch(synchronizeWithRepricer(payload))
    }

    setConfirm({
      title: l("Are you sure you want to proceed?"),
      message: l(
        !isEnabledSyncWithRepricer
          ? "All costs for this product’s marketplace will be provided by Repricer."
          : "All costs for this product’s marketplace will no longer be provided by Repricer.",
      ),
      onOk: handleOk,
      cancelText: l("Cancel"),
      okText: l("Confirm"),
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    id,
    isEnabledSyncWithRepricer,
    isProductInRepricer,
    repricerId,
    repricerIsDeleted,
  ])

  const settings = useMemo(() => {
    const buttonStates = {
      connect: l("Connect"),
      disconnect: l("Disconnect"),
    }

    if (!isProductPresentInRepricer) {
      return {
        alertMessage: l("The product is not present in Repricer"),
        alertDescription: "",
        actionText: isEnabledSyncWithRepricer
          ? buttonStates.disconnect
          : buttonStates.connect,
      }
    }

    if (isRepricerConnected) {
      return {
        alertMessage: l("The current value is <b>{value}</b>.", {
          value: totalValue,
          b: (text) => <strong>{text}</strong>,
        }),
        alertDescription: l(
          `Disconnect synchronization with Repricer to set a custom ${FORM_TYPES_LOWER_TITLES[formType]}.`,
        ),
        actionText: buttonStates.disconnect,
      }
    }

    if (!isEnabledSyncWithRepricer && repricerId) {
      return {
        alertMessage: l("The product is present in Repricer."),
        alertDescription: l(
          "Synchronization is available for this marketplace.",
        ),
        actionText: buttonStates.connect,
      }
    }

    if (isRepricerValueNotProvided) {
      return {
        alertMessage: l("The value is not provided."),
        alertDescription: l("Please set the value in Repricer."),
        actionText: isEnabledSyncWithRepricer
          ? buttonStates.disconnect
          : buttonStates.connect,
      }
    }

    return {
      alertMessage: l("Synchronize with Repricer to see the current value"),
      alertDescription: "",
      actionText: buttonStates.connect,
    }
  }, [
    totalValue,
    formType,
    isEnabledSyncWithRepricer,
    isProductPresentInRepricer,
    isRepricerConnected,
    isRepricerValueNotProvided,
    repricerId,
  ])

  return {
    settings,
    isProductPresentInRepricer,
    onClick: handleClick,
  }
}
