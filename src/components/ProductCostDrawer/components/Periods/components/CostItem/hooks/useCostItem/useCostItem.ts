import { useCallback, useMemo } from "react"
import { useDispatch } from "react-redux"

import setConfirm from "utils/confirm"
import ln from "utils/localeNumber"
import l from "utils/intl"

import productCostActions from "actions/productCostActions"

import {
  PERIOD_SOURCES,
  FORM_TYPES_LOWER_TITLES,
  FORM_TYPES_TITLES,
  FORM_TYPES,
} from "constants/productCost"
import { permissionKeys, restrictPopoverMessages } from "constants/permissions"

const { startEditPeriod, deletePeriod } = productCostActions

export const useCostItem = ({
  period,
  formType,
  selectedPeriodId,
  currencyCode,
  isEnabledSyncWithRepricer,
  productCostValue,
  isBasSubscriptionExpired,
}) => {
  const dispatch = useDispatch()

  const { id, amount_total, is_current, source } = period

  const { title, value } = useMemo(() => {
    const mapper = {
      vat: {
        title: FORM_TYPES_TITLES.vat,
        value: `${ln(amount_total || 0, null)}%`,
      },
      default: {
        title: "Total",
        value: ln(amount_total || 0, 2, { currency: currencyCode }),
      },
    }

    if (formType === "vat") {
      return mapper.vat
    }

    return mapper.default
  }, [amount_total, currencyCode, formType])

  const handleEdit = useCallback(
    () => {
      const { id } = period

      dispatch(startEditPeriod(id))
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [period.id],
  )

  const handleDelete = useCallback(
    (event) => {
      event.stopPropagation()

      const shortFormTypeNameLower = FORM_TYPES_LOWER_TITLES[formType]

      const handleOk = (): void => {
        dispatch(deletePeriod(id))
      }

      setConfirm({
        title: l(`Delete ${shortFormTypeNameLower} period`),
        message: l(`Do you really want to delete this item?`),
        onOk: handleOk,
        cancelText: l("Cancel"),
        okText: l("Confirm"),
      })
    },
    [id, formType],
  )

  const isCostItemHidden: boolean =
    !!is_current &&
    !!isEnabledSyncWithRepricer &&
    !!productCostValue &&
    source === PERIOD_SOURCES.repricer

  const isVatEditing: boolean =
    selectedPeriodId === id && formType === FORM_TYPES.vat

  const deletePermission: string | false = isBasSubscriptionExpired
    ? false
    : permissionKeys.basMyProductsManage

  const deleteRestrictedPopoverMessage: string = isBasSubscriptionExpired
    ? restrictPopoverMessages.action
    : restrictPopoverMessages.alter

  return {
    title,
    value,
    isVatEditing,
    onEdit: handleEdit,
    onDelete: handleDelete,
    isCostItemHidden,
    deletePermission,
    deleteRestrictedPopoverMessage,
  }
}
