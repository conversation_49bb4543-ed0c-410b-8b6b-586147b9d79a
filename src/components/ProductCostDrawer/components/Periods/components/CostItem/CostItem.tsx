import React from "react"
import { Box, Typography } from "@develop/fe-library"
import { Colors } from "@develop/fe-library/dist/lib/types"

import { LINKED_MARKETPLACE_DISABLED_POPOVER } from "components/ProductCostDrawer/constants"
import { RestrictedIconPopover } from "components/shared/RestrictedIconPopover"

import l from "utils/intl"

import { restrictPopoverMessages } from "constants/permissions"

import { EditVatForm } from "./components"

import { useCostItem } from "./hooks"

export const CostItem = ({
  isSinglePeriod,
  period,
  formType,
  selectedPeriodId,
  currencyCode,
  isEnabledSyncWithRepricer,
  productCostValue,
  isBasSubscriptionExpired,
  isDisabled,
}) => {
  const {
    title,
    value,
    isVatEditing,
    onEdit,
    onDelete,
    isCostItemHidden,
    deletePermission,
    deleteRestrictedPopoverMessage,
  } = useCostItem({
    period,
    formType,
    selectedPeriodId,
    currencyCode,
    isEnabledSyncWithRepricer,
    productCostValue,
    isBasSubscriptionExpired,
  })

  if (isCostItemHidden) {
    return null
  }

  if (isVatEditing) {
    return <EditVatForm period={period} />
  }

  const titleColor: Colors = isDisabled
    ? "--color-text-disable"
    : "--color-text-second"

  const valueColor: Colors = isDisabled
    ? "--color-text-disable"
    : "--color-text-main"

  const iconColor: Colors = isDisabled
    ? "--color-icon-disable"
    : "--color-icon-clickable"

  return (
    <Box
      hasBorder
      align="center"
      backgroundColor="--color-main-background"
      borderRadius="--border-radius"
      height={60}
      justify="space-between"
      padding="l"
      width="100%"
    >
      <Box align="center" gap="m">
        <Typography color={titleColor} variant="--font-body-text-7">
          {l(title)}:
        </Typography>

        <Typography color={valueColor} variant="--font-body-text-5">
          {value}
        </Typography>
      </Box>

      <Box align="center" gap="m">
        <RestrictedIconPopover
          color={iconColor}
          content={l("Edit")}
          managePermission={!isBasSubscriptionExpired && !isDisabled}
          name="icnEdit"
          placement="top"
          size="--icon-size-3"
          popoverMessage={
            isDisabled
              ? l(LINKED_MARKETPLACE_DISABLED_POPOVER)
              : restrictPopoverMessages.action
          }
          onClick={onEdit}
        />

        {!isSinglePeriod ? (
          <RestrictedIconPopover
            color={iconColor}
            content={l("Delete")}
            managePermission={deletePermission && !isDisabled}
            name="icnDeleteOutlined"
            popoverMessage={deleteRestrictedPopoverMessage}
            popoverPlacement="top"
            size="--icon-size-3"
            onClick={onDelete}
          />
        ) : null}
      </Box>
    </Box>
  )
}
