import { useMemo } from "react"
import { useDispatch } from "react-redux"
import { useForm } from "react-hook-form"
import * as yup from "yup"
import { yupResolver } from "@hookform/resolvers/yup"
import { buildFormSubmitFailureCallback } from "@develop/fe-library/dist/utils"

import productCostActions from "actions/productCostActions"

import { buildErrorMessagesMapper } from "utils/formHelpers"
import l from "utils/intl"

import type { ProductCostItem } from "types/Models"
import type {
  EditVatFormProps,
  EditVatFormValues,
} from "../../EditVatFormTypes"

const { createProductCost, updateProductCost, endEditPeriod } =
  productCostActions

const buildSchema = (): yup.AnyObjectSchema => {
  const { required: requiredErrorMessage } = buildErrorMessagesMapper()

  return yup
    .object()
    .shape({
      amount_vat: yup
        .string()
        .required(requiredErrorMessage)
        .test(
          "max",
          l("VAT (%) must be no greater than 100."),
          function (value) {
            if (!value) {
              return true
            }
            const numValue = parseFloat(value)
            return !Number.isNaN(numValue) && numValue <= 100
          },
        ),
    })
    .required()
}

export const useEditVatForm = ({ period }: EditVatFormProps) => {
  const dispatch = useDispatch()

  const firstItem: ProductCostItem | null = period.items?.[0] || null

  const form = useForm<EditVatFormValues>({
    resolver: yupResolver(buildSchema()),
    defaultValues: {
      amount_vat: firstItem ? firstItem.amount_total : "",
    },
  })

  const { handleSubmit: formHandleSubmit, formState, setError } = form

  const { isSubmitting } = formState

  const items = useMemo(() => {
    const handleClose = () => {
      dispatch(endEditPeriod())
    }

    const handleSubmit = formHandleSubmit(async ({ amount_vat }) => {
      const failureCallback = buildFormSubmitFailureCallback<EditVatFormValues>(
        {
          setError,
        },
      )

      const commonPayload = {
        amount_total: amount_vat,
        amount_per_unit: amount_vat,
        marketplace_amount_per_unit: amount_vat,
      }

      if (!firstItem?.id) {
        await dispatch(
          createProductCost(commonPayload, handleClose, failureCallback),
        )
      } else {
        const payload = {
          ...firstItem,
          ...commonPayload,
        }

        await dispatch(updateProductCost(payload, handleClose, failureCallback))
      }
    })

    return [
      {
        type: "numeric",
        name: "amount_vat",
        inputProps: {
          label: l("Confirm VAT, %"),
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
          isNegativeAllowed: false,
        },
        gridItemProps: { mSM: true },
      },
      {
        type: "action",
        label: null,
        actionProps: {
          icon: "icnCheck",
          iconOnly: true,
          onClick: handleSubmit,
          disabled: isSubmitting,
        },
      },
      {
        type: "action",
        label: null,
        actionProps: {
          icon: "icnClose",
          iconOnly: true,
          variant: "secondary",
          onClick: handleClose,
          disabled: isSubmitting,
        },
      },
    ]
  }, [isSubmitting, firstItem, formHandleSubmit, setError])

  return {
    form,
    items,
  }
}
