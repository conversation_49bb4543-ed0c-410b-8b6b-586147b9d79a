import { useCallback, useState } from "react"
import { useSelector } from "react-redux"
import { format } from "date-fns"
import type { Colors } from "@develop/fe-library/dist/lib/types"

import { languageSelector } from "selectors/mainStateSelectors"

import { LINKED_MARKETPLACE_DISABLED_POPOVER } from "components/ProductCostDrawer/constants"

import l from "utils/intl"

import { LOCALES } from "constants/locales"
import { restrictPopoverMessages } from "constants/permissions"
import {
  PERIOD_SOURCES,
  TIMELINE_COST_DATE_FORMAT_DATE_FNS,
} from "constants/productCost"

import { UseTimelineCostLabelParams } from "./UseTimelineCostLabelTypes"

export const useTimelineCostLabel = ({
  period,
  isEnabledSyncWithRepricer,
  isDisabled,
}: UseTimelineCostLabelParams) => {
  const [isHovered, setIsHovered] = useState(false)

  const { date_start: dateStart, is_current: isCurrent, source } = period

  const language = useSelector(languageSelector)

  const isRepricerPeriodWithSync: boolean =
    isEnabledSyncWithRepricer && source === PERIOD_SOURCES.repricer
  const isEditIconVisible: boolean = isRepricerPeriodWithSync && isCurrent
  const dateStartFormatted: string = dateStart
    ? format(new Date(dateStart), TIMELINE_COST_DATE_FORMAT_DATE_FNS, {
        locale: LOCALES[language],
      })
    : ""

  const handlePointerEnter = useCallback((): void => {
    setIsHovered(true)
  }, [])

  const handlePointerLeave = useCallback((): void => {
    setIsHovered(false)
  }, [])

  const textColor: Colors = isDisabled
    ? "--color-text-disable"
    : "--color-text-second"

  let iconColor: Colors

  if (isDisabled) {
    iconColor = "--color-icon-disable"
  } else if (isHovered) {
    iconColor = "--color-icon-active"
  } else {
    iconColor = "--color-icon-clickable"
  }

  const popoverMessage: string = isDisabled
    ? l(LINKED_MARKETPLACE_DISABLED_POPOVER)
    : restrictPopoverMessages.action

  return {
    dateStartFormatted,
    isEditIconVisible,
    isHovered,
    onPointerEnter: handlePointerEnter,
    onPointerLeave: handlePointerLeave,
    textColor,
    iconColor,
    popoverMessage,
  }
}
