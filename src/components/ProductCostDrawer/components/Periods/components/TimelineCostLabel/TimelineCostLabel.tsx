import React from "react"
import { Box, Typography } from "@develop/fe-library"

import { RestrictedIconPopover } from "components/shared/RestrictedIconPopover"

import l from "utils/intl"

import { useTimelineCostLabel } from "./hooks"

import { EditPeriodDropdown } from "../EditPeriodDropdown"

import { TimelineCostLabelProps } from "./TimelineCostLabelTypes"

export const TimelineCostLabel = ({
  period,
  isEnabledSyncWithRepricer,
  isBasSubscriptionExpired,
  isDisabled,
}: TimelineCostLabelProps) => {
  const {
    dateStartFormatted,
    isEditIconVisible,
    onPointerEnter,
    onPointerLeave,
    textColor,
    iconColor,
    popoverMessage,
  } = useTimelineCostLabel({
    period,
    isEnabledSyncWithRepricer,
    isDisabled,
  })

  return (
    <EditPeriodDropdown actionType="updatePeriod" period={period}>
      <Box
        cursor="pointer"
        gap="m"
        width="100%"
        onPointerEnter={onPointerEnter}
        onPointerLeave={onPointerLeave}
      >
        <Typography color={textColor} variant="--font-body-text-7">
          {dateStartFormatted}
        </Typography>

        {isEditIconVisible ? null : (
          <RestrictedIconPopover
            color={iconColor}
            content={l("Edit")}
            managePermission={!isBasSubscriptionExpired && !isDisabled}
            name="icnEdit"
            placement="top"
            popoverMessage={popoverMessage}
            size="--icon-size-3"
          />
        )}
      </Box>
    </EditPeriodDropdown>
  )
}
