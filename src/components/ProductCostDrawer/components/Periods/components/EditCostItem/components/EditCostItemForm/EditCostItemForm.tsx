import React from "react"
import { Empty } from "antd"
import {
  Box,
  Button,
  Icon,
  IconPopover,
  SimpleTable,
  Typography,
} from "@develop/fe-library"

import { RestrictedButtonPopover } from "components/shared/RestrictedButtonPopover"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import { useEditCostItemForm } from "./hooks"

import type { EditCostItemFormProps, Row } from "./EditCostItemFormTypes"

export const EditCostItemForm = ({ items }: EditCostItemFormProps) => {
  const {
    dateStartFormatted,
    dateEndFormatted,
    amountTotalFormatted,
    rows,
    columns,
    isSubmitting,
    isSaveButtonDisabled,
    duplicateErrorText,
    cancelButtonText,
    onSubmit,
    onCancel,
  } = useEditCostItemForm({ items })

  return (
    <Box flex={1} flexDirection="column">
      <Box flex={1} flexDirection="column">
        <Box
          gap="l"
          hasBorder={{ bottom: true }}
          justify="space-between"
          padding="m l"
        >
          <Box gap="l">
            <IconPopover
              content={l("Back")}
              name="icnChevronLeft"
              size="--icon-size-5"
              onClick={onCancel}
            />
            <Icon
              color="var(--color-icon-static)"
              name="icnCalendar"
              size="--icon-size-4"
            />

            <Box align="center" gap="s">
              {dateStartFormatted} - {dateEndFormatted}
            </Box>
          </Box>

          <Box gap="s">
            <Typography
              color="--color-text-second"
              variant="--font-body-text-7"
            >
              {l("Total")}:
            </Typography>
            <Typography variant="--font-body-text-5">
              {amountTotalFormatted}
            </Typography>
          </Box>
        </Box>

        <Box flex={1} overflowX="auto" width="100%">
          <Box flexDirection="column" minWidth={768} width="100%">
            <SimpleTable<Row>
              columns={columns}
              data={rows}
              hasOuterBorder={false}
            />
            {!checkIsArray(rows) ? (
              <Box align="center" flex={1} justify="center" width="100%">
                <Empty
                  description={l("No data")}
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              </Box>
            ) : null}
          </Box>
        </Box>
      </Box>
      <Box
        display="grid"
        gap="m"
        hasBorder={{ top: true }}
        mSM={{
          gridTemplateColumns: "0 1fr 1fr",
          padding: "m",
        }}
        mXL={{
          gridTemplateColumns: "1fr min-content min-content",
          padding: "m l",
        }}
      >
        <div />
        <Button disabled={isSubmitting} variant="secondary" onClick={onCancel}>
          {cancelButtonText}
        </Button>
        <RestrictedButtonPopover
          managePermission={!isSaveButtonDisabled}
          popoverMessage={duplicateErrorText}
          popoverPlacement="top"
          onClick={onSubmit}
        >
          {l("Save")}
        </RestrictedButtonPopover>
      </Box>
    </Box>
  )
}
