import React, { useCallback } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useForm } from "react-hook-form"
import {
  Box,
  Button,
  Dropdown,
  IconPopover,
  InputAddon,
  InputGroup,
  NumericInput,
  Typography,
} from "@develop/fe-library"
import { getCurrencySymbol } from "@develop/fe-library/dist/utils"

import { selectedMarketplaceSelector } from "selectors/productCostSelectors"
import { currenciesSelector } from "selectors/mainStateSelectors"

import productCostActions from "actions/productCostActions"

import l from "utils/intl"
import ln from "utils/localeNumber"
import { isRTL } from "utils/isRTL"

import { CurrencyBox, CurrencyMenu } from "./components"

import { CurrencyExchangeRateProps } from "../../CurrencyExchangeRateTypes"

const { openDisclaimerModal } = productCostActions

export const CurrencyExchangeRateForm = ({
  getInitialValues,
  onSave,
  onClose,
}: CurrencyExchangeRateProps) => {
  const dispatch = useDispatch()

  // @ts-expect-error
  const { currency_code } = useSelector(selectedMarketplaceSelector)
  const { currencies } = useSelector(currenciesSelector)

  const initialValues = getInitialValues()

  const { watch, setValue } = useForm({
    defaultValues: initialValues,
  })

  const { calculatorCurrencyId, calculatorValue } = watch()

  const marketplaceCurrency = currencies.find(({ id }) => id === currency_code)
  const calculatorCurrency = currencies.find(
    ({ id }) => id === calculatorCurrencyId,
  )

  const handleShowDisclaimerModal = useCallback((): void => {
    dispatch(openDisclaimerModal())
  }, [])

  const handleChangeCalculatorCurrency = useCallback((value: number): void => {
    setValue("calculatorCurrencyId", value)

    const calculatorCurrencyNew = currencies.find(({ id }) => id === value)

    const isSameCurrencies: boolean = value === marketplaceCurrency?.id

    if (isSameCurrencies) {
      setValue("calculatorValue", 1)
      return
    }

    const calculatorCurrencyRate: number =
      parseFloat(calculatorCurrencyNew?.rate) || 1

    const marketplaceCurrencyRate: number =
      parseFloat(marketplaceCurrency?.rate) || 1

    const valueInEuro = 1 / calculatorCurrencyRate
    const resultRate = valueInEuro * marketplaceCurrencyRate

    setValue("calculatorValue", Math.floor(resultRate * 1000) / 1000)
  }, [])

  const handleChangeCalculatorValue = useCallback((value: number): void => {
    setValue("calculatorValue", value)
  }, [])

  const handleSave = useCallback(() => {
    onSave?.({ calculatorCurrencyId, calculatorValue })
    onClose?.()
  }, [onSave, calculatorCurrencyId, calculatorValue])

  const calculatorValueFormatted = ln(1, 2, {
    currency: calculatorCurrency.title,
  })

  const calculatorValueDir = isRTL(
    getCurrencySymbol({ id: calculatorCurrency.id }),
  )
    ? "rtl"
    : "ltr"

  const marketplaceValueFormatted = ln(calculatorValue, 3, {
    currency: marketplaceCurrency.title,
  })

  const marketplaceValueDir = isRTL(
    getCurrencySymbol({ id: marketplaceCurrency.id }),
  )
    ? "rtl"
    : "ltr"

  return (
    <Box margin="m" gap="m" flexDirection="column" width={178}>
      <Typography variant="--font-body-text-6">
        {l("Edit currency exchange rate")}
      </Typography>

      <InputGroup>
        <Dropdown
          isGlobal
          isVisibilityPassedToMenu
          menu={
            <CurrencyMenu
              calculatorCurrencyId={calculatorCurrencyId}
              currencies={currencies}
              handleChangeCalculatorCurrency={handleChangeCalculatorCurrency}
            />
          }
        >
          <CurrencyBox
            id={calculatorCurrency?.id}
            title={calculatorCurrency?.title}
            hasDropdown
          />
        </Dropdown>

        <NumericInput
          value={1}
          minimumFractionDigits={2}
          maximumFractionDigits={6}
          isDisabled
        />
      </InputGroup>

      <InputGroup>
        <CurrencyBox
          id={marketplaceCurrency?.id}
          title={marketplaceCurrency?.title}
        />

        <NumericInput
          value={calculatorValue}
          onChange={handleChangeCalculatorValue}
          minimumFractionDigits={2}
          maximumFractionDigits={6}
        />

        <InputAddon>
          <Box minWidth={20} align="center" justify="center">
            <IconPopover
              name="icnInfoCircle"
              size="--icon-size-3"
              content={
                <Box gap="s" flexDirection="column">
                  <Typography
                    variant="--font-body-text-7"
                    color="--color-text-main"
                  >
                    <span dir={calculatorValueDir}>
                      {calculatorValueFormatted}
                    </span>{" "}
                    ={" "}
                    <span dir={marketplaceValueDir}>
                      {marketplaceValueFormatted}
                    </span>
                  </Typography>
                  <Typography
                    className="rate-disclaimer"
                    onClick={handleShowDisclaimerModal}
                    variant="--font-body-text-7"
                    color="--color-text-link"
                  >
                    {l("Disclaimer")}
                  </Typography>
                </Box>
              }
            />
          </Box>
        </InputAddon>
      </InputGroup>

      <Box gap="m" justify="end">
        <Button
          variant="secondary"
          iconOnly
          icon="icnClose"
          onClick={onClose}
        />
        <Button iconOnly icon="icnCheck" onClick={handleSave} />
      </Box>
    </Box>
  )
}
