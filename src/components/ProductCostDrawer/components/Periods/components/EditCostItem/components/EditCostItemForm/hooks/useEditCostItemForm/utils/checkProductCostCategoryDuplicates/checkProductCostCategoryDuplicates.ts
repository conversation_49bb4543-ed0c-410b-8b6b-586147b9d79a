import { CheckProductCostCategoryDuplicates } from "./checkProductCostCategoryDuplicatesTypes"

export const checkProductCostCategoryDuplicates: CheckProductCostCategoryDuplicates =
  (productCostCategory) => {
    const selectedProductCostCategoryIds = productCostCategory.map(
      (cost) => cost.product_cost_category_id,
    )

    const hasProductCostDuplicateIds = selectedProductCostCategoryIds.some(
      (cost, index) => {
        return selectedProductCostCategoryIds.indexOf(cost) !== index
      },
    )

    return {
      selectedProductCostCategoryIds,
      hasProductCostDuplicateIds,
    }
  }
