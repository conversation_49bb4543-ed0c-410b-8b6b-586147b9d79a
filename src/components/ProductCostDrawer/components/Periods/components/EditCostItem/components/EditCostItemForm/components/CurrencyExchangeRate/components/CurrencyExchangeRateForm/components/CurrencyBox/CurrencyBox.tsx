import React, { forwardRef } from "react"
import { Box, Flag, Icon, InputAddon, Typography } from "@develop/fe-library"
import type { IconNames } from "@develop/fe-library"

import { CURRENCIES_NAMES } from "constants/currencies"

import { CurrencyBoxProps } from "./CurrencyBoxTypes"

export const CurrencyBox = forwardRef<HTMLDivElement, CurrencyBoxProps>(
  ({ id, title, hasDropdown, isPopoverVisible, onClick, onMouseDown }, ref) => {
    if (!id || !title) {
      return null
    }

    const iconName: IconNames = isPopoverVisible
      ? "icnChevronUp"
      : "icnChevronDown"
    const cursor = hasDropdown ? "pointer" : "default"

    const flagLocale = CURRENCIES_NAMES[id].countryCode.toLowerCase()

    return (
      <InputAddon ref={ref} onClick={onClick} onMouseDown={onMouseDown}>
        <Box
          gap="s"
          height="100%"
          width="100%"
          minWidth={70}
          align="center"
          cursor={cursor}
        >
          <Flag
            size={18}
            locale={flagLocale}
            borderRadius="--border-radius-circle"
          />

          <Typography variant="--font-body-text-7" color="--color-text-main">
            {title}
          </Typography>

          {hasDropdown ? <Icon name={iconName} size="--icon-size-2" /> : null}
        </Box>
      </InputAddon>
    )
  },
)
