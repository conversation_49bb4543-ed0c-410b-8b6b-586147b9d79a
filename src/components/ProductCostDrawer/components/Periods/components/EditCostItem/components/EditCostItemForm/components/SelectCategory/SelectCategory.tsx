import React, { <PERSON><PERSON><PERSON>, useState } from "react"
import { useC<PERSON>roller } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { Box, Select, TextInput } from "@develop/fe-library"

import productCostActions from "actions/productCostActions"

import {
  categoriesSelector,
  formTypeSelector,
} from "selectors/productCostSelectors"

import { RestrictedButtonPopover } from "components/shared/RestrictedButtonPopover"
import { RestrictedIconPopover } from "components/shared/RestrictedIconPopover"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "constants/permissions"
import {
  FORM_TYPES_LOWER_TITLES,
  SALES_CATEGORY_IDS,
} from "constants/productCost"

const { createCategory, deleteCategory } = productCostActions

export const SelectCategory = ({
  name,
  control,
  onChange,
  duplicateCostIds,
}) => {
  const dispatch = useDispatch()

  const formType = useSelector(formTypeSelector)
  const categories = useSelector(categoriesSelector)

  const [newCostTypeName, setNewCostTypeName] = useState("")
  const [textInputError, setTextInputError] = useState("")

  const { field } = useController({
    name,
    control,
  })

  const shortFormTypeNameLower = FORM_TYPES_LOWER_TITLES[formType]

  const categoriesOptions = checkIsArray(categories)
    ? categories.map(({ id: categoryId, is_default, is_editable, name }) => {
        const label = is_default || !is_editable ? l(name) : name
        const isDisabled = duplicateCostIds.includes(categoryId)

        return {
          value: categoryId,
          label: `${label}`,
          disabled: isDisabled,
        }
      })
    : []

  const handleChangeValue = (value: string | number): void => {
    field.onChange(value)
    onChange(value)
  }

  const changeTextValueHandler = (value: string): void => {
    textInputError && setTextInputError("")
    setNewCostTypeName(value)
  }

  const handleCreateCategory = (): void => {
    const payload = {
      name: newCostTypeName,
      sales_category_id: SALES_CATEGORY_IDS[formType],
    }

    const categoryNames: string[] = checkIsArray(categories)
      ? categories.map(({ name }) => name.toLowerCase())
      : []

    const isSameCategoryName: boolean = categoryNames.includes(
      payload?.name.toLowerCase(),
    )

    if (isSameCategoryName) {
      setTextInputError(l("The cost type of this name already exists"))

      return
    }

    dispatch(createCategory(payload))
  }

  const buildHandleDeleteCategory =
    (id: number) =>
    (event: MouseEvent): void => {
      event.stopPropagation()

      dispatch(deleteCategory(id))
    }

  return (
    <Select
      isFullWidth
      isGlobal
      options={categoriesOptions}
      value={field.value}
      renderDropdownMenu={({ menu }) => {
        return (
          <Box flexDirection="column">
            <Box gap="m" padding="m">
              <TextInput
                errorMessage={textInputError}
                label={l("Cost type")}
                value={newCostTypeName}
                onChange={changeTextValueHandler}
              />

              <RestrictedButtonPopover
                iconOnly
                content={l(`Add new ${shortFormTypeNameLower} type`)}
                icon="icnPlus"
                managePermission={permissionKeys.basMyProductsManage}
                popoverMessage={restrictPopoverMessages.alter}
                variant="secondary"
                onClick={handleCreateCategory}
              />
            </Box>
            {menu}
          </Box>
        )
      }}
      renderOption={({ option }) => {
        const { id, is_deletable, is_default } =
          categories?.find(({ id }) => id === option.value) || {}

        if (!id) {
          return null
        }

        const popoverContentMessage: string = !is_deletable
          ? l(`This ${shortFormTypeNameLower} type is currently being used`)
          : l(`Delete ${shortFormTypeNameLower} type`)

        return (
          <Box gap="m" justify="space-between" width="100%">
            {option.label}
            {!is_default ? (
              <RestrictedIconPopover
                content={popoverContentMessage}
                isDisabled={!is_deletable}
                managePermission={permissionKeys.basMyProductsManage}
                name="icnDeleteOutlined"
                popoverMessage={restrictPopoverMessages.alter}
                size="--icon-size-3"
                onClick={buildHandleDeleteCategory(id)}
              />
            ) : null}
          </Box>
        )
      }}
      onChangeValue={handleChangeValue}
    />
  )
}
