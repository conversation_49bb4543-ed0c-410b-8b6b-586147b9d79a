import React from "react"

import { CurrencyExchangeRateForm } from "./components"

import { CurrencyExchangeRateProps } from "./CurrencyExchangeRateTypes"

export const CurrencyExchangeRate = ({
  isVisible,
  getInitialValues,
  onSave,
  onClose,
}: CurrencyExchangeRateProps) => {
  if (!isVisible) {
    return null
  }

  return (
    <CurrencyExchangeRateForm
      getInitialValues={getInitialValues}
      onSave={onSave}
      onClose={onClose}
    />
  )
}
