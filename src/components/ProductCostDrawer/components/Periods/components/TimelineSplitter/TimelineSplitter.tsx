import React from "react"
import { Colors } from "@develop/fe-library/dist/lib/types"

import { LINKED_MARKETPLACE_DISABLED_POPOVER } from "components/ProductCostDrawer/constants"
import { RestrictedIconPopover } from "components/shared/RestrictedIconPopover"

import l from "utils/intl"

import { restrictPopoverMessages } from "constants/permissions"
import { PERIOD_SOURCES } from "constants/productCost"

import { EditPeriodDropdown } from "../EditPeriodDropdown"

import { TimelineSplitterProps } from "./TimelineSplitterTypes"

export const TimelineSplitter = ({
  period,
  isBasSubscriptionExpired,
  isEnabledSyncWithRepricer,
  isDisabled,
}: TimelineSplitterProps) => {
  const { is_current: isCurrent, source } = period

  const isSourceRepricer = source === PERIOD_SOURCES.repricer

  const isSplitterHidden: boolean =
    isCurrent && isEnabledSyncWithRepricer && isSourceRepricer

  if (isSplitterHidden) {
    return null
  }

  const color: Colors = isDisabled
    ? "--color-icon-disable"
    : "--color-icon-clickable"

  return (
    <EditPeriodDropdown actionType="createPeriod" period={period}>
      <RestrictedIconPopover
        isHovered
        color={color}
        content={l("Split period")}
        managePermission={!isBasSubscriptionExpired && !isDisabled}
        name="icnSplitRows"
        placement="top"
        popoverMessage={
          isDisabled
            ? l(LINKED_MARKETPLACE_DISABLED_POPOVER)
            : restrictPopoverMessages.action
        }
      />
    </EditPeriodDropdown>
  )
}
