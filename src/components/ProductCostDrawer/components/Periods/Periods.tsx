import React, { memo } from "react"
import { Box, Button, Icon, Skeleton, Timeline } from "@develop/fe-library"

import l from "utils/intl"

import {
  CostItem,
  EditCostItem,
  EmptyPeriods,
  RepricerConnectAlert,
  TimelineCostLabel,
  TimelineSplitter,
} from "./components"

import { usePeriods } from "./hooks"

import { PERIODS_LOADING } from "./constants"

import type { ProductCostPeriod } from "types/Models/ProductCostPeriod"

import type { LoadingPeriodItem, PeriodsProps } from "./PeriodsTypes"

export const Periods = memo(({ onClose }: PeriodsProps) => {
  const {
    isProductCostsEditFormVisible,
    isSinglePeriod,
    isEmptyProductPeriods,
    isLoading,
    isEnabledSyncWithRepricer,
    periods,
    formType,
    selectedPeriodId,
    currencyCode,
    productCostValue,
    isBasSubscriptionExpired,
    gridTemplateColumns,
    isMobile,
    isRepricerAvailable,
    isDisabled,
  } = usePeriods()

  if (isLoading) {
    const renderLeftSkeletonWidth = isMobile ? "40px" : "100px"
    const renderRightSkeletonWidth = isMobile ? "50px" : "100px"
    const alertSkeletonPadding = isMobile ? "m" : "l"

    return (
      <Box flex={1} flexDirection="column">
        <Box
          display="block"
          hasBorder={{ bottom: true }}
          padding={alertSkeletonPadding}
        >
          <Skeleton height="40px" width="100%" />
        </Box>

        <Box flexBasis="1px" flexGrow={1} overflow="auto">
          <Timeline<LoadingPeriodItem>
            gridTemplateColumns={gridTemplateColumns}
            items={PERIODS_LOADING}
            renderSplitter={() => <Icon name="icnSplitRows" />}
            renderLeft={(item) => {
              if (!item.hasLeftItem) {
                return null
              }

              return <Skeleton height="20px" width={renderLeftSkeletonWidth} />
            }}
            renderRight={() => (
              <Box
                hasBorder
                align="center"
                backgroundColor="--color-main-background"
                borderRadius="--border-radius"
                height={60}
                justify="space-between"
                padding="l"
                width="100%"
              >
                <Skeleton height="20px" width={renderRightSkeletonWidth} />
                <Skeleton height="20px" width="20px" />
              </Box>
            )}
          />
        </Box>
      </Box>
    )
  }

  if (isEmptyProductPeriods) {
    return (
      <Box flex={1} flexDirection="column">
        {isRepricerAvailable ? (
          <RepricerConnectAlert isDisabled={isDisabled} />
        ) : null}

        <EmptyPeriods />
      </Box>
    )
  }

  return (
    <Box flex={1} flexDirection="column">
      {isRepricerAvailable ? (
        <RepricerConnectAlert isDisabled={isDisabled} />
      ) : null}

      {isProductCostsEditFormVisible ? (
        <EditCostItem />
      ) : (
        <>
          <Box flexBasis="1px" flexGrow={1} overflow="auto">
            <Timeline<ProductCostPeriod>
              gridTemplateColumns={gridTemplateColumns}
              items={periods}
              renderLeft={(period) => {
                if (!period.date_start) {
                  return null
                }

                return (
                  <TimelineCostLabel
                    isBasSubscriptionExpired={isBasSubscriptionExpired}
                    isDisabled={isDisabled}
                    isEnabledSyncWithRepricer={isEnabledSyncWithRepricer}
                    period={period}
                  />
                )
              }}
              renderRight={(period) => (
                <CostItem
                  currencyCode={currencyCode}
                  formType={formType}
                  isBasSubscriptionExpired={isBasSubscriptionExpired}
                  isDisabled={isDisabled}
                  isEnabledSyncWithRepricer={isEnabledSyncWithRepricer}
                  isSinglePeriod={isSinglePeriod}
                  period={period}
                  productCostValue={productCostValue}
                  selectedPeriodId={selectedPeriodId}
                />
              )}
              renderSplitter={(period) => {
                return (
                  <TimelineSplitter
                    isBasSubscriptionExpired={isBasSubscriptionExpired}
                    isDisabled={isDisabled}
                    isEnabledSyncWithRepricer={isEnabledSyncWithRepricer}
                    period={period}
                  />
                )
              }}
            />
          </Box>

          <Box hasBorder={{ top: true }} justify="end" padding="m">
            <Box mSM={{ width: "100%" }} mXL={{ width: "min-content" }}>
              <Button fullWidth variant="secondary" onClick={onClose}>
                {l("Close")}
              </Button>
            </Box>
          </Box>
        </>
      )}
    </Box>
  )
})
