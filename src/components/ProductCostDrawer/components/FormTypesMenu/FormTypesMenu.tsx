import React from "react"
import { Box, Icon, Menu, Popover, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { FormTypesMenuProps } from "./FormTypesMenuTypes"

export const FormTypesMenu = ({
  items,
  selectedIndex,
  onSelect,
}: FormTypesMenuProps) => {
  return (
    <Menu
      size="large"
      items={items}
      selectedIndex={selectedIndex}
      onSelect={onSelect}
      renderItem={({ item: { isSelected, label, disabled } }) => {
        const iconColor = isSelected
          ? "--color-icon-active"
          : "--color-icon-clickable"

        const popoverContent = disabled
          ? l(
              "FBM shipping costs cannot be applied to FBA products and orders.",
            )
          : null

        return (
          <Popover content={popoverContent}>
            <Box
              gap="s"
              align="center"
              justify="space-between"
              width="100%"
              height="100%"
            >
              <Typography variant="--font-body-text-7">{label}</Typography>

              <Icon
                name="icnChevronRight"
                size="--icon-size-2"
                color={iconColor}
              />
            </Box>
          </Popover>
        )
      }}
      isNavigationEnabled
    />
  )
}
