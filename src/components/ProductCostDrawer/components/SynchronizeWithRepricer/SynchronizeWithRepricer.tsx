import React from "react"
import { Box, Button, Popover, Switch } from "@develop/fe-library"

import l from "utils/intl"
import { buildTextRepricerActivation } from "utils/repricerHelpers"

import { useSynchronizeWithRepricer } from "./hooks"

import type { SynchronizeWithRepricerProps } from "./SynchronizeWithRepricerTypes"

export const SynchronizeWithRepricer = ({
  onClose,
}: SynchronizeWithRepricerProps) => {
  const {
    onChange,
    isEnabledSyncWithRepricer,
    isEnableSyncWithRepricerLoading,
    isRepricerToBASyncAllowed,
    isUpgradeRepricerSubscription,
  } = useSynchronizeWithRepricer()

  return (
    <Box flex={1} flexDirection="column">
      <Box align="start" flex={1} padding="l">
        <Popover
          content={
            !isRepricerToBASyncAllowed
              ? buildTextRepricerActivation(isUpgradeRepricerSubscription)
              : ""
          }
        >
          <Switch
            isChecked={isEnabledSyncWithRepricer}
            isLoading={isEnableSyncWithRepricerLoading}
            label={l("Synchronize with Repricer")}
            isDisabled={
              isEnableSyncWithRepricerLoading || !isRepricerToBASyncAllowed
            }
            onChange={onChange}
          />
        </Popover>
      </Box>

      <Box
        hasBorder={{ top: true }}
        mSM={{
          padding: "m",
          flexDirection: "column",
          align: "stretch",
        }}
        tb={{
          padding: "m l",
          flexDirection: "row",
          justify: "end",
        }}
      >
        <Button variant="secondary" onClick={onClose}>
          {l("Close")}
        </Button>
      </Box>
    </Box>
  )
}
