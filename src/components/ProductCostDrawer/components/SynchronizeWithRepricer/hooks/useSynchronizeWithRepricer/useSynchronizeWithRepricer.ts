import { useCallback } from "react"
import { useDispatch, useSelector } from "react-redux"

import productCostActions from "actions/productCostActions"

import { customerRepricerSubscriptionInfoSelector } from "selectors/customerSelectors"
import {
  isEnableSyncWithRepricerLoadingSelector,
  selectedMarketplaceSelector,
} from "selectors/productCostSelectors"

import setConfirm from "utils/confirm"
import l from "utils/intl"

const { synchronizeWithRepricer } = productCostActions

export const useSynchronizeWithRepricer = () => {
  const dispatch = useDispatch()

  const { id, is_enabled_sync_with_repricer } =
    useSelector(selectedMarketplaceSelector) || {}
  const isEnableSyncWithRepricerLoading = useSelector(
    isEnableSyncWithRepricerLoadingSelector,
  )
  const { isRepricerToBASyncAllowed, isUpgradeRepricerSubscription } =
    useSelector(customerRepricerSubscriptionInfoSelector)

  const handleChange = useCallback(
    (isChecked: boolean): void => {
      const payload = {
        id,
        is_enabled_sync_with_repricer: isChecked,
      }

      const handleOk = (): void => {
        dispatch(synchronizeWithRepricer(payload))
      }

      setConfirm({
        title: l("Are you sure you want to proceed?"),
        message: l(
          "Please note, any edits to this field will re-write settings for all current costs presented on this page for selected products.",
        ),
        onOk: handleOk,
        cancelText: l("Cancel"),
        okText: l("Confirm"),
      })
    },
    [id],
  )

  return {
    onChange: handleChange,
    isEnabledSyncWithRepricer: is_enabled_sync_with_repricer,
    isEnableSyncWithRepricerLoading,
    isRepricerToBASyncAllowed,
    isUpgradeRepricerSubscription,
  }
}
