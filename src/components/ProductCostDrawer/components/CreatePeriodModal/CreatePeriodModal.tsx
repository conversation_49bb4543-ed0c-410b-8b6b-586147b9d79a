import React from "react"
import { Box, FormItems, Modal, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { useCreatePeriodModal } from "./hooks"

import { CreatePeriodModalFormValues } from "./CreatePeriodModalTypes"

export const CreatePeriodModal = () => {
  const {
    form,
    canBeRendered,
    productBuyingPrice,
    onClose,
    onSubmit,
    isSubmitting,
    isSubmitButtonDisabled,
    items,
    title,
  } = useCreatePeriodModal()

  if (!canBeRendered) {
    return null
  }

  return (
    <Modal
      visible
      title={title}
      cancelButtonText={l("Cancel")}
      onCancel={onClose}
      cancelButtonProps={{
        disabled: isSubmitting,
      }}
      okButtonText={l("Save")}
      onOk={onSubmit}
      okButtonProps={{
        disabled: isSubmitButtonDisabled,
      }}
    >
      <Box gap="m" flexDirection="column">
        <Typography variant="--font-body-text-7" color="--color-text-main">
          {l(
            "Please enter the date from which the current value {productBuyingPrice} is valid.",
            {
              productBuyingPrice,
            },
          )}
        </Typography>

        <FormItems<CreatePeriodModalFormValues>
          gridContainerProps={{ gap: "m" }}
          // @ts-expect-error
          form={form}
          items={items}
        />
      </Box>
    </Modal>
  )
}
