import React from "react"
import { Modal, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { useDisclaimerModal } from "./hooks"

export const DisclaimerModal = () => {
  const { onCancel } = useDisclaimerModal()

  return (
    <Modal
      visible
      className="disclaimer-modal"
      onCancel={onCancel}
      title={l("Disclaimer")}
      width="--modal-size-m"
    >
      <div className="disclaimer-modal-body">
        <Typography
          className="disclaimer-modal-text"
          variant="--font-body-text-7"
          color="--color-text-main"
        >
          {l(
            "Currencies, used in the Currency exchange are for informational purposes only. Only marketplace currency is used for operations with/on Amazon.",
          )}
        </Typography>
        <Typography
          className="disclaimer-modal-text"
          variant="--font-body-text-7"
          color="--color-text-main"
        >
          <a
            className="main-link"
            rel="noopener noreferrer"
            href="https://www.ecb.europa.eu/stats/policy_and_exchange_rates/euro_reference_exchange_rates/html/index.en.html"
            target="_blank"
          >
            {l(
              "The currency exchange rate is given by the ECB and changes twice a day.",
            )}
          </a>
        </Typography>
      </div>
    </Modal>
  )
}
