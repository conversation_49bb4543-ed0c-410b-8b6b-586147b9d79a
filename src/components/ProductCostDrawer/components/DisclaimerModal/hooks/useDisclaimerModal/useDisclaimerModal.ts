import { useCallback } from "react"
import { useDispatch } from "react-redux"

import productCostActions from "actions/productCostActions"

const { closeDisclaimerModal } = productCostActions

export const useDisclaimerModal = () => {
  const dispatch = useDispatch()

  const handleCancel = useCallback((): void => {
    dispatch(closeDisclaimerModal())
  }, [])

  return {
    onCancel: handleCancel,
  }
}
