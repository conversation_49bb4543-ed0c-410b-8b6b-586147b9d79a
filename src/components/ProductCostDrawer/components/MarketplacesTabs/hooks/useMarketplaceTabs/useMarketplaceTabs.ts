import { useCallback } from "react"
import { useDispatch, useSelector } from "react-redux"
import { breakpoints } from "@develop/fe-library/dist/consts"
import { useBreakpoint } from "@develop/fe-library/dist/hooks"

import productCostActions from "actions/productCostActions"

import { marketplacesTabsStatesSelector } from "selectors/productCostSelectors"

import { confirmResetChanges } from "components/ProductCostDrawer/utils"

const { changeMarketplaceTab, endEditPeriod, setIsFormEditing } =
  productCostActions

export const useMarketplaceTabs = () => {
  const dispatch = useDispatch()

  const {
    formType,
    homeMarketplaceId,
    isLoading,
    isFormChanged,
    selectedMarketplaceId,
    items,
    marketplaceFlags,
    selectedTabIndex,
    marketplaces,
    isGlobalMarketplaceFeatureAvailable,
  } = useSelector(marketplacesTabsStatesSelector)

  const breakpoint = useBreakpoint()

  const isMobile: boolean =
    breakpoint === breakpoints.mSM ||
    breakpoint === breakpoints.mMD ||
    breakpoint === breakpoints.mLG

  const handleChangeTab = useCallback(
    (tab): void => {
      const callback = () => {
        dispatch(endEditPeriod())
        dispatch(setIsFormEditing(false))
        dispatch(
          changeMarketplaceTab({
            formType,
            marketplaceId: tab.key,
          }),
        )
      }

      confirmResetChanges({
        callback,
        isChanged: isFormChanged,
      })
    },
    [formType, isFormChanged],
  )

  return {
    homeMarketplaceId,
    isLoading,
    selectedMarketplaceId,
    items,
    marketplaceFlags,
    selectedTabIndex,
    onChangeTab: handleChangeTab,
    headerPadding: !isMobile ? "l" : undefined,
    isMobile,
    marketplaces,
    formType,
    isGlobalMarketplaceFeatureAvailable,
  }
}
