import React, { useCallback, useState, useMemo } from "react"
import { Box, DropdownMenu, Icon, Typography } from "@develop/fe-library"
import type { IconNames } from "@develop/fe-library"

import l from "utils/intl"

import { TabLabel } from "../TabLabel"

import { CollapsedItemsDropdownProps } from "./CollapsedItemsDropdownTypes"

export const CollapsedItemsDropdown = ({
  collapsedItems,
  onSelect,
  selectedIndex,
  isMobile,
}: CollapsedItemsDropdownProps) => {
  const [isDropdownVisible, setIsDropdownVisible] = useState(false)

  const options = useMemo(() => {
    return collapsedItems.map((item) => {
      const { key, label } = item
      return {
        value: key,
        label: label || "",
      }
    })
  }, [collapsedItems])

  const handleSelect = useCallback(
    (index: number): void => {
      onSelect(collapsedItems[index])
    },
    [onSelect, collapsedItems],
  )

  const isSelectedItemAmongCollapsed = selectedIndex >= 0

  const iconName: IconNames = isDropdownVisible
    ? "icnChevronUp"
    : "icnChevronDown"
  const iconColor = isSelectedItemAmongCollapsed
    ? "--color-icon-active"
    : "--color-icon-clickable"
  const textColor = isSelectedItemAmongCollapsed
    ? "--color-text-link"
    : "--color-text-main"

  return (
    <DropdownMenu
      isGlobal
      options={options}
      renderItem={({ item }) => {
        return <TabLabel country={item.label} isMobile={isMobile} />
      }}
      selectedIndex={selectedIndex}
      onSelect={handleSelect}
      onVisibilityChange={setIsDropdownVisible}
    >
      <Box height="100%" align="center" gap="m" padding="0 m" cursor="pointer">
        <Typography variant="--font-body-text-6" color={textColor}>
          {l("{count} More", {
            count: collapsedItems.length,
          })}
        </Typography>

        <Icon name={iconName} size="--icon-size-3" color={iconColor} />
      </Box>
    </DropdownMenu>
  )
}
