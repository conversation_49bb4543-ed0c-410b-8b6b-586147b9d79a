import React from "react"
import { Box, Flag, Icon, Typography } from "@develop/fe-library"

import { countryCode } from "utils/countryCode"

import { TabLabelProps } from "./TabLabelTypes"

export const TabLabel = ({
  country,
  isHomeMarketplace = false,
  isMobile,
  isGlobalMarketplace = false,
  isLinkedMarketplace = false,
}: TabLabelProps) => (
  <Box align="center" gap="m">
    <Flag
      borderRadius="--border-radius-circle"
      locale={countryCode(country)}
      size={16}
    />

    {!isMobile ? (
      <Typography variant="--font-body-text-7">{country}</Typography>
    ) : null}

    {isHomeMarketplace ? (
      <Icon color="--color-icon-static" name="icnHome" size="--icon-size-2" />
    ) : null}

    {isGlobalMarketplace ? (
      <Icon color="--color-icon-static" name="icnGlobal" size="--icon-size-2" />
    ) : null}

    {isLinkedMarketplace ? (
      <Icon color="--color-icon-static" name="icnLink" size="--icon-size-2" />
    ) : null}
  </Box>
)
