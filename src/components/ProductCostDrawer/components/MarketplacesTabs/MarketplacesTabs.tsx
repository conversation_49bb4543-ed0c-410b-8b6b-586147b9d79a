import React from "react"
import { Box, Skeleton, Tabs } from "@develop/fe-library"

import { FORM_TYPES } from "constants/productCost"

import { CollapsedItemsDropdown, TabLabel } from "./components"

import { useMarketplaceTabs } from "./hooks"

import { Product } from "types"

export const MarketplacesTabs = ({ children }) => {
  const {
    homeMarketplaceId,
    isLoading,
    selectedMarketplaceId,
    items,
    marketplaceFlags,
    selectedTabIndex,
    onChangeTab,
    headerPadding,
    isMobile,
    marketplaces,
    formType,
    isGlobalMarketplaceFeatureAvailable,
  } = useMarketplaceTabs()

  return (
    <Tabs
      activeKey={selectedMarketplaceId || undefined}
      headerPadding={headerPadding}
      items={items}
      renderCollapsedItemDropdown={({ collapsedItems, collapsedIndex }) => {
        const selectedMenuItemIndex = selectedTabIndex - collapsedIndex

        return (
          <CollapsedItemsDropdown
            collapsedItems={collapsedItems}
            isMobile={isMobile}
            selectedIndex={selectedMenuItemIndex}
            onSelect={onChangeTab}
          />
        )
      }}
      renderContents={() => {
        return (
          <Box align="stretch" flexDirection="column" height="100%">
            {children}
          </Box>
        )
      }}
      renderLabel={({ item }) => {
        if (isLoading) {
          return <Skeleton width="50px" />
        }

        const isHomeMarketplace: boolean =
          !!homeMarketplaceId && item.key === homeMarketplaceId

        if (!isGlobalMarketplaceFeatureAvailable) {
          return (
            <TabLabel
              country={marketplaceFlags[item.key]}
              isGlobalMarketplace={false}
              isHomeMarketplace={isHomeMarketplace}
              isLinkedMarketplace={false}
              isMobile={isMobile}
            />
          )
        }

        const {
          is_global_marketplace_product,
          is_enabled_sync_with_global_marketplace,
          is_enabled_cost_of_goods_sync,
          is_enabled_other_fees_sync,
          is_enabled_fbm_shipping_cost_sync,
        } = marketplaces[item.key] as Product

        const isBuyingPriceAndSyncEnabled: boolean =
          formType === FORM_TYPES.buying_price && is_enabled_cost_of_goods_sync

        const isOtherFeesAndSyncEnabled: boolean =
          formType === FORM_TYPES.other_fees && is_enabled_other_fees_sync

        const isShippingCostAndSyncEnabled: boolean =
          formType === FORM_TYPES.shipping_cost &&
          is_enabled_fbm_shipping_cost_sync

        const isGlobalMarketplaceFeatureAvailableForCurrentMarketplace: boolean =
          isBuyingPriceAndSyncEnabled ||
          isOtherFeesAndSyncEnabled ||
          isShippingCostAndSyncEnabled

        const isGlobalMarketplace: boolean =
          isGlobalMarketplaceFeatureAvailableForCurrentMarketplace &&
          is_global_marketplace_product

        const isLinkedMarketplace: boolean =
          isGlobalMarketplaceFeatureAvailableForCurrentMarketplace &&
          is_enabled_sync_with_global_marketplace

        return (
          <TabLabel
            country={marketplaceFlags[item.key]}
            isGlobalMarketplace={isGlobalMarketplace}
            isHomeMarketplace={isHomeMarketplace}
            isLinkedMarketplace={isLinkedMarketplace}
            isMobile={isMobile}
          />
        )
      }}
      onChange={onChangeTab}
    />
  )
}
