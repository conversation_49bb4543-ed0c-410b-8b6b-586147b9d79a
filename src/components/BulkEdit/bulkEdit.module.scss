@import "assets/styles/variables";

.bulkDropdown {
  margin-left: -27px;
  & .ant-dropdown-menu-item {
    &:hover {
      color: $text_link;
    }
  }
}

.bulkActionControl {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 100%;
  
  .checkbox {
    min-height: 32px;
    background: $second_bg;
    padding: var(--padding-s) var(--padding-m);
    border-radius: var(--border-radius);
  }

  .bulkaction {
    @include RobotoSanSerifNormal(12px);
    display: flex;
    align-items: center;
    color: $text_link;
    &.disabled {
      color: #676e7a;
      cursor: not-allowed;
      pointer-events: all !important;
    }
  }
  &.disabled {
    color: $text_second;
    cursor: not-allowed;
    pointer-events: all !important;
  }
  
  .selectedText {
    @include RobotoSanSerifNormal(12px);
    line-height: 1.33;
    color: $text_second;
    text-align: left;
    opacity: 0;

    &.visible {
      opacity: 1;
      margin-right: 2px;
    }
  }
  .labelBeta {
    position: absolute;
    padding: 2px;
    background: $icon_active;
    color: $white_text;
    padding: 1px 3px;
    border-radius: 4px;
    top: -7px;
    left: 15px;
    font-size: 10px;
  }
}

.bulkResizingButtons {
  display: flex;
  align-items: center;
  height: 58px;
}
