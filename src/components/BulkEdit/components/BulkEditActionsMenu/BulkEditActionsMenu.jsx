import React from "react"
import { Icon, Popover, Typography } from "@develop/fe-library"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"

import { MD, sizes } from "utils/breakpoints"
import l from "utils/intl"
import { checkIsString } from "utils/validationHelper"

import { SelectedTextLabel } from "./SelectedTextLabel"

import styles from "./bulkEditActionsMenu.module.scss"

const getIcon = (icon) => {
  return checkIsString(icon) ? (
    <Icon color="--color-icon-static" name={icon} />
  ) : (
    icon
  )
}

const generateHandleClick = (onClick) => () => {
  if (onClick) {
    onClick()
  }
}

export const BulkEditActionsMenu = ({
  selectedCount,
  totalCount,
  selectedTotalCount,
  filtered,
  buttons,
}) => {
  const { innerWidth: windowWidth } = window

  const isVisible = selectedCount || selectedTotalCount
  const mobileDeviceWidth = windowWidth < sizes[MD]

  if (!isVisible) {
    return null
  }

  return (
    <>
      <div className={styles.container}>
        <div className={styles.selected}>
          <SelectedTextLabel
            filtered={filtered}
            selectedCount={selectedCount}
            selectedTotalCount={!!selectedTotalCount}
            totalCount={totalCount}
          />
        </div>

        {buttons.map(({ title, icon, onClick }) => (
          <div
            key={title}
            className={styles.button}
            onClick={generateHandleClick(onClick)}
          >
            {mobileDeviceWidth ? (
              <Popover content={l(title)} placement="top">
                {getIcon(icon)}
              </Popover>
            ) : (
              getIcon(icon)
            )}
            <Typography
              className={styles.buttonTitle}
              color="--color-text-main"
              variant="--font-body-text-7"
            >
              <FormattedMessage id={title} />
            </Typography>
          </div>
        ))}
      </div>
    </>
  )
}

BulkEditActionsMenu.propTypes = {
  selectedCount: PropTypes.number,
  selectedTotalCount: PropTypes.bool,
  onClose: PropTypes.func,
  buttons: PropTypes.array,
  callBackType: PropTypes.string,
}
