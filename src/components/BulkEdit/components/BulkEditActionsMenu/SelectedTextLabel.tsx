import React from "react"
import cn from "classnames"
import FormattedMessage from "components/FormattedMessage"
import ln from "utils/localeNumber"
import { Typography } from "@develop/fe-library"

import { SelectedTextLabelProps } from "./SelectedTextLabelTypes"

export const SelectedTextLabel = ({
  selectedText = "Selected",
  filteredText = "Filtered",
  selectedTotalCount = false,
  selectedCount = 0,
  filtered = 0,
  totalCount = 0,
}: SelectedTextLabelProps) => {
  let textLabel = selectedText
  let countToDisplay = selectedTotalCount ? totalCount : selectedCount

  if (selectedCount <= 0 && filtered > 0) {
    textLabel = filteredText
    countToDisplay = filtered
  }

  return (
    <Typography variant="--font-body-text-4" color="--color-text-main" className={cn("selectedText", { visible: countToDisplay > 0 })}>
      <FormattedMessage id={textLabel} /> <br /> {ln(countToDisplay)}
    </Typography>
  )
}
