import React, { useEffect, useState } from "react"
import { useSelector } from "react-redux"
import { Dropdown, Menu } from "antd"
import { Box, Checkbox, Icon } from "@develop/fe-library"
import { getObjectKeys } from "@develop/fe-library/dist/utils"
import cn from "classnames"
import * as PropTypes from "prop-types"

import { customerIdSelector } from "selectors/mainStateSelectors"

import FormattedMessage from "components/FormattedMessage"

import { BULK_CONTROLS_MAPPER } from "constants/bulkEdit"

import { BulkEditActionsMenu } from "./components"

import styles from "./bulkEdit.module.scss"

const BulkEdit = ({
  filtered,
  resizingActions,
  controls,
  buttons,

  selectedCount,
  selectedTotalCount,
  totalCount,

  products,
  selectedData,

  onSelectAll,
  onSelectTotalCount,

  isDisabled,
}) => {
  const [isMenuVisible, setIsMenuVisible] = useState(false)

  const customerId = useSelector(customerIdSelector)

  useEffect(() => {
    onSelectAll(false)
    onSelectTotalCount(false)
  }, [customerId])

  const controlsList = getObjectKeys(controls).map((key) => ({
    title: controls[key].title,
    onClick: () => {
      const selectArgs = BULK_CONTROLS_MAPPER[controls[key].type]

      handleSelect(selectArgs)
      if (isMenuVisible) {
        setIsMenuVisible(false)
      }
    },
  }))

  const isPartialSelected =
    !selectedTotalCount &&
    !!(selectedData.length && selectedData.length < products.length)
  const checked =
    !!(selectedData.length && products.length === selectedData.length) ||
    selectedTotalCount ||
    isPartialSelected

  const handleSelect = ({ all, total = false }) => {
    onSelectAll(all)
    onSelectTotalCount(total)
  }

  const handleCheckboxToggle = (isChecked) => {
    handleSelect({ all: isChecked })
  }

  const selectType = isPartialSelected ? "selectAll" : "default"

  return (
    <>
      {resizingActions?.isResizingMode ? (
        <div className={styles.bulkResizingButtons}>
          {resizingActions.buttons}
        </div>
      ) : (
        <div
          className={cn(styles.bulkActionControl, {
            [styles.active]: selectedCount || selectedTotalCount,
          })}
        >
          <Box align="center" className={styles.checkbox} gap="m">
            <Checkbox
              checked={checked}
              disabled={isDisabled}
              type={selectType}
              onChange={handleCheckboxToggle}
            />
            <Dropdown
              disabled={isDisabled}
              trigger={["click"]}
              visible={isMenuVisible}
              overlay={
                <Menu className={styles.bulkDropdown}>
                  {controlsList.map(({ title, onClick }) => (
                    <Menu.Item key={title} onClick={onClick}>
                      <FormattedMessage id={title} />
                    </Menu.Item>
                  ))}
                </Menu>
              }
              onVisibleChange={setIsMenuVisible}
            >
              <Icon isHovered name="icnChevronDown" size="--icon-size-1" />
            </Dropdown>
          </Box>

          <BulkEditActionsMenu
            buttons={buttons}
            filtered={filtered}
            selectedCount={selectedCount}
            selectedTotalCount={selectedTotalCount}
            totalCount={totalCount}
          />
        </div>
      )}
    </>
  )
}

BulkEdit.propTypes = {
  filtered: PropTypes.number,
  resizingActions: PropTypes.object,
  controls: PropTypes.array,
  buttons: PropTypes.array,

  selectedCount: PropTypes.number,
  selectedTotalCount: PropTypes.bool,
  totalCount: PropTypes.number,

  data: PropTypes.array,
  selectedData: PropTypes.array,

  onSelectAll: PropTypes.func,
  onSelectTotalCount: PropTypes.func,

  isDisabled: PropTypes.bool,
}

BulkEdit.defaultProps = {
  filtered: 0,
  resizingActions: {},
  controls: [],
  buttons: [],

  selectedCount: 0,
  selectedTotalCount: false,
  totalCount: 0,

  data: [],
  selectedData: [],

  onSelectAll: () => {},
  onSelectTotalCount: () => {},

  isDisabled: false,
}

export { BulkEdit }
