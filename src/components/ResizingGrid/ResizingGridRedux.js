import { connect } from 'react-redux'
import ResizingGridContainer from './ResizingGridContainer'

import tableSettingsActions from 'actions/tableSettingsActions'
import { settingsSelector } from 'selectors/tableSettingsSelectors'
import { 
  currentUserSelector,
  translationsSelector 
} from 'selectors/mainStateSelectors'

const { save, defaultSave, get, setIsResizingMode } = tableSettingsActions

const mapStateToProps = state => {
  const { locale } = translationsSelector(state)
  const { role } = currentUserSelector(state)?.user
  const { isResizingMode } = state.tableSettings
  return {
    isResizingMode,
    settings: settingsSelector(state),
    language: locale,
    role,
  }
}

const mapDispatchToProps = dispatch => ({
  saveSettings: (settings, callback) => dispatch(save(settings, callback)),
  saveDefaultSettings: (settings, language, callback) =>
    dispatch(defaultSave(settings, language, callback)),
  getTableSettings: (key, callback, defaultKey) =>
    dispatch(get(key, callback, defaultKey)),
  setIsResizingMode: isResizingMode =>
    dispatch(setIsResizingMode(isResizingMode)),
})

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(ResizingGridContainer)
