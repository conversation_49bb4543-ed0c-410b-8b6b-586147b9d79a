import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useLocation } from "react-router"
import {
  getObjectKeys,
  getUrlSearchParams,
} from "@develop/fe-library/dist/utils"

import gridActions from "actions/gridActions"
import tableSettingsActions from "actions/tableSettingsActions"
import userSettingsActions from "actions/userSettingsActions"

import { translationsSelector } from "selectors/mainStateSelectors"
import {
  getSelectedTableSettingsSelector,
  isTableSettingsVisibleSelector,
} from "selectors/tableSettingsSelectors"
import { isCustomerUserSelector } from "selectors/userSelectors"

import { checkIsArray } from "utils/arrayHelpers"
import { getIsShowClearButton } from "utils/clearAllUtils"
import { getClearObject } from "utils/objectHelpers"

import { DEFAULT_PAGE_SIZE } from "constants/grid"

export const useResizingGrid = ({
  searchOptions = {},
  prevSearchOptions = {},
  initialUrlParams = {},
  excludeClearFilterKeys = [],
  tableSettingsKey = null,
  getProducts = () => {},
  onGridInit,
  savedUserFilter = null,
  onUrlParamsFormat,
  handleUnmount = () => {},
}) => {
  const { display: displaySettings, get: getTableSettings } =
    tableSettingsActions
  const { getUserSettings } = userSettingsActions
  const { page, pageSize } = searchOptions

  const dispatch = useDispatch()
  const location = useLocation()

  const [changedItems, setChangedItems] = useState([])
  const [isShowButtons, setIsShowButtons] = useState(false)
  const [locale, setLocale] = useState(null)

  const { locale: language } = useSelector(translationsSelector)
  const isCustomerUser = useSelector(isCustomerUserSelector)
  const columns = useSelector((state) =>
    getSelectedTableSettingsSelector(state, tableSettingsKey),
  )

  const isTableSettingsVisible = useSelector(isTableSettingsVisibleSelector)

  const tableSettingsKeyDefault = locale
    ? `${tableSettingsKey}Default${locale}`
    : null

  const urlSearchParams = getUrlSearchParams({
    locationSearch: document.location.search,
  })

  useEffect(() => {
    const currentSearchOptions = getClearObject({ object: searchOptions })
    const prevSearchParams = getClearObject({ object: prevSearchOptions })
    const isSearchOptionsChanged =
      JSON.stringify(currentSearchOptions) !== JSON.stringify(prevSearchParams)

    if (isSearchOptionsChanged) {
      setIsShowButtons(
        getIsShowClearButton({
          searchOptions: urlSearchParams,
          excludeKeys: excludeClearFilterKeys,
        }),
      )

      if (!location.search) {
        return
      }

      getProducts({
        searchOptions: {
          ...getUrlSearchParams({ locationSearch: location.search }),
        },
        ...changedItems,
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.key])

  useEffect(() => {
    const isStateWithoutLocale = language.length && !locale
    const currentLanguage = language[0].toUpperCase() + language.slice(1)
    let currentUrlSearchParams = urlSearchParams

    if (isStateWithoutLocale) {
      setLocale(currentLanguage)
    }
    const isTableSettingsKeyDefined =
      tableSettingsKey && tableSettingsKeyDefault

    if (isTableSettingsKeyDefined) {
      onGridInit && onGridInit()

      dispatch(
        getUserSettings({ key: "mainFilterPageSettings" }, (data) => {
          const hasSavedData = checkIsArray(data) && savedUserFilter

          if (hasSavedData) {
            currentUrlSearchParams = isCustomerUser
              ? JSON.parse(data[0].value)?.[savedUserFilter]
              : urlSearchParams
          }

          dispatch(
            getTableSettings(
              tableSettingsKey,
              ({ pageSize = DEFAULT_PAGE_SIZE }) => {
                const pageSizeResult =
                  currentUrlSearchParams?.pageSize || pageSize
                const initialSearchParams = {
                  ...initialUrlParams,
                  ...currentUrlSearchParams,
                }

                setIsShowButtons(
                  getIsShowClearButton({
                    searchOptions: initialSearchParams,
                    excludeKeys: excludeClearFilterKeys,
                  }),
                )

                dispatch(
                  gridActions.pushUrl({
                    ...initialSearchParams,
                    pageSize: +pageSizeResult,
                  }),
                )

                getProducts({
                  searchOptions: {
                    ...initialSearchParams,
                    pageSize: +pageSizeResult,
                  },
                })
              },
              tableSettingsKeyDefault,
            ),
          )
        }),
      )
    }

    return () => {
      handleUnmount()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tableSettingsKey, tableSettingsKeyDefault])

  const onSearchOptionsChange = ({
    searchOptions,
    selectedFilterItem,
    params,
  }) => {
    const formattedUrlParams = onUrlParamsFormat
      ? onUrlParamsFormat({ searchOptions, selectedFilterItem })
      : searchOptions

    dispatch(gridActions.pushUrl(formattedUrlParams))

    setChangedItems({
      selectedFilterItem,
      params,
    })
  }

  const onPageChange = (page) => {
    onSearchOptionsChange({
      searchOptions: {
        ...searchOptions,
        page,
      },
    })
  }

  const onPageSizeChange = (pageSize) => {
    onSearchOptionsChange({
      searchOptions: {
        ...searchOptions,
        page: 1,
        pageSize,
      },
    })
  }

  const onTableChange = (first, second, { order, columnKey }) => {
    onSearchOptionsChange({
      searchOptions: {
        ...searchOptions,
        page: 1,
        sort: order
          ? `${order === "ascend" ? "" : "-"}${columnKey}`
          : undefined,
      },
    })
  }

  const onFilterChange = ({ params, newSearchOptions }) => {
    if (!checkIsArray(params)) {
      const [itemKey] = getObjectKeys(params).filter((key) => params[key])

      const composedSearchOptions = {
        ...searchOptions,
        ...newSearchOptions,
        ...params,
      }

      setIsShowButtons(
        getIsShowClearButton({
          searchOptions: composedSearchOptions,
          excludeKeys: excludeClearFilterKeys,
        }),
      )

      onSearchOptionsChange({
        searchOptions: composedSearchOptions,
        selectedFilterItem: !!params[itemKey]
          ? { key: itemKey, value: params[itemKey] }
          : null,
        params,
      })

      return
    }

    const [item] = params

    setIsShowButtons(
      getIsShowClearButton({
        searchOptions: newSearchOptions,
        excludeKeys: excludeClearFilterKeys,
      }),
    )

    onSearchOptionsChange({
      searchOptions: newSearchOptions,
      selectedFilterItem: item,
      params,
    })
  }

  const onFilterClear = () => {
    onSearchOptionsChange({
      searchOptions: { ...initialUrlParams, page, pageSize },
    })
  }

  const onDisplaySettings = () =>
    dispatch(displaySettings(tableSettingsKey, true))

  return {
    isShowButtons,
    isTableSettingsVisible,
    columns,
    page,
    pageSize,
    changedItems,
    onPageChange,
    onPageSizeChange,
    onTableChange,
    onFilterChange,
    onFilterClear,
    onDisplaySettings,
  }
}
