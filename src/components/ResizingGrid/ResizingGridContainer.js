import React, { Component } from "react"
import PropTypes from "prop-types"
import delay from "lodash/delay"
import { <PERSON><PERSON>, Popover } from "@develop/fe-library"

import { DEFAULT_MIN_COLUMNS_WIDTH, MIN_COLUMN_WIDTH } from "constants/grid"

import l from "utils/intl"

import "./resizing.scss"

const getLastKey = (path) => {
  path = !Array.isArray(path) ? path.split(".") : path
  return path[path.length - 1]
}

class ResizingGridContainer extends Component {
  constructor(props) {
    super(props)
    const { settings, role } = props
    this.settings = settings || []
    this.buttons = this.getButtons(role)
  }

  state = {
    resizingActions: {},
  }

  setGridRef = (ref) => {
    if (!ref) return
    this.gridRef = ref
    this.gridHeaderRef = ref.getElementsByClassName("ant-table-header")[0]
    this.gridBodyRef = ref.getElementsByClassName("ant-table-body")[0]
    const allCol = this.getColArr(this.gridRef)
    allCol.forEach((col, i) => {
      allCol[i].style.cssText = this.getCssWidthInString(this.getColWidth(col))
    })
  }

  onSave(settingsKey, columKey, width) {
    const {
      saveSettings,
      searchOptions: { pageSize },
    } = this.props

    const index = this.settings.findIndex(
      (col) => col.name === settingsKey || col.name === columKey,
    )
    if (index > -1) {
      this.settings[index]["width"] = width
    }
    saveSettings({ settings: this.settings, pageSize })
  }

  saveAsDefault = async (settings) => {
    const {
      saveSettings,
      saveDefaultSettings,
      language,
      setIsResizingMode,
      searchOptions: { pageSize },
    } = this.props
    const { isReset } = settings || {}
    if (isReset) {
      saveSettings(
        {
          settings: this.settings.map(({ width, ...col }) => col),
          pageSize,
        },
        () => window.location.reload(),
      )
    } else {
      await saveDefaultSettings({ settings: this.settings }, language)
      setIsResizingMode(false)
    }
  }

  getColumnIngex = (columKey) => {
    const {
      children: {
        props: { columns },
      },
      startFromColumnIndex,
    } = this.props

    if (!columns || !columns.length) return
    return (
      columns.findIndex(({ key }) => key === columKey) + startFromColumnIndex ||
      0
    )
  }

  getColArr = (ref) => (ref ? ref.querySelectorAll("col") : undefined)

  getColWidth = (col) => col?.style?.width && +col.style.width.replace("px", "")

  getCssWidthInString = (width) => `width: ${width}px; min-width: ${width}px;`

  toggleBodyClassName = (status, className) => {
    if (status) {
      document.body.className = document.body.className
        ? `${document.body.className} ${className}`
        : `${className}`
    } else {
      document.body.className = document.body.className
        .replace(`${className}`, "")
        .replace("  ", "")
    }
  }

  onResize = ({ dataIndex, columKey, event }) => {
    if (!this.gridBodyRef && !this.gridRef) return
    let startPositionX
    let finishWidth

    const settingsKey = getLastKey(dataIndex)
    columKey = getLastKey(columKey)

    const startTime = Date.now()
    const bodyCols = this.getColArr(this.gridBodyRef || this.gridRef)

    if (!bodyCols || !settingsKey) return
    this.toggleBodyClassName(true, "is-resizing")

    const index = this.getColumnIngex(columKey)
    const headerCols = this.getColArr(this.gridHeaderRef)
    const startWidth = this.getColWidth(bodyCols[index])

    const { children: { props: childrenProps } = {} } = this.props
    const { columns = [] } = childrenProps

    const currentColumn =
      columns?.find(({ dataIndex }) => dataIndex === columKey) || null
    const columnMinWidth = currentColumn?.min || null
    const columnType = currentColumn?.type || null

    const onMove = (event) => {
      if (event.cancelable) {
        event.preventDefault()
      }

      event.stopPropagation()

      if (event.touches && Date.now() - startTime < 601) {
        onMoveEnd()
        return
      }
      const pageX =
        event.pageX || event.pageX === 0 ? event.pageX : event.touches[0].pageX
      if (startPositionX === undefined) startPositionX = pageX
      if (!startWidth || startWidth < 5) return

      const currentStartWidth = startWidth + (startPositionX - pageX) * -1
      const currentColumnWidth =
        columnMinWidth ||
        DEFAULT_MIN_COLUMNS_WIDTH?.[columnType] ||
        MIN_COLUMN_WIDTH
      const calculatedWidth = Math.max(currentStartWidth, currentColumnWidth)

      delay(
        (width) => {
          finishWidth = width
          const cssText = this.getCssWidthInString(width)
          if (headerCols) headerCols[index].style.cssText = cssText
          if (bodyCols) bodyCols[index].style.cssText = cssText
          if (bodyCols && headerCols) {
            headerCols.forEach((col, index) => {
              if (bodyCols[index] !== undefined) {
                bodyCols[index].style.cssText = col?.style?.cssText
              }
            })
          }
        },
        100,
        calculatedWidth
      )
    }

    const onMoveEnd = (event) => {
      if (event) {
        event.cancelable && event.preventDefault()
        event.stopPropagation()
        this.onSave(settingsKey, columKey, finishWidth)
      }
      window.removeEventListener("touchmove", onMove, { passive: false })
      window.removeEventListener("touchend", onMoveEnd, { passive: false })

      window.removeEventListener("mousemove", onMove, { passive: false })
      window.removeEventListener("mouseup", onMoveEnd, { passive: false })
      this.toggleBodyClassName(false, "is-resizing")
    }

    window.addEventListener("touchmove", onMove, { passive: false })
    window.addEventListener("touchend", onMoveEnd, { passive: false })

    window.addEventListener("mousemove", onMove, { passive: false })
    window.addEventListener("mouseup", onMoveEnd, { passive: false })
  }

  getButtons = (role) => {
    const handleSave = () => this.saveAsDefault()
    const handleReset = () => this.saveAsDefault({ isReset: true })

    return (
      <div className="resizing-buttons">
        {role === "superadmin" && (
          <Popover content={l("Save as default")} placement="topLeft">
            <Button
              onClick={handleSave}
              icon="icnSave"
              iconOnly
            />
          </Popover>
        )}
        <Popover content={l("Reset to default")} placement="top">
          <Button
            onClick={handleReset}
            icon="icnReload"
            iconOnly
          />
        </Popover>
      </div>
    )
  }

  componentWillUnmount() {
    const { setIsResizingMode } = this.props
    setIsResizingMode(false)

    this.toggleBodyClassName(false, "resizing-mode")
    this.toggleBodyClassName(false, "is-resizing")
  }

  componentDidUpdate(prevProps) {
    const { isResizingMode, settings, getActions } = this.props
    const { isResizingMode: prevIsResizingMode, settings: prevSettings } =
      prevProps

    if (isResizingMode !== prevIsResizingMode) {
      this.toggleBodyClassName(Boolean(isResizingMode), "resizing-mode")

      const resizingActions = {
        isResizingMode,
        buttons: this.buttons,
        saveAsDefault: this.saveAsDefault,
        resetToDefault: () => this.saveAsDefault(true),
      }

      getActions && getActions(resizingActions)
      this.setState({ resizingActions })
    }

    if (settings !== prevSettings) {
      this.settings = settings || []
    }
  }

  render() {
    return (
      <>
        {React.cloneElement(this.props.children, {
          onResize: this.onResize,
          setGridRef: this.setGridRef,
        })}
      </>
    )
  }
}

ResizingGridContainer.propTypes = {
  columns: PropTypes.array,
}

export default ResizingGridContainer
