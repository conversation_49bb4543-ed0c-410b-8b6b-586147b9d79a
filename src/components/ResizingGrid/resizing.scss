@import "assets/styles/variables.scss";

.is-resizing {
  user-select: none;
  & *,
  .ant-table,
  .ant-table-scroll {
    touch-action: none !important;
  }
}

.table-grid-resize-hover {
  pointer-events: auto;
  position: absolute;
  left: 16px;
  right: -16px;
  top: 0;
  bottom: 0;
  z-index: 1;
  .resize-grag-box {
    cursor: col-resize;
    pointer-events: auto;
    position: absolute;
    top: 0px;
    right: -4px;
    opacity: 0;
    transition: 0.5s;
    z-index: 5;
    width: 40px;
    text-align: center;
    height: 100%;
  }
  .resize-icon {
    transform: rotate(90deg);
  }
  &:hover .resize-grag-box {
    opacity: 1;
  }
}

.resizing-mode {
  user-select: none;
  .ant-table-column-title {
    & > span,
    .grid-filter {
      opacity: 0.5;
    }
  }
  .table-grid-resize-hover {
    z-index: 20;
    opacity: 1;
    * {
      opacity: 1;
    }
    .resize-grag-box {
      .anticon {
        * {
          fill: $icon_clicable;
        }
      }
    }
  }
}

.resizing-buttons {
  position: relative;
  display: flex;
  align-items: center;
  z-index: 20;
  gap: var(--gap-m);

  button {
    flex-shrink: 0;
  }
}
