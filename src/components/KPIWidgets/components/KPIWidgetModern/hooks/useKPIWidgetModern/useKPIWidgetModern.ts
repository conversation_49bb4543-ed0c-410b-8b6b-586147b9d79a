import { useCallback, useEffect, useMemo, useRef, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useHistory } from "react-router-dom"
import { usePrevious } from "@develop/fe-library/dist/hooks"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"
import { format, isSameDay } from "date-fns"

import { salesHistoryActions } from "actions/salesHistoryActions"

import {
  languageSelector,
  translationsSelector,
} from "selectors/mainStateSelectors"
import { isMarketplaceGroupsSuccessSelector } from "selectors/marketplaceSelectors"
import { isCustomerUserSelector } from "selectors/userSelectors"

import { KPI_WIDGET_SELECTED_URL_PARAMS_TO_WATCH } from "components/KPIWidgets/constants"

import { useSubscription } from "hooks"
import { useDateRangeLabels } from "hooks/useDateRangeLabels"
import { useKPIWidgets } from "hooks/useKPIWidgets"
import { useMinStatsDate } from "hooks/useMinStatsDate"
import { useSellerMarketplaceParams } from "hooks/useSellerMarketplaceParams"
import { useUrlParams } from "hooks/useUrlParams"

import { checkIsArray } from "utils/arrayHelpers"
import { convertToLocalDateTime } from "utils/dateConverter"
import l from "utils/intl"

import { ASYNC_STATUSES } from "constants/async"
import { CLEAR_TO_CLOSE_LABEL } from "constants/dateRange"
import { DATE_FNS_FORMATS } from "constants/dateTime"
import { SALES_CATEGORY_STRATEGIES } from "constants/general"
import { LOCALES } from "constants/locales"
import { PAGE_VIEW } from "constants/pageView"

import {
  AsyncStatus,
  DashboardFiltersParams,
  KeyPerformance,
  KPIWidgetRequestParams,
} from "types"
import { DateRangePickerSelected } from "types/DatePicker"

import { UseKPIWidgetModernProps } from "./useKPIWidgetModernTypes"

const { getWidgetData } = salesHistoryActions

export const useKPIWidgetModern = ({
  index,
  onSelectWidget,
}: UseKPIWidgetModernProps) => {
  const dispatch = useDispatch()
  const history = useHistory()

  const {
    kpiWidgetsSettings,
    setKPIWidgetsSettingByIndex,
    getSelectedWidget,
    getCustomerKPIWidgetsSettings,
    updateCustomerKPIWidgetsSettings,
  } = useKPIWidgets()

  const [loadingWidget, setLoadingWidget] = useState<AsyncStatus>(
    ASYNC_STATUSES.IDLE,
  )

  const isInitiated = useRef(false)

  const selected = kpiWidgetsSettings?.[index]?.selected
  const inputMode = kpiWidgetsSettings?.[index]?.inputMode
  const prevDates = usePrevious(kpiWidgetsSettings?.[index]?.selected)

  const isMarketplaceGroupsSuccess = useSelector(
    isMarketplaceGroupsSuccessSelector,
  )
  const { locale } = useSelector(translationsSelector)
  const language = useSelector(languageSelector)
  const isCustomerUser = useSelector(isCustomerUserSelector)

  const { urlParams, prevUrlParams } = useUrlParams<DashboardFiltersParams>()
  const { getSellerMarketplaceParams } = useSellerMarketplaceParams()
  const { hasConnectedAmazonAdAccounts, isBasSubscriptionActive } =
    useSubscription()

  const {
    minStatsDate: fromDate,
    today: toDate,
    minStatsDateLoading,
  } = useMinStatsDate()
  const dateRangePickerLabels = useDateRangeLabels(CLEAR_TO_CLOSE_LABEL)

  const [widgetData, setWidgetData] = useState<KeyPerformance | null>(null)

  const selectedWidget = getSelectedWidget()

  const isSelectedWidget: boolean = String(selectedWidget) === String(index)

  const dateFormatted: string = useMemo(() => {
    if (!selected?.start || !selected?.end) {
      return ""
    }

    const formattedStart = format(selected.start, "P", {
      locale: LOCALES[locale],
    })
    const formattedEnd = format(selected.end, "P", { locale: LOCALES[locale] })
    const isSameDate = isSameDay(selected.start, selected.end)

    return isSameDate ? formattedStart : `${formattedStart} - ${formattedEnd}`
  }, [selected?.end, selected?.start, locale])

  const handleDateRangeSelect = (values: DateRangePickerSelected): void => {
    if (!values?.selected) {
      return
    }

    setKPIWidgetsSettingByIndex({
      index,
      settings: {
        inputMode: values.inputMode,
        selected: values.selected,
      },
    })

    if (isCustomerUser) {
      updateUserSettings(values)
    }

    if (String(selectedWidget) === String(index)) {
      history.push({
        ...history.location,
        search: getUrlSearchParamsString({
          params: {
            ...urlParams,
            from: format(values.selected.start, DATE_FNS_FORMATS.SERVER),
            to: format(values.selected.end, DATE_FNS_FORMATS.SERVER),
            inputMode: values.inputMode,
          },
        }),
      })
    }
  }

  const updateUserSettings = useCallback(
    (values: DateRangePickerSelected): void => {
      const customerKPIWidgetsSettings = getCustomerKPIWidgetsSettings()

      // DESC: Only satisfy TS
      if (!checkIsArray(customerKPIWidgetsSettings)) {
        return
      }

      // DESC: Replace current widget settings with new
      customerKPIWidgetsSettings[index] = {
        selected: values.selected,
        inputMode: values.inputMode,
      }

      updateCustomerKPIWidgetsSettings(customerKPIWidgetsSettings)
    },
    [getCustomerKPIWidgetsSettings, index, updateCustomerKPIWidgetsSettings],
  )

  const fetchWidgetData = useCallback((): void => {
    const { marketplaceSellerIds, marketplace_id, seller_id } =
      getSellerMarketplaceParams(urlParams)

    const requestParams: KPIWidgetRequestParams = {
      sellerId: seller_id,
      marketplaceId: marketplace_id,
      marketplaceSellerIds,
      sellerSku: urlParams.productSku || urlParams.sku,

      dateStart: format(selected.start, DATE_FNS_FORMATS.SERVER),
      dateEnd: format(selected.end, DATE_FNS_FORMATS.SERVER),
      currencyId: urlParams.currency_code,
      isTransactionDateMode:
        urlParams.view === PAGE_VIEW.TRANSACTION ? "1" : "0",
      asin: urlParams.asin,
      ean: urlParams.ean,
      upc: urlParams.upc,
      isbn: urlParams.isbn,
      brand: urlParams.brand,
      productType: urlParams.product_type,
      stockType: urlParams.stock_type,
      manufacturer: urlParams.manufacturer,
      adultProduct: urlParams.adult_product ? "1" : undefined,
      offerType: urlParams.offer_type,
      tagId: urlParams.tags,
      sales_category_strategy: SALES_CATEGORY_STRATEGIES.custom,
    }

    setLoadingWidget(ASYNC_STATUSES.PENDING)

    const successCallback = (data: any): void => {
      data.currency = urlParams.currency_code
      setLoadingWidget(ASYNC_STATUSES.FULFILLED)
      setWidgetData(data)
    }

    const failureCallback = (): void => {
      setLoadingWidget(ASYNC_STATUSES.REJECTED)
    }

    dispatch(getWidgetData(requestParams, successCallback, failureCallback))
  }, [
    selected.end,
    selected.start,
    dispatch,
    getSellerMarketplaceParams,
    urlParams,
  ])

  const handleSelectWidget = useCallback((): void => {
    onSelectWidget(index)

    history.push({
      ...history.location,
      search: getUrlSearchParamsString({
        params: {
          ...urlParams,
          from: format(selected.start, DATE_FNS_FORMATS.SERVER),
          to: format(selected.end, DATE_FNS_FORMATS.SERVER),
          inputMode,
        },
      }),
    })
  }, [onSelectWidget, index, selected, inputMode, history, urlParams])

  // DESC: Initialize and watch effect
  useEffect(() => {
    const isReadyToFetch: boolean = [
      selected.start,
      selected.end,
      urlParams.currency_code,
      isMarketplaceGroupsSuccess,
      loadingWidget !== ASYNC_STATUSES.PENDING,
    ].every(Boolean)

    if (!isReadyToFetch) {
      return
    }

    const isWidgetInitiated: boolean =
      !isInitiated.current || !prevDates || !prevUrlParams

    if (isWidgetInitiated) {
      isInitiated.current = true

      fetchWidgetData()

      return
    }

    const hasDatesChanged: boolean =
      !isSameDay(prevDates?.start, selected.start) ||
      !isSameDay(prevDates?.end, selected.end)

    const hasUrlParamsChanged: boolean =
      KPI_WIDGET_SELECTED_URL_PARAMS_TO_WATCH.some(
        (key) => urlParams[key] !== prevUrlParams?.[key],
      )

    if (hasDatesChanged || hasUrlParamsChanged) {
      fetchWidgetData()
    }
  }, [selected.end, selected.start, urlParams, isMarketplaceGroupsSuccess])

  const iconPopoverContent: string = widgetData?.updatedAt
    ? l("Last update") + ": " + convertToLocalDateTime(widgetData.updatedAt)
    : ""

  const isDateRangePickerReady: boolean = !!(
    selected?.start &&
    selected?.end &&
    inputMode
  )

  const isLoadingWidget: boolean = loadingWidget === ASYNC_STATUSES.PENDING

  const isDisabled: boolean = isLoadingWidget || !isBasSubscriptionActive

  return {
    title: dateRangePickerLabels[inputMode],
    widgetData,
    iconPopoverContent,
    dateFormatted,
    handleDateRangeSelect,
    handleSelectWidget,
    isSelectedWidget,
    isLoadingWidget: loadingWidget === ASYNC_STATUSES.PENDING,
    isDateRangePickerReady,
    fromDate,
    toDate,
    inputMode,
    selected,
    language,
    locale,
    dateRangePickerLabels,
    isDisabledDateRangePicker: minStatsDateLoading,
    isDisabled,
    hasConnectedAmazonAdAccounts,
    isBasSubscriptionActive,
  }
}
