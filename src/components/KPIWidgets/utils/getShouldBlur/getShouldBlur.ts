import { GetShouldBlurProps } from "./getShouldBlurTypes"

export const getShouldBlur = ({
  shouldSkipToBlur,
  isBasSubscriptionActive,
  isSLUser,
  isFreemiumBasModelActive,
}: GetShouldBlurProps): boolean => {
  const shouldShow: boolean = Boolean(isBasSubscriptionActive || isSLUser)

  if (shouldShow) {
    return false
  }

  if (isFreemiumBasModelActive) {
    return !shouldSkipToBlur
  }

  return false
}
