import { INPUT_MODES } from "constants/dateRange"

import { getKPIWidgetDefaultSettings } from "../getKPIWidgetDefaultSettings/getKPIWidgetDefaultSettings"

import { DateRangePickerSelected } from "types/DatePicker"

import { TransformKPIWidgetLoadedSettingsProps } from "./transformKPIWidgetLoadedSettingsTypes"

export const transformKPIWidgetLoadedSettings = ({
  inputMode,
  setting,
  today,
  minStatsDate,
}: TransformKPIWidgetLoadedSettingsProps): DateRangePickerSelected => {
  // there is three ways:
  // 1. if setting null - getKPIWidgetDefaultSettings
  // 2. if setting?.inputMode is a preset - generate actual date range
  // 3. if setting?.inputMode is a custom - regenerate dates as new Date

  // 1
  if (!setting) {
    const defaultValue = getKPIWidgetDefaultSettings({
      inputMode,
      today,
      minStatsDate,
    })

    return defaultValue
  }

  // is from BE and is a preset
  // in this case we should ignore the saved dates and generate new ones
  const isPreset: boolean =
    setting?.selected &&
    setting?.inputMode &&
    INPUT_MODES.includes(setting.inputMode)

  // 2
  if (isPreset) {
    const generated = getKPIWidgetDefaultSettings({
      inputMode: setting.inputMode,
      today,
      minStatsDate,
    })

    return generated
  }

  // 3
  const transformed: DateRangePickerSelected = {
    selected: {
      start: new Date(setting.selected.start),
      end: new Date(setting.selected.end),
    },
    inputMode: setting.inputMode,
  }

  return transformed
}
