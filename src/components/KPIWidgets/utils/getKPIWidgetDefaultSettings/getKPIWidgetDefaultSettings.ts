import { InputMode } from "@develop/fe-library"
import {
  endOfDay,
  endOfMonth,
  endOfWeek,
  endOfYear,
  Interval,
  isAfter,
  isBefore,
  startOfDay,
  startOfMonth,
  startOfWeek,
  startOfYear,
  subDays,
  subMonths,
  subWeeks,
  subYears,
} from "date-fns"

import { DateRangePickerSelected } from "types/DatePicker"

import { GetKPIWidgetDefaultSettingsProps } from "./getKPIWidgetDefaultSettingsTypes"

export const getKPIWidgetDefaultSettings = ({
  today,
  inputMode,
  minStatsDate,
}: GetKPIWidgetDefaultSettingsProps): DateRangePickerSelected => {
  const checkRangeLimits = (range: Interval) => {
    if (!range) {
      return null
    }

    if (isBefore(range.start, minStatsDate)) {
      range.start = minStatsDate
    }

    if (isAfter(range.end, today)) {
      range.end = today
    }

    if (isBefore(range.end, range.start)) {
      range.end = range.start
    }

    return range
  }

  // TODO: Refactor, using library's utils
  const range: Record<
    Extract<
      InputMode,
      | "today"
      | "yesterday"
      | "lastWeek"
      | "lastMonth"
      | "last7days"
      | "last14days"
      | "last30days"
      | "currentWeek"
      | "currentMonth"
      | "currentYear"
      | "lastYear"
      | "allTime"
    >,
    Interval
  > = {
    today: {
      start: startOfDay(today),
      end: endOfDay(today),
    },
    yesterday: {
      start: startOfDay(subDays(today, 1)),
      end: endOfDay(subDays(today, 1)),
    },
    lastWeek: {
      start: startOfWeek(subWeeks(today, 1), { weekStartsOn: 1 }),
      end: endOfWeek(subWeeks(today, 1), { weekStartsOn: 1 }),
    },
    lastMonth: {
      start: startOfMonth(subMonths(today, 1)),
      end: endOfMonth(subMonths(today, 1)),
    },
    last7days: {
      start: startOfDay(subDays(today, 6)),
      end: endOfDay(today),
    },
    last14days: {
      start: startOfDay(subDays(today, 13)),
      end: endOfDay(today),
    },
    last30days: {
      start: startOfDay(subDays(today, 29)),
      end: endOfDay(today),
    },
    currentWeek: {
      start: startOfWeek(today, { weekStartsOn: 1 }),
      end: endOfWeek(today, { weekStartsOn: 1 }),
    },
    currentMonth: {
      start: startOfMonth(today),
      end: endOfMonth(today),
    },
    currentYear: {
      start: startOfYear(today),
      end: endOfYear(today),
    },
    lastYear: {
      start: startOfYear(subYears(today, 1)),
      end: endOfYear(subYears(today, 1)),
    },
    allTime: {
      start: new Date(minStatsDate),
      end: today,
    },
  }

  const selected = checkRangeLimits(range[inputMode]) || range.today

  const defaultSettings: DateRangePickerSelected = {
    selected,
    inputMode,
  }

  return defaultSettings
}
