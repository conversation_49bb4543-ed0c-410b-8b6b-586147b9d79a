import { InputMode } from "@develop/fe-library"

import { DashboardFiltersParams } from "types"
import { DateRangePickerSelected } from "types/DatePicker"

export type GetWidgetDataParams = {
  selectedWidgetIndex: number | null
  customerKPIWidgetsSettings: DateRangePickerSelected[]
  urlParams: DashboardFiltersParams
}

export type GetWidgetDataReturnType = {
  nextSelectedWidgetIndex: number
  defaultWidgetsList: InputMode[]
}
