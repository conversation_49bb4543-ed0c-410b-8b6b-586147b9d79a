import { InputMode } from "@develop/fe-library"

import { KPIWIDGET_LIST_DEFAULT } from "components/KPIWidgets/constants"

import { INPUT_MODE } from "constants/dateRange"
import { KPI_WIDGET_LIST } from "constants/kpiWidgets"

import {
  GetWidgetDataParams,
  GetWidgetDataReturnType,
} from "./getWidgetDataTypes"

export const getWidgetData = ({
  selectedWidgetIndex,
  customerKPIWidgetsSettings,
  urlParams,
}: GetWidgetDataParams): GetWidgetDataReturnType => {
  let nextSelectedWidgetIndex: number =
    selectedWidgetIndex ?? Number(KPI_WIDGET_LIST.THIRD)

  let defaultWidgetsList: InputMode[] = KPIWIDGET_LIST_DEFAULT

  if (typeof selectedWidgetIndex === "number") {
    // At this point we suppose that customerKPIWidgetsSettings does exist
    const customerInputMode: InputMode | undefined =
      customerKPIWidgetsSettings?.[selectedWidgetIndex]?.inputMode
    const isCustomerInputMode: boolean = !!customerInputMode

    // 1. If urlParams.inputMode none or equals saved
    const shouldUseSessionWidgets: boolean =
      !urlParams.inputMode ||
      (isCustomerInputMode && urlParams.inputMode === customerInputMode)

    // 2. If urlParams.inputMode doesn't equals saved
    const shouldSetAndSelectFirstWidget: boolean =
      !!urlParams.inputMode &&
      isCustomerInputMode &&
      urlParams.inputMode !== customerInputMode

    if (shouldUseSessionWidgets) {
      nextSelectedWidgetIndex = selectedWidgetIndex
      defaultWidgetsList = customerKPIWidgetsSettings.map(
        (setting) => setting.inputMode,
      )
    } else if (shouldSetAndSelectFirstWidget) {
      nextSelectedWidgetIndex = Number(KPI_WIDGET_LIST.FIRST)
      defaultWidgetsList = [
        urlParams.inputMode as InputMode,
        ...customerKPIWidgetsSettings
          .slice(1)
          .map((setting) => setting.inputMode),
      ]
    }
  } else {
    // At this point we do not rely on customerKPIWidgetsSettings
    // 3. If urlParams.inputMode none or currentMonth
    const isDefault: boolean =
      !urlParams.inputMode || urlParams.inputMode === INPUT_MODE.CURRENT_MONTH

    // 4. If urlParams.inputMode have other than currentMonth
    const shouldSetAndSelectFirstWidget: boolean =
      !!urlParams.inputMode && urlParams.inputMode !== INPUT_MODE.CURRENT_MONTH

    if (isDefault) {
      nextSelectedWidgetIndex = Number(KPI_WIDGET_LIST.THIRD)
      defaultWidgetsList = KPIWIDGET_LIST_DEFAULT
    } else if (shouldSetAndSelectFirstWidget) {
      nextSelectedWidgetIndex = Number(KPI_WIDGET_LIST.FIRST)
      defaultWidgetsList = [
        urlParams.inputMode as InputMode,
        ...KPIWIDGET_LIST_DEFAULT.slice(1),
      ]
    }
  }

  return {
    nextSelectedWidgetIndex,
    defaultWidgetsList,
  }
}
