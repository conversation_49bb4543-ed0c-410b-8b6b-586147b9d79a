import { useCallback, useEffect, useState } from "react"
import { InputMode } from "@develop/fe-library"

import { KPIWIDGET_LIST_DEFAULT } from "components/KPIWidgets/constants"
import {
  getKPIWidgetDefaultSettings,
  transformKPIWidgetLoadedSettings,
} from "components/KPIWidgets/utils"

import { useCustomer, useMinStatsDate, useUrlParams } from "hooks"
import { useKPIWidgets } from "hooks/useKPIWidgets"

import { isInputModeValid } from "utils/dateRange/validateInputMode"

import { INPUT_MODE } from "constants/dateRange"
import { KPI_WIDGET_LIST } from "constants/kpiWidgets"

import { getWidgetData } from "./utils"

import { DashboardFiltersParams } from "types"
import { DateRangePickerSelected } from "types/DatePicker"

export const useKPIWidgetsList = () => {
  const { customerId, isCustomerUser } = useCustomer()
  const { minStatsDate, today } = useMinStatsDate()
  const {
    isKPIWidgetsInitiated,
    setKPIWidgetsSettings,
    setIsKPIWidgetsInitiated,
    getSelectedWidget,
    handleSelectWidget,
    getCustomerKPIWidgetsSettings,
    updateCustomerKPIWidgetsSettings,
  } = useKPIWidgets()
  const { urlParams } = useUrlParams<DashboardFiltersParams>()

  const [openPopoverIndex, setOpenPopoverIndex] = useState<number | null>(null)

  const handleOpenPopover = useCallback((index: number): void => {
    setOpenPopoverIndex(index)
  }, [])

  const handleClosePopover = useCallback((): void => {
    setOpenPopoverIndex(null)
  }, [])

  // DESC: Initialize SL user
  const initSLUser = useCallback((): void => {
    const shouldSelectFirstWidget: boolean =
      !!urlParams.inputMode &&
      urlParams.inputMode !== INPUT_MODE.CURRENT_MONTH &&
      isInputModeValid(urlParams.inputMode)

    const defaultWidgetsList: InputMode[] = shouldSelectFirstWidget
      ? [urlParams.inputMode as InputMode, ...KPIWIDGET_LIST_DEFAULT.slice(1)]
      : KPIWIDGET_LIST_DEFAULT

    const widgetsSettings = defaultWidgetsList.map((inputMode) =>
      getKPIWidgetDefaultSettings({
        today,
        inputMode,
        minStatsDate,
      }),
    )

    handleSelectWidget(
      Number(
        shouldSelectFirstWidget ? KPI_WIDGET_LIST.FIRST : KPI_WIDGET_LIST.THIRD,
      ),
    )
    setKPIWidgetsSettings(widgetsSettings)
  }, [
    urlParams.inputMode,
    handleSelectWidget,
    setKPIWidgetsSettings,
    today,
    minStatsDate,
  ])

  // DESC: Initialize Customer user
  const initCustomerUserAlt = useCallback((): void => {
    const customerKPIWidgetsSettings = getCustomerKPIWidgetsSettings()

    const selectedWidgetIndex = getSelectedWidget()

    const { nextSelectedWidgetIndex, defaultWidgetsList } = getWidgetData({
      selectedWidgetIndex,
      customerKPIWidgetsSettings,
      urlParams,
    })

    const widgetsSettings: DateRangePickerSelected[] = defaultWidgetsList.map(
      (inputMode, index) => {
        // DESC: Check if we should generate new inputMode
        const isSelectedWidgetSettingsActual: boolean =
          nextSelectedWidgetIndex === index &&
          customerKPIWidgetsSettings?.[index]?.inputMode === inputMode

        const setting: DateRangePickerSelected | null =
          isSelectedWidgetSettingsActual
            ? customerKPIWidgetsSettings[index]
            : null

        return transformKPIWidgetLoadedSettings({
          inputMode,
          setting,
          today,
          minStatsDate,
        })
      },
    )

    handleSelectWidget(nextSelectedWidgetIndex)
    setKPIWidgetsSettings(widgetsSettings)

    updateCustomerKPIWidgetsSettings(widgetsSettings)
  }, [
    getCustomerKPIWidgetsSettings,
    getSelectedWidget,
    handleSelectWidget,
    minStatsDate,
    setKPIWidgetsSettings,
    today,
    updateCustomerKPIWidgetsSettings,
    urlParams,
  ])

  // DESC: Initialize each widget props
  useEffect(() => {
    if (isKPIWidgetsInitiated) {
      return
    }

    if (isCustomerUser) {
      initCustomerUserAlt()
    } else {
      initSLUser()
    }

    setIsKPIWidgetsInitiated(true)

    return () => {
      setIsKPIWidgetsInitiated(false)
    }
  }, [customerId])

  return {
    isKPIWidgetsInitiated,

    openPopoverIndex,
    handleOpenPopover,
    handleClosePopover,

    selectedWidget: getSelectedWidget(),
    handleSelectWidget,
  }
}
