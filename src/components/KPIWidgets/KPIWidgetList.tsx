import React from "react"
import { Box } from "@develop/fe-library"

import { KPIWidgetModern } from "./components"

import { useKPIWidgetsList } from "./hooks"

import styles from "./kpiWidgets.module.scss"

export const KPIWidgetList = () => {
  const {
    isKPIWidgetsInitiated,
    openPopoverIndex,
    handleOpenPopover,
    handleClosePopover,
    selectedWidget,
    handleSelectWidget,
  } = useKPIWidgetsList()

  if (!isKPIWidgetsInitiated) {
    return null
  }

  return (
    <div className={styles.kpiWidgetsContainer}>
      {Array(4)
        .fill(null)
        .map((_, index) => {
          return (
            <Box key={index} display="block" height="320px">
              <KPIWidgetModern
                index={index}
                isOpenPopover={openPopoverIndex === index}
                isSelected={selectedWidget === index}
                onClosePopover={handleClosePopover}
                onOpenPopover={handleOpenPopover}
                onSelectWidget={handleSelectWidget}
              />
            </Box>
          )
        })}
    </div>
  )
}
