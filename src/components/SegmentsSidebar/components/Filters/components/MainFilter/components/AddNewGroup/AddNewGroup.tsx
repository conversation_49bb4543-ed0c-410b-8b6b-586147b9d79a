import React from "react"
import { useHistory } from "react-router-dom"
import { Box, Icon, Typography } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"

import l from "utils/intl"

export const AddNewGroup = () => {
  const history = useHistory()

  const handleCreateGroup = (): void => {
    history.push({
      ...history.location,
      pathname: ROUTES.BAS_ROUTES.PATH_BAS_EDIT_GROUPS,
      state: { createGroup: true },
    })
  }

  return (
    <Box
      align="center"
      cursor="pointer"
      gap="m"
      height="var(--dropdown-item-height-m)"
      justify="center"
      padding="0 m"
      hasBorder={{
        bottom: true,
      }}
      onClick={handleCreateGroup}
    >
      <Icon color="--color-icon-active" name="icnPlus" size="--icon-size-3" />
      <Typography color="--color-text-link" variant="--font-body-text-3">
        {l("Add new group")}
      </Typography>
    </Box>
  )
}
