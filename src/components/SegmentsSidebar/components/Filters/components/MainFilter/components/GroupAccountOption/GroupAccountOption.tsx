import React, { MouseEvent } from "react"
import { useHistory } from "react-router-dom"
import { Box, Icon, IconPopover, Typography } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"

import { getGroupAccountIconName } from "components/SegmentsSidebar/components/Filters/components/MainFilter/utils"

import l from "utils/intl"

import { GROUP_ACCOUNT_TYPE } from "constants/groupAccount"

import { GroupAccountOptionProps } from "./GroupAccountOptionTypes"
import { Colors } from "@develop/fe-library/dist/lib/types"

import styles from "./groupAccountOption.module.scss"

export const GroupAccountOption = ({
  option,
  isSelected,
}: GroupAccountOptionProps) => {
  const history = useHistory()

  const isGroup: boolean = option.type === GROUP_ACCOUNT_TYPE.GROUP
  const iconColor: Colors = isSelected
    ? "--color-icon-active"
    : "--color-icon-static"

  const handleGroupEdit =
    (value: string) =>
    (event: MouseEvent): void => {
      event.preventDefault()
      event.stopPropagation()

      history.push({
        ...history.location,
        pathname: ROUTES.BAS_ROUTES.PATH_BAS_EDIT_GROUPS,
        state: { editGroup: value },
      })
    }

  return (
    <Box align="center" gap="m" width="100%">
      <Icon
        color={iconColor}
        name={getGroupAccountIconName(option.type)}
        size="--icon-size-3"
      />

      <Typography
        className={styles.label}
        color="--color-text-main"
        title={option.label}
        variant="--font-body-text-9"
      >
        {option.label}
      </Typography>

      {isGroup ? (
        <IconPopover
          content={l("Edit")}
          name="icnEdit"
          placement="top"
          size="--icon-size-2"
          onClick={handleGroupEdit(option.id)}
        />
      ) : null}
    </Box>
  )
}
