import React from "react"
import { Box, Flag, Icon, Typography } from "@develop/fe-library"

import { countryCode } from "utils/countryCode"

import { MarketplaceOption } from "types"

import { MarketplacesOptionProps } from "./MarketplacesOptionTypes"

export const MarketplacesOption = ({
  option,
  isSelected,
}: MarketplacesOptionProps) => {
  const marketplaceOption = Object.hasOwn(option, "country")
    ? (option as MarketplaceOption)
    : null

  return (
    <Box align="center" gap="m" justify="space-between" width="100%">
      <Flag
        borderRadius="--border-radius-circle"
        locale={countryCode(marketplaceOption?.country)}
        size={16}
      />
      <Box flexGrow={1}>
        <Typography color="--color-text-main" variant="--font-body-text-9">
          {marketplaceOption?.title}
        </Typography>
      </Box>

      {isSelected ? (
        <Icon
          color="--color-icon-active"
          name="icnCheck"
          size="--icon-size-2"
        />
      ) : null}
    </Box>
  )
}
