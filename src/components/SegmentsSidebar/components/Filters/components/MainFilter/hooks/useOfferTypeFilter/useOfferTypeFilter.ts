import { FormItemsProps } from "@develop/fe-library"

import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"

import { useOfferType } from "hooks"

import l from "utils/intl"

export const useOfferTypeFilter = () => {
  const { isLoadingProductsOrFilters } = useProductsAndFilters()
  const { options } = useOfferType()

  return {
    type: "select",
    name: "offer_type",
    inputProps: {
      label: l("Offer type"),
      options: options,
      isDisabled: isLoadingProductsOrFilters,
      hasClearIcon: true,
      prefixIcons: [
        {
          name: "icnBlock",
          color: "--color-icon-static",
        },
      ],
      suffixIcons: [
        {
          name: "icnInfoCircle",
          content: l(
            "Filter the Dashboard results to see only order-related transactions.",
          ),
          color: "--color-icon-active",
        },
      ],
    },
    gridItemProps: {
      always: 12,
    },
  } as Partial<FormItemsProps>
}
