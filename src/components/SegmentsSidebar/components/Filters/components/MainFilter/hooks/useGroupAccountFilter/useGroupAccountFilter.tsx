import React, { use<PERSON><PERSON>back, useMemo } from "react"
import { useFormContext, useWatch } from "react-hook-form"
import { useSelector } from "react-redux"

import { groupAccountOptionsSelector } from "selectors/groupAccountSelectors"

import {
  AddNewGroup,
  GroupAccountOption,
} from "components/SegmentsSidebar/components/Filters/components/MainFilter/components"
import { getGroupAccountIconName } from "components/SegmentsSidebar/components/Filters/components/MainFilter/utils"
import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"
import { useFormHandlers } from "components/SegmentsSidebar/hooks/useFormHandlers/useFormHandlers"

import { useSubscription } from "hooks"

import l from "utils/intl"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

export const useGroupAccountFilter = () => {
  const { options: groupAccountOptions } = useSelector(
    groupAccountOptionsSelector,
  )

  const form = useFormContext<DashboardFiltersFormType>()
  const [seller_id] = useWatch({ control: form.control, name: ["seller_id"] })

  const { isLoadingProductsOrFilters } = useProductsAndFilters()
  const { isBasSubscriptionActive } = useSubscription()
  const { handleFormChangeValue } = useFormHandlers()

  const renderDropdownMenu = useCallback(
    ({ menu }): JSX.Element => (
      <>
        {isBasSubscriptionActive ? <AddNewGroup /> : null}
        {menu}
      </>
    ),
    [isBasSubscriptionActive],
  )

  const handleFieldChangeValue = useCallback((): void => {
    form.setValue("marketplace_id", [])

    handleFormChangeValue({
      value: {
        marketplace_id: [],
      },
      shouldFetchProducts: true,
    })
  }, [form, handleFormChangeValue])

  return useMemo(() => {
    const selectedGroupAccount = groupAccountOptions.find(
      (option) => option.value === seller_id,
    )

    return {
      type: "select",
      name: "seller_id",
      inputProps: {
        label: l("Group/Account"),
        options: groupAccountOptions,
        isDisabled: isLoadingProductsOrFilters,
        isGlobal: true,
        prefixIcons: [
          {
            name: getGroupAccountIconName(selectedGroupAccount?.type),
            color: "--color-icon-static",
            size: "--icon-size-3",
          },
        ],
        renderDropdownMenu,
        renderOption: GroupAccountOption,
        onChangeValue: handleFieldChangeValue,
      },
      gridItemProps: {
        always: 12,
      },
    }
  }, [
    handleFieldChangeValue,
    isLoadingProductsOrFilters,
    groupAccountOptions,
    renderDropdownMenu,
    seller_id,
  ])
}
