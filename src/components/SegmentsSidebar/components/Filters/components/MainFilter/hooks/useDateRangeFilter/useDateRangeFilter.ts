import { useMemo } from "react"
import { useSelector } from "react-redux"

import {
  languageSelector,
  translationsSelector,
} from "selectors/mainStateSelectors"

import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"

import { useMinStatsDate } from "hooks"
import { useDateRangeLabels } from "hooks/useDateRangeLabels"

import { CLEAR_TO_CLOSE_LABEL } from "constants/dateRange"
import { LOCALES } from "constants/locales"

export const useDateRangeFilter = () => {
  const { locale } = useSelector(translationsSelector)
  const language = useSelector(languageSelector)

  const { isLoadingProductsOrFilters } = useProductsAndFilters()
  const { minStatsDate, today, minStatsDateLoading } = useMinStatsDate()

  const labels = useDateRangeLabels(CLEAR_TO_CLOSE_LABEL)

  return useMemo(
    () => ({
      type: "dateRange",
      name: "dateRange",
      inputProps: {
        language: LOCALES[locale],
        locale: LOCALES[language],
        labels,
        isClearIconVisible: false,
        isGlobal: true,
        isDisabled: isLoadingProductsOrFilters || minStatsDateLoading,
        fromDate: minStatsDate,
        toDate: today,
      },
      gridItemProps: {
        always: 12,
      },
    }),
    [
      locale,
      language,
      labels,
      isLoadingProductsOrFilters,
      minStatsDateLoading,
      minStatsDate,
      today,
    ],
  )
}
