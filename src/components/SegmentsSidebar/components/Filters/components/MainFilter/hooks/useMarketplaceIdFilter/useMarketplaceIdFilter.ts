import { useCallback, useEffect, useMemo, useState } from "react"
import { useFormContext, useWatch } from "react-hook-form"

import { MarketplacesOption } from "components/SegmentsSidebar/components/Filters/components/MainFilter/components"
import {
  useFormHandlers,
  useProductsAndFilters,
} from "components/SegmentsSidebar/hooks"

import { useMarketplaceOptions } from "hooks"
import usePrevious from "hooks/usePrevious"

import { checkIsArray, isArraysShallowEqual } from "utils/arrayHelpers"
import l from "utils/intl"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { MarketplaceOption } from "types"

export const useMarketplaceIdFilter = () => {
  const form = useFormContext<DashboardFiltersFormType>()
  const [seller_id, marketplace_id] = useWatch({
    control: form.control,
    name: ["seller_id", "marketplace_id"],
  })

  const previousMarketplaceId = usePrevious(marketplace_id)
  const { isLoadingProductsOrFilters } = useProductsAndFilters()
  const { handleFormChangeValue } = useFormHandlers()
  const { getMarketplaceOptions } = useMarketplaceOptions()

  const [shouldUpdateFilters, setShouldUpdateFilters] = useState(false)

  const handleFieldSelect = useCallback((): void => {
    const isMarketplaceIdEqual = isArraysShallowEqual(
      marketplace_id || [],
      previousMarketplaceId || [],
    )

    if (isMarketplaceIdEqual) {
      return
    }

    setShouldUpdateFilters(true)
  }, [marketplace_id, previousMarketplaceId])

  const handleFieldClear = useCallback((): void => {
    setShouldUpdateFilters(true)
  }, [])

  useEffect(() => {
    if (shouldUpdateFilters) {
      setShouldUpdateFilters(false)

      handleFormChangeValue({
        shouldFetchProducts: true,
      })
    }
  }, [shouldUpdateFilters])

  const { disabledOptions, renderSelectedCount } = useMemo(() => {
    const marketplacesOptions = getMarketplaceOptions(seller_id)
    const options = checkIsArray(marketplacesOptions)
      ? [...marketplacesOptions].sort(
          ({ country: prevCountry }, { country: nextCountry }) =>
            prevCountry.localeCompare(nextCountry),
        )
      : []

    const disabledOptions: MarketplaceOption[] = options.map((option) => ({
      ...option,
      disabled: isLoadingProductsOrFilters,
    }))

    const renderSelectedCount = (count: number): string =>
      `${count} ${l("selected")}`

    return {
      disabledOptions,
      renderSelectedCount,
    }
  }, [getMarketplaceOptions, isLoadingProductsOrFilters, seller_id])

  return {
    type: "select",
    name: "marketplace_id",
    inputProps: {
      label: l("Marketplace"),
      options: disabledOptions,
      isDisabled: isLoadingProductsOrFilters,
      prefixIcons: [
        {
          name: "icnFlag",
          color: "--color-icon-static",
        },
      ],
      isMultiSelect: true,
      hasClearIcon: true,
      tagsMode: "count",
      renderSelectedCount,
      renderOption: MarketplacesOption,
      onSelect: handleFieldSelect,
      onClear: handleFieldClear,
    },
    gridItemProps: {
      always: 12,
    },
  }
}
