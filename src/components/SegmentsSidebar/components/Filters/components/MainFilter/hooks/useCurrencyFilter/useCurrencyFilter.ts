import { useCallback, useMemo } from "react"
import { useFormContext, useWatch } from "react-hook-form"
import { useSelector } from "react-redux"

import { currenciesSelector } from "selectors/mainStateSelectors"

import { CurrencyOption } from "components/SegmentsSidebar/components/Filters/components/MainFilter/components"
import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"
import {
  useFormHandlers,
  useProductsAndFilters,
} from "components/SegmentsSidebar/hooks"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import { CURRENCIES_NAMES } from "constants/currencies"

export const useCurrencyFilter = () => {
  const { currencies } = useSelector(currenciesSelector)

  const form = useFormContext<DashboardFiltersFormType>()
  const currency = useWatch({
    control: form.control,
    name: "currency_code",
  })

  const { isLoadingProductsOrFilters } = useProductsAndFilters()
  const { handleFormChangeValue } = useFormHandlers()

  const handleFieldChangeValue = useCallback((): void => {
    handleFormChangeValue()
  }, [handleFormChangeValue])

  return useMemo(() => {
    const options = checkIsArray(currencies)
      ? currencies.map((currency: any) => ({
          ...currency,
          label: currency.title,
          value: currency.id,
        }))
      : []

    return {
      type: "select",
      name: "currency_code",
      inputProps: {
        label: l("Currency"),
        options,
        hasSearch: true,
        isGlobal: true,
        isDisabled: isLoadingProductsOrFilters,
        prefixIcons: currency
          ? [
              {
                isFlag: true,
                locale: CURRENCIES_NAMES[currency]?.countryCode.toLowerCase(),
              },
            ]
          : [],
        renderOption: CurrencyOption,
        onChangeValue: handleFieldChangeValue,
      },
      gridItemProps: {
        always: 12,
      },
    }
  }, [currencies, currency, handleFieldChangeValue, isLoadingProductsOrFilters])
}
