import React from "react"
import { use<PERSON><PERSON><PERSON><PERSON>, useFormContext } from "react-hook-form"
import { ButtonsSwitch, ButtonsSwitchProps } from "@develop/fe-library"

import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"

import l from "utils/intl"

import { PAGE_VIEW, PAGE_VIEW_VALUES } from "constants/pageView"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { PageView } from "types/PageView"

export const usePageView = () => {
  const form = useFormContext<DashboardFiltersFormType>()
  const control = useController({
    control: form.control,
    name: "view",
  })

  const { isLoadingProductsOrFilters } = useProductsAndFilters()

  const isPageView = (
    value: ButtonsSwitchProps["value"],
  ): value is PageView => {
    if (typeof value === "string") {
      return PAGE_VIEW_VALUES.includes(value as PageView)
    }

    return false
  }

  const handleChangeView: ButtonsSwitchProps["onChange"] = (value) => {
    if (isPageView(value)) {
      control.field.onChange(value)
    }
  }

  return {
    type: "component",
    component: (
      <ButtonsSwitch
        isFullWidth
        isDisabled={isLoadingProductsOrFilters}
        value={control.field.value}
        items={[
          {
            label: l("Order view"),
            value: PAGE_VIEW.ORDER,
          },
          {
            label: l("Transaction view"),
            value: PAGE_VIEW.TRANSACTION,
          },
        ]}
        onChange={handleChangeView}
      />
    ),
    gridItemProps: {
      always: 12,
    },
  }
}
