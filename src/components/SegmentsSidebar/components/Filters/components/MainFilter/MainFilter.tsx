import React, { useMemo } from "react"
import { useFormContext } from "react-hook-form"
import { FormItems } from "@develop/fe-library"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import {
  useCurrencyFilter,
  useDateRangeFilter,
  useGroupAccountFilter,
  useMarketplaceIdFilter,
  useOfferTypeFilter,
} from "./hooks"

import { usePageView } from "./hooks/usePageView"

export const MainFilter = () => {
  const form = useFormContext<DashboardFiltersFormType>()

  const groupAccountFilter = useGroupAccountFilter()
  const marketplacesFilter = useMarketplaceIdFilter()
  const offerTypeFilter = useOfferTypeFilter()
  const dateRangeFilter = useDateRangeFilter()
  const currencyFilter = useCurrencyFilter()
  const pageViewFilter = usePageView()

  const items = useMemo(
    () => [
      groupAccountFilter,
      marketplacesFilter,
      offerTypeFilter,
      dateRangeFilter,
      currencyFilter,
      pageViewFilter,
    ],
    [
      currencyFilter,
      dateRangeFilter,
      groupAccountFilter,
      marketplacesFilter,
      offerTypeFilter,
      pageViewFilter,
    ],
  )

  return (
    <FormItems
      // @ts-expect-error
      form={form}
      items={items}
      boxContainerProps={{
        padding: "m",
        tb: {
          padding: "l",
        },
      }}
      gridContainerProps={{
        gap: "m",
      }}
    />
  )
}
