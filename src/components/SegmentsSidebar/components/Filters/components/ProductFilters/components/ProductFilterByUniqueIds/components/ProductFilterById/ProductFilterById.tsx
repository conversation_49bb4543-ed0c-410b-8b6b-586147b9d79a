import React from "react"
import { FormItems } from "@develop/fe-library"
import { useFormContext } from "react-hook-form"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { ProductFilterByIdProps } from "./ProductFilterByIdTypes"
import { useProductFilterById } from "./hooks"

export const ProductFilterById = ({ id }: ProductFilterByIdProps) => {
  const form = useFormContext<DashboardFiltersFormType>()

  const { items } = useProductFilterById({ id })

  return (
    <FormItems
      // @ts-expect-error
      form={form}
      items={items}
      gridContainerProps={{
        gap: "s",
      }}
    />
  )
}
