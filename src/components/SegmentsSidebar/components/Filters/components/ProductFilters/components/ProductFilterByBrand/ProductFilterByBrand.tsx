import React, { useCallback, useMemo } from "react"
import { useFormContext, useWatch } from "react-hook-form"

import { useFormHandlers } from "components/SegmentsSidebar/hooks/useFormHandlers/useFormHandlers"
import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"
import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { ProductFilterCheckboxes } from "../ProductFilterCheckboxes"

export const ProductFilterByBrand = () => {
  const form = useFormContext<DashboardFiltersFormType>()
  const [productId] = useWatch({
    control: form.control,
    name: ["productId"],
  })

  const {
    isLoadingProductsOrFilters,
    isProductFiltersLoading,
    productFilters,
  } = useProductsAndFilters()
  const { handleFormChangeValue } = useFormHandlers()

  const handleFieldChangeValue = useCallback((): void => {
    handleFormChangeValue()
  }, [handleFormChangeValue])

  const brands = useMemo(() => productFilters?.brands || [], [productFilters])

  const isDisabled: boolean = !!productId || isLoadingProductsOrFilters

  return (
    <ProductFilterCheckboxes
      values={brands}
      categoryName="brand"
      isLoading={isProductFiltersLoading}
      isDisabled={isDisabled}
      onFormChangeValue={handleFieldChangeValue}
    />
  )
}
