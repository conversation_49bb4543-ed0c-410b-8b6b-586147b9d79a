import React, { useCallback, useEffect, useMemo, useState } from "react"
import { useFormContext, useWatch } from "react-hook-form"
import debounce from "lodash/debounce"

import { PRODUCT_IDS_KEYS_LIST } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/components/ProductFilterByUniqueIds/constants"
import { ProductFilterSelectOption } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/components/ProductFilterSelectOption"
import { ProductFilterSimpleSelectOption } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/components/ProductFilterSimpleSelectOption"
import { useProductFilterDropdownMenu } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/hooks"
import {
  buildProductFilterOptions,
  BuildProductFilterOptionType,
  makeOptionsUnique,
} from "components/SegmentsSidebar/components/Filters/components/ProductFilters/utils"
import {
  useCompareForm,
  useFetchProducts,
  useProductsAndFilters,
} from "components/SegmentsSidebar/hooks"
import { useFormHandlers } from "components/SegmentsSidebar/hooks/useFormHandlers/useFormHandlers"

import l from "utils/intl"

import {
  DashboardFiltersFormType,
  ProductFiltersExtraFormKeys,
} from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { ProductOptionType, ProductType } from "types/store/salesHistoryReducer"

import {
  CustomKeyToDisplayType,
  CustomValueToDisplay,
  UseProductFilterByIdProps,
} from "./useProductFilterByIdTypes"

export const useProductFilterById = ({ id }: UseProductFilterByIdProps) => {
  const idHas = `has_${id}` as ProductFiltersExtraFormKeys
  const form = useFormContext<DashboardFiltersFormType>()
  const [idValue, hasIdValue, productId] = useWatch({
    control: form.control,
    name: [id, idHas, "productId"],
  })

  const {
    isLoadingProductsOrFilters,
    products,
    hasProducts,
    isProductsIdle,
    isProductsLoading,
    isProductsFulfilledOrRejected,
    findProduct,
    handleClearProducts,
  } = useProductsAndFilters()
  const { handleFormChangeValue } = useFormHandlers()
  const { renderProductDropdownMenu } = useProductFilterDropdownMenu()
  const { fetchProducts } = useFetchProducts()
  const { getIsAnyProductFieldInForm } = useCompareForm()

  const options: ProductOptionType[] = useMemo(() => {
    const selectedProduct = findProduct(idValue, id)

    const productsForOptions: ProductType[] = selectedProduct
      ? [selectedProduct]
      : products

    const productFilterOptionsParams: BuildProductFilterOptionType = {
      valueKey: id,
      labelKey: id,
    }

    return (
      productsForOptions
        .map(buildProductFilterOptions(productFilterOptionsParams))
        .filter(Boolean) as unknown as ProductOptionType[]
    ).reduce<ProductOptionType[]>(makeOptionsUnique, [])
  }, [findProduct, id, idValue, products])

  const [searchValue, setSearchValue] = useState("")

  const searchProduct = useCallback(
    debounce((searchValue: string): void => {
      const values = form.getValues()

      fetchProducts({
        values,
        searchValue: {
          key: id,
          value: searchValue,
        },
      })
    }, 500),
    [],
  )

  const handleSearchValueChange = (value: string): void => {
    setSearchValue(value.trim())
  }

  const handleIsTogglePrevented = useCallback(
    ({ open }: { open: boolean }): boolean => {
      return open
    },
    [],
  )

  const handleFieldChangeValue = useCallback(
    (value: string): void => {
      if (!value) {
        return
      }

      searchProduct(value)

      const selectedProduct = findProduct(value, id)

      if (!selectedProduct) {
        return
      }

      handleFormChangeValue({
        value: {
          [id]: value,
        },
      })
    },
    [findProduct, handleFormChangeValue, id, searchProduct],
  )

  const handleProductClear = useCallback((): void => {
    setSearchValue("")

    const values = form.getValues()

    const nextValues: DashboardFiltersFormType = {
      ...values,
      [id]: "",
    }

    handleFormChangeValue({
      value: {
        [id]: "",
      },
    })

    const isAnyProductFieldInForm = getIsAnyProductFieldInForm(nextValues)

    if (!isAnyProductFieldInForm) {
      handleClearProducts()
    } else {
      fetchProducts({
        values: {
          ...form.getValues(),
          [id]: "",
        },
      })
    }
  }, [
    fetchProducts,
    form,
    getIsAnyProductFieldInForm,
    handleClearProducts,
    handleFormChangeValue,
    id,
  ])

  useEffect(() => {
    const values = form.getValues()

    const isAnyProductFieldInForm = getIsAnyProductFieldInForm(values)

    if (isAnyProductFieldInForm) {
      return
    }

    const isAllowToSearch: boolean =
      searchValue.trim().length >= 3 && !products?.length

    if (isAllowToSearch) {
      searchProduct(searchValue)

      return
    }

    const shouldClearProducts: boolean =
      !isAnyProductFieldInForm && hasProducts && !searchValue

    if (shouldClearProducts) {
      handleClearProducts()
    }
  }, [searchValue])

  const handleHasIdChangeValue = useCallback(
    (value: boolean): void => {
      if (!value) {
        form.setValue(id, "", { shouldDirty: true })

        handleProductClear()
      }
    },
    [form, handleProductClear, id],
  )

  const renderOption = useCallback(
    ({ option }: { option: ProductOptionType }) => {
      const isAsin: boolean = ["asin"].includes(id)
      const customKeyToDisplay: CustomKeyToDisplayType = !isAsin
        ? id
        : undefined
      const customValueToDisplay: CustomValueToDisplay = !isAsin
        ? id.toUpperCase()
        : undefined

      if (id === "sku") {
        return (
          <ProductFilterSimpleSelectOption
            customKeyToDisplay={customKeyToDisplay!}
            customValueToDisplay={customValueToDisplay!}
            option={option}
            searchValue={searchValue}
          />
        )
      }

      return (
        <ProductFilterSelectOption
          customKeyToDisplay={customKeyToDisplay}
          customValueToDisplay={customValueToDisplay}
          isAsin={isAsin}
          isMarketplaceHidden={PRODUCT_IDS_KEYS_LIST.includes(id)}
          option={option}
          searchValue={searchValue}
        />
      )
    },
    [id, searchValue],
  )

  const isDisabled: boolean = !!productId || isLoadingProductsOrFilters
  const isOptionsEmpty: boolean = !options.length
  const isIdle: boolean = isProductsIdle && (!idValue || isOptionsEmpty)
  const label: string = id.toUpperCase()
  const optionHeight: number = ["sku"].includes(id) ? 40 : 60

  const items = useMemo(() => {
    return [
      {
        type: "checkbox",
        name: idHas,
        inputProps: {
          label,
          disabled: isDisabled,
          onChange: handleHasIdChangeValue,
        },
        gridItemProps: {
          always: 12,
        },
      },
      {
        type: "select",
        name: id,
        inputProps: {
          label: l("Search"),
          options,
          hasClearIcon: true,
          hasChevronIcon: false,
          hasSearch: true,
          hasSearchIcon: false,
          optionHeight,
          searchValue,
          isGlobal: true,
          maxVisibleOptions: 5,
          isDisabled,
          isTogglePrevented: handleIsTogglePrevented,
          renderOption,
          renderDropdownMenu: renderProductDropdownMenu({
            title: id.toUpperCase(),
            isIdle,
            isLoading: isProductsLoading,
            isEmpty: isProductsFulfilledOrRejected && isOptionsEmpty,
          }),
          onSearch: handleSearchValueChange,
          onClear: handleProductClear,
          onChangeValue: handleFieldChangeValue,
        },
        gridItemProps: {
          always: 12,
        },
        isVisible: !!hasIdValue,
      },
    ]
  }, [
    idHas,
    label,
    isDisabled,
    handleHasIdChangeValue,
    id,
    options,
    optionHeight,
    searchValue,
    handleIsTogglePrevented,
    renderOption,
    renderProductDropdownMenu,
    isIdle,
    isProductsLoading,
    isProductsFulfilledOrRejected,
    isOptionsEmpty,
    handleProductClear,
    handleFieldChangeValue,
    hasIdValue,
  ])

  return {
    items,
  }
}
