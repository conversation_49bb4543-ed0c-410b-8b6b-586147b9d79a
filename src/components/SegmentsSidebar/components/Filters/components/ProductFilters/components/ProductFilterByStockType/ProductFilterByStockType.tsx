import React, { useCallback, useMemo } from "react"
import { useFormContext, useWatch } from "react-hook-form"

import { useFormHandlers } from "components/SegmentsSidebar/hooks/useFormHandlers/useFormHandlers"
import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"
import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"

import { ProductFilterCheckboxes } from "../ProductFilterCheckboxes"

export const ProductFilterByStockType = () => {
  const form = useFormContext<DashboardFiltersFormType>()
  const [productId] = useWatch({
    control: form.control,
    name: ["productId"],
  })

  const { isLoadingProductsOrFilters, productFilters } = useProductsAndFilters()
  const { handleFormChangeValue } = useFormHandlers()

  const handleFieldChangeValue = useCallback((): void => {
    handleFormChangeValue()
  }, [handleFormChangeValue])

  const stockTypes = useMemo(
    () => productFilters?.stock_types || [],
    [productFilters],
  )

  const isDisabled: boolean = !!productId || isLoadingProductsOrFilters

  return (
    <ProductFilterCheckboxes
      categoryName="stock_type"
      values={stockTypes}
      isLoading={isLoadingProductsOrFilters}
      isDisabled={isDisabled}
      onFormChangeValue={handleFieldChangeValue}
      hasSearch={false}
      isSelectedOnTop={false}
    />
  )
}
