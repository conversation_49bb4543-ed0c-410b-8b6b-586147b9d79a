import React, { useCallback, useMemo } from "react"
import { useFormContext, useWatch } from "react-hook-form"

import { useFormHandlers } from "components/SegmentsSidebar/hooks/useFormHandlers/useFormHandlers"
import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"
import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { ProductFilterCheckboxes } from "../ProductFilterCheckboxes"

export const ProductFilterByProductType = () => {
  const form = useFormContext<DashboardFiltersFormType>()
  const [productId] = useWatch({
    control: form.control,
    name: ["productId"],
  })

  const { isLoadingProductsOrFilters, productFilters } = useProductsAndFilters()
  const { handleFormChangeValue } = useFormHandlers()

  const handleFieldChangeValue = useCallback((): void => {
    handleFormChangeValue()
  }, [handleFormChangeValue])

  const productTypes = useMemo(
    () => productFilters?.product_types || [],
    [productFilters],
  )

  const isDisabled: boolean = !!productId || isLoadingProductsOrFilters

  return (
    <ProductFilterCheckboxes
      values={productTypes}
      categoryName="product_type"
      isLoading={isLoadingProductsOrFilters}
      isDisabled={isDisabled}
      onFormChangeValue={handleFieldChangeValue}
    />
  )
}
