import React, { useCallback } from "react"
import { FormItems } from "@develop/fe-library"
import { useFormContext, useWatch } from "react-hook-form"

import l from "utils/intl"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"
import { useFormHandlers } from "components/SegmentsSidebar/hooks/useFormHandlers/useFormHandlers"
import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"

export const ProductFilterByAdultProduct = () => {
  const form = useFormContext<DashboardFiltersFormType>()
  const [productId] = useWatch({
    control: form.control,
    name: ["productId"],
  })

  const { isLoadingProductsOrFilters } = useProductsAndFilters()
  const { handleFormChangeValue } = useFormHandlers()

  const handleFieldChangeValue = useCallback((): void => {
    handleFormChangeValue()
  }, [handleFormChangeValue])

  const isDisabled: boolean = !!productId || isLoadingProductsOrFilters

  return (
    <FormItems
      // @ts-expect-error
      form={form}
      boxContainerProps={{
        padding: "m",
        tb: {
          padding: "m l",
        },
      }}
      items={[
        {
          type: "checkbox",
          name: "adult_product",
          inputProps: {
            label: l("Adult"),
            disabled: isDisabled,
            onChange: handleFieldChangeValue,
          },
        },
      ]}
    />
  )
}
