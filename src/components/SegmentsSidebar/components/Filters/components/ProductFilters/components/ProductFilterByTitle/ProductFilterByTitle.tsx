import React, { useCallback, useEffect, useMemo, useState } from "react"
import { useFormContext, useWatch } from "react-hook-form"
import { FormItems } from "@develop/fe-library"
import debounce from "lodash/debounce"

import { useProductFilterDropdownMenu } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/hooks"
import { buildProductFilterOptions } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/utils"
import {
  useCompareForm,
  useFetchProducts,
  useProductsAndFilters,
} from "components/SegmentsSidebar/hooks"
import { useFormHandlers } from "components/SegmentsSidebar/hooks/useFormHandlers/useFormHandlers"

import l from "utils/intl"

import { ProductFilterSelectOption } from "../ProductFilterSelectOption"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { ProductOptionType } from "types/store/salesHistoryReducer"

export const ProductFilterByTitle = () => {
  const form = useFormContext<DashboardFiltersFormType>()
  const [productId] = useWatch({
    control: form.control,
    name: ["productId"],
  })

  const {
    isLoadingProductsOrFilters,
    products,
    hasProducts,
    isProductsIdle,
    isProductsLoading,
    isProductsFulfilledOrRejected,
    findProduct,
    handleClearProducts,
  } = useProductsAndFilters()
  const { handleFormChangeValue } = useFormHandlers()
  const { fetchProducts } = useFetchProducts()
  const { renderProductDropdownMenu } = useProductFilterDropdownMenu()
  const { getIsAnyProductFieldInForm } = useCompareForm()

  const [searchValue, setSearchValue] = useState("")

  const options: ProductOptionType[] = useMemo(() => {
    const selectedProduct = findProduct(productId, "id")
    const selectedProductRepricer = findProduct(productId, "repricer_id")

    if (selectedProductRepricer) {
      return [selectedProductRepricer]
        .map(
          buildProductFilterOptions({
            valueKey: "repricer_id",
          }),
        )
        .filter(Boolean) as ProductOptionType[]
    }

    const options = selectedProduct ? [selectedProduct] : products

    return options
      .map(buildProductFilterOptions())
      .filter(Boolean) as ProductOptionType[]
  }, [findProduct, productId, products])

  const searchProduct = useCallback(
    debounce((value: string): void => {
      const values = form.getValues()

      fetchProducts({
        values,
        searchValue: {
          key: "title",
          value,
        },
      })
    }, 500),
    [],
  )

  const handleSearchValueChange = (value: string): void => {
    setSearchValue(value.trim())
  }

  const renderOption = useCallback(
    ({ option }: { option: ProductOptionType }) => (
      <ProductFilterSelectOption
        isAsin
        isSku
        isMarketplaceHidden={false}
        option={option}
        searchValue={searchValue}
      />
    ),
    [searchValue],
  )

  const handleIsTogglePrevented = useCallback(
    ({ open }: { open: boolean }): boolean => {
      return open
    },
    [],
  )

  const handleFieldChangeValue = useCallback((): void => {
    handleFormChangeValue()
  }, [handleFormChangeValue])

  const handleClear = useCallback((): void => {
    setSearchValue("")
  }, [])

  useEffect(() => {
    const values = form.getValues()

    const isAnyProductFieldInForm = getIsAnyProductFieldInForm(values)

    const isAllowToSearch: boolean = searchValue.trim().length >= 3

    if (isAllowToSearch) {
      searchProduct(searchValue)

      return
    }

    const shouldClearProducts: boolean =
      !isAnyProductFieldInForm && hasProducts && !searchValue

    if (shouldClearProducts) {
      handleClearProducts()
    }
  }, [searchValue])

  const isOptionsEmpty: boolean = !options.length
  const isIdle: boolean = isProductsIdle && (!productId || isOptionsEmpty)

  return (
    <FormItems
      // @ts-expect-error
      form={form}
      items={[
        {
          type: "select",
          name: "productId",
          inputProps: {
            label: l("Search"),
            options,
            hasClearIcon: true,
            hasChevronIcon: false,
            hasSearch: true,
            hasSearchIcon: false,
            optionHeight: 60,
            searchValue,
            isGlobal: true,
            isDisabled: isLoadingProductsOrFilters,
            isTogglePrevented: handleIsTogglePrevented,
            maxVisibleOptions: 5,
            renderOption,
            renderDropdownMenu: renderProductDropdownMenu({
              title: l("Title"),
              isIdle,
              isLoading: isProductsLoading,
              isEmpty: isProductsFulfilledOrRejected && isOptionsEmpty,
            }),
            onSearch: handleSearchValueChange,
            onClear: handleClear,
            onChangeValue: handleFieldChangeValue,
          },
          gridItemProps: {
            always: 12,
          },
        },
      ]}
    />
  )
}
