import React, { useCallback, useEffect, useMemo, useState } from "react"
import { useFormContext, useWatch } from "react-hook-form"
import { FormItems } from "@develop/fe-library"
import debounce from "lodash/debounce"

import { useProductFilterDropdownMenu } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/hooks"
import {
  buildProductFilterOptions,
  BuildProductFilterOptionType,
  makeOptionsUnique,
} from "components/SegmentsSidebar/components/Filters/components/ProductFilters/utils"
import {
  useCompareForm,
  useFetchProducts,
  useProductsAndFilters,
} from "components/SegmentsSidebar/hooks"
import { useFormHandlers } from "components/SegmentsSidebar/hooks/useFormHandlers/useFormHandlers"

import l from "utils/intl"

import { ProductFilterSelectOption } from "../ProductFilterSelectOption"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { ProductOptionType } from "types/store/salesHistoryReducer"

export const ProductFilterByParentAsin = () => {
  const form = useFormContext<DashboardFiltersFormType>()
  const [productId] = useWatch({
    control: form.control,
    name: ["productId"],
  })

  const {
    isLoadingProductsOrFilters,
    products,
    isProductsIdle,
    isProductsLoading,
    isProductsFulfilledOrRejected,
    findProduct,
    hasProducts,
    handleClearProducts,
  } = useProductsAndFilters()
  const { handleFormChangeValue } = useFormHandlers()
  const { fetchProducts } = useFetchProducts()
  const { renderProductDropdownMenu } = useProductFilterDropdownMenu()
  const { getIsAnyProductFieldInForm } = useCompareForm()

  const [searchValue, setSearchValue] = useState("")

  const options: ProductOptionType[] = useMemo(() => {
    const selectedProduct = findProduct(productId)

    const optionsParams: BuildProductFilterOptionType = {
      valueKey: "parent_asin",
      labelKey: "parent_asin",
    }

    const options = selectedProduct ? [selectedProduct] : products

    return (
      options
        .map(buildProductFilterOptions(optionsParams))
        .filter(Boolean) as ProductOptionType[]
    ).reduce<ProductOptionType[]>(makeOptionsUnique, [])
  }, [findProduct, productId, products])

  const searchProduct = useCallback(
    debounce((value: string): void => {
      const values = form.getValues()

      fetchProducts({
        values,
        searchValue: {
          key: "parent_asin",
          value,
        },
      })
    }, 500),
    [],
  )

  const handleSearchValueChange = (value: string): void => {
    setSearchValue(value.trim())
  }

  const renderOption = useCallback(
    ({ option }: { option: ProductOptionType }) => {
      return (
        <ProductFilterSelectOption
          isMarketplaceHidden
          customKeyToDisplay="parent_asin"
          customValueToDisplay={"Parent ASIN"}
          isAsin={false}
          option={option}
          searchValue={searchValue}
        />
      )
    },
    [searchValue],
  )

  const handleIsTogglePrevented = useCallback(
    ({ open }: { open: boolean }): boolean => {
      return open
    },
    [],
  )

  const handleFieldChangeValue = useCallback(
    (value: string): void => {
      if (!value) {
        return
      }

      if (!hasProducts) {
        searchProduct(value)
      }
      const selectedProduct = findProduct(value, "parent_asin")

      if (!selectedProduct) {
        return
      }

      handleFormChangeValue({
        value: {
          parent_asin: value,
        },
      })
    },
    [findProduct, handleFormChangeValue, hasProducts, searchProduct],
  )

  const handleClear = useCallback((): void => {
    setSearchValue("")
  }, [])

  useEffect(() => {
    const values = form.getValues()

    const isAnyProductFieldInForm: boolean = getIsAnyProductFieldInForm(values)

    if (isAnyProductFieldInForm) {
      return
    }

    const isAllowToSearch: boolean =
      searchValue.trim().length >= 3 && !products?.length

    if (isAllowToSearch) {
      searchProduct(searchValue)

      return
    }

    const shouldClearProducts: boolean =
      !isAnyProductFieldInForm && hasProducts && !searchValue

    if (shouldClearProducts) {
      handleClearProducts()
    }
  }, [searchValue])

  const isOptionsEmpty: boolean = !options.length
  const isDisabled: boolean = !!productId || isLoadingProductsOrFilters
  const isIdle: boolean = isProductsIdle && (!productId || isOptionsEmpty)

  return (
    <FormItems
      // @ts-expect-error
      form={form}
      items={[
        {
          type: "select",
          name: "parent_asin",
          inputProps: {
            label: l("Search"),
            options,
            hasClearIcon: true,
            hasChevronIcon: false,
            hasSearch: true,
            hasSearchIcon: false,
            optionHeight: 60,
            searchValue,
            isGlobal: true,
            isDisabled,
            isTogglePrevented: handleIsTogglePrevented,
            maxVisibleOptions: 5,
            renderOption,
            renderDropdownMenu: renderProductDropdownMenu({
              title: l("Parent"),
              isIdle,
              isLoading: isProductsLoading,
              isEmpty: isProductsFulfilledOrRejected && isOptionsEmpty,
            }),
            onSearch: handleSearchValueChange,
            onClear: handleClear,
            onChangeValue: handleFieldChangeValue,
          },
          gridItemProps: {
            always: 12,
          },
        },
      ]}
    />
  )
}
