import { ProductFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { ProductFiltersMultipleType } from "types/store/salesHistoryReducer"

export type ProductFilterCheckboxesProps = {
  values: ProductFiltersMultipleType
  categoryName: keyof ProductFiltersFormType
  isLoading?: boolean
  isDisabled?: boolean
  isSelectedOnTop?: boolean
  hasSearch?: boolean
  onFormChangeValue?: (value: any) => void
}
