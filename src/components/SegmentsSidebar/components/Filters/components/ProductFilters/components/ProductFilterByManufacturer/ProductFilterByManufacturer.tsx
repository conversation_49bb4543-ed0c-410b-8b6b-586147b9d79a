import React, { useCallback, useMemo } from "react"
import { useFormContext, useWatch } from "react-hook-form"

import { useFormHandlers } from "components/SegmentsSidebar/hooks/useFormHandlers/useFormHandlers"
import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"
import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { ProductFilterCheckboxes } from "../ProductFilterCheckboxes"

export const ProductFilterByManufacturer = () => {
  const form = useFormContext<DashboardFiltersFormType>()
  const [productId] = useWatch({
    control: form.control,
    name: ["productId"],
  })

  const { isLoadingProductsOrFilters, productFilters } = useProductsAndFilters()
  const { handleFormChangeValue } = useFormHandlers()

  const handleFieldChangeValue = useCallback((): void => {
    handleFormChangeValue()
  }, [handleFormChangeValue])

  const manufacturers = useMemo(
    () => productFilters?.manufacturers || [],
    [productFilters],
  )

  const isDisabled: boolean = !!productId || isLoadingProductsOrFilters

  return (
    <ProductFilterCheckboxes
      values={manufacturers}
      categoryName="manufacturer"
      isLoading={isLoadingProductsOrFilters}
      isDisabled={isDisabled}
      onFormChangeValue={handleFieldChangeValue}
    />
  )
}
