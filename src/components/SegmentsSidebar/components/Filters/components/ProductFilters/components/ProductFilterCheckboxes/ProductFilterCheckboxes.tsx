import React, { useEffect, useMemo, useState } from "react"
import { useFormContext, useWatch } from "react-hook-form"
import {
  Box,
  FormItems,
  Option,
  Skeleton,
  Spinner,
  TextInput,
  Typography,
} from "@develop/fe-library"
import { getObjectKeys } from "@develop/fe-library/dist/utils"

import { buildCheckboxRadioOptions } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/utils"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { ProductFilterCheckboxesProps } from "./ProductFilterCheckboxesTypes"

export const ProductFilterCheckboxes = ({
  values,
  isLoading = false,
  isDisabled = false,
  isSelectedOnTop = true,
  categoryName,
  hasSearch = true,
  onFormChangeValue,
}: ProductFilterCheckboxesProps) => {
  const form = useFormContext<DashboardFiltersFormType>()
  const [categoryField] = useWatch({
    control: form.control,
    name: [categoryName],
  }) as string[]

  const [searchValue, setSearchValue] = useState("")

  const options: Option[] = useMemo(() => {
    const sortedOptions = buildCheckboxRadioOptions(values)

    if (!isSelectedOnTop) {
      return sortedOptions
    }

    return sortedOptions.sort((a, b) => {
      const isPlaceOnTop: boolean =
        // @ts-expect-error
        categoryField?.includes(a.value) && !categoryField?.includes(b.value)

      if (isPlaceOnTop) {
        return -1
      }

      const isPlaceOnBottom: boolean =
        // @ts-expect-error
        !categoryField?.includes(a.value) && categoryField?.includes(b.value)

      if (isPlaceOnBottom) {
        return 1
      }

      return 0
    })
  }, [categoryField, isSelectedOnTop, values])

  const filteredOptions: Option[] = useMemo(() => {
    if (!searchValue) {
      return options
    }

    return options.filter((item) =>
      String(item.value).toLowerCase().includes(searchValue.toLowerCase()),
    )
  }, [options, searchValue])

  // Validate checked values with BE
  const differenceArray: string[] = useMemo(() => {
    if (!Array.isArray(categoryField)) {
      return []
    }

    const categoryValues: string[] = options.map((option) => {
      return String(option.value)
    })

    return categoryField.filter((category) => {
      return categoryValues.includes(category)
    })
  }, [categoryField, options])

  useEffect(() => {
    if (checkIsArray(differenceArray)) {
      form.setValue(categoryName, differenceArray, { shouldDirty: false })
    }
  }, [values])

  const handleSearchChange = (value: string) => {
    setSearchValue(value)
  }

  const handleFieldChangeValue = (value: string) => {
    onFormChangeValue?.(value)

    if (searchValue) {
      setSearchValue("")
    }
  }

  const isOptions: boolean = !!filteredOptions.length
  const isValues: boolean = !!getObjectKeys(values).length
  const isSkeletonShown: boolean = isLoading && !isValues
  const isEmptyShown: boolean = !isLoading && !isValues
  const isUpdatingSpinnerShown: boolean = isLoading && isValues

  return (
    <Box flexDirection="column" gap="m" maxWidth="100%">
      {isSkeletonShown ? <Skeleton width="100%" /> : null}

      {isEmptyShown ? (
        <Typography variant="--font-body-text-7">
          {l("No options for this category")}
        </Typography>
      ) : null}

      {isValues ? (
        <>
          {hasSearch ? (
            <TextInput
              hasClearIcon
              isFullWidth
              isDisabled={isDisabled}
              placeholder={l("Search")}
              value={searchValue}
              onChange={handleSearchChange}
            />
          ) : null}

          <Box
            display="block"
            flexDirection="column"
            maxHeight="200px"
            overflowX="hidden"
            overflowY="auto"
            position="relative"
          >
            {isUpdatingSpinnerShown ? (
              <Box
                align="center"
                height="100%"
                justify="center"
                left={0}
                position="absolute"
                top={0}
                width="100%"
                zIndex={1}
              >
                <Spinner />
              </Box>
            ) : null}

            {!isOptions ? (
              <Typography variant="--font-body-text-7">
                {l("No options from search")}
              </Typography>
            ) : null}

            <FormItems
              // @ts-expect-error
              form={form}
              items={[
                {
                  type: "checkboxGroup",
                  name: categoryName,
                  inputProps: {
                    items: filteredOptions,
                    isDisabled,
                    onChange: handleFieldChangeValue,
                    gridItemTemplateProps: {
                      always: 12,
                    },
                  },
                  gridItemProps: {
                    always: 12,
                  },
                  isVisible: !!options.length,
                },
              ]}
            />
          </Box>
        </>
      ) : null}
    </Box>
  )
}
