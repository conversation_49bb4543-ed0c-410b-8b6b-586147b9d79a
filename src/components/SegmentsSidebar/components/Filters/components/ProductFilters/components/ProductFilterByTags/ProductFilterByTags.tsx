import React, { useCallback, useEffect, useState } from "react"
import { useFormContext, useWatch } from "react-hook-form"
import { Box, FormItems, Typography } from "@develop/fe-library"

import {
  useFormHandlers,
  useProductsAndFilters,
} from "components/SegmentsSidebar/hooks"

import usePrevious from "hooks/usePrevious"

import { isArraysShallowEqual } from "utils/arrayHelpers"
import l from "utils/intl"

import { useProductFilterByTags } from "./hooks"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

export const ProductFilterByTags = () => {
  const form = useFormContext<DashboardFiltersFormType>()
  const [productId, tags] = useWatch({
    control: form.control,
    name: ["productId", "tags"],
  })

  const previousTags = usePrevious(tags)
  const { isLoadingProductsOrFilters } = useProductsAndFilters()
  const { handleFormChangeValue } = useFormHandlers()
  const { options, isTagsLoading } = useProductFilterByTags()

  const [shouldUpdateFilters, setShouldUpdateFilters] = useState(false)

  const handleFieldChangeValue = useCallback((): void => {
    const isTagsEqual = isArraysShallowEqual(tags || [], previousTags || [])

    if (isTagsEqual) {
      return
    }

    setShouldUpdateFilters(true)
  }, [previousTags, tags])

  const handleRemoveSelectedItem = useCallback((): void => {
    setShouldUpdateFilters(true)
  }, [])

  useEffect(() => {
    if (shouldUpdateFilters) {
      setShouldUpdateFilters(false)

      handleFormChangeValue()
    }
  }, [shouldUpdateFilters])

  const isDisabled: boolean =
    !!productId || isLoadingProductsOrFilters || isTagsLoading

  return (
    <Box flexDirection="column" gap="m" maxWidth="100%">
      <FormItems
        // @ts-expect-error
        form={form}
        items={[
          {
            type: "select",
            name: "tags",
            inputProps: {
              label: l("Tags"),
              options,
              hasChevronIcon: true,
              isMultiSelect: true,
              isMultiLine: true,
              isFullWidth: true,
              hasSearch: true,
              hasSearchIcon: false,
              isGlobal: true,
              isDisabled,
              maxVisibleOptions: 5,
              onSelect: handleFieldChangeValue,
              onRemoveSelectedItem: handleRemoveSelectedItem,
              renderNoDataPlaceholder: () => {
                return (
                  <Box
                    align="center"
                    justify="center"
                    minWidth="100%"
                    padding="m"
                  >
                    <Typography variant="--font-body-text-7">
                      {l("No tags found")}
                    </Typography>
                  </Box>
                )
              },
            },
            gridItemProps: {
              always: 12,
            },
          },
        ]}
      />
    </Box>
  )
}
