import { BaseSyntheticEvent, useCallback, useMemo, useState } from "react"
import { useFormContext } from "react-hook-form"

import { useTransformValues } from "components/SegmentsSidebar/components/Filters/hooks"
import {
  useCompareForm,
  useFormHandlers,
  useProductsAndFilters,
  useSegments,
} from "components/SegmentsSidebar/hooks"

import { useHashTab } from "hooks"

import { DASHBOARD_TABS_INITIAL_URL_PARAMS } from "constants/dashboard"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { SegmentRequestBodyType } from "types/RequestParams/Segments"

import {
  HandleSaveAsProps,
  UseFiltersFooterProps,
} from "./useFiltersFooterTypes"

export const useFiltersFooter = ({
  onFormReset,
  onFormSubmit,
}: UseFiltersFooterProps) => {
  const form = useFormContext<DashboardFiltersFormType>()
  const { getValues } = form
  const values = getValues()

  const {
    transformValuesToSegment,
    transformValuesToUrlParams,
    transformUrlParamsToValues,
  } = useTransformValues()
  const { isLoadingProductsOrFilters } = useProductsAndFilters()
  const { handleFormChangeValue } = useFormHandlers()
  const {
    getSegments,
    createSegment,
    updateSegment,
    clearAppliedSegment,
    clearSegmentToEdit,
  } = useSegments()
  const { compareFormValuesWithUrlParams } = useCompareForm()
  const { hashTab } = useHashTab({ tabKey: "tab" })

  const handleResetForm = useCallback((): void => {
    // The order matters
    onFormReset()
    handleFormChangeValue()
  }, [handleFormChangeValue, onFormReset])

  const handleFormSubmit = useCallback(
    (event?: BaseSyntheticEvent): void => {
      onFormSubmit(event)
    },
    [onFormSubmit],
  )

  const [isSaveAsModalOpen, setIsSaveAsModalOpen] = useState(false)

  const handleSaveAsModalOpen = useCallback((): void => {
    setIsSaveAsModalOpen(true)
  }, [])

  const handleSaveAsModalClose = useCallback((): void => {
    setIsSaveAsModalOpen(false)
  }, [])

  const handleSaveAs = ({
    name,
    values: formValues,
    id,
    onSuccess = () => {},
    onFailure = () => {},
  }: HandleSaveAsProps): void => {
    const segmentParams = transformValuesToSegment(formValues)

    const segment: SegmentRequestBodyType = {
      filters: segmentParams,
      name,
    }

    const handleSuccess = (): void => {
      getSegments()

      clearAppliedSegment()
      clearSegmentToEdit()

      handleSaveAsModalClose()

      onSuccess?.()
    }

    if (id) {
      updateSegment(id, segment, handleSuccess, onFailure)
    } else {
      createSegment(segment, handleSuccess, onFailure)
    }
  }

  const isResetDisabled: boolean = useMemo(() => {
    const defaultValues = transformUrlParamsToValues(
      hashTab ? DASHBOARD_TABS_INITIAL_URL_PARAMS[hashTab] : {},
    )
    const defaultUrlParams = transformValuesToUrlParams(defaultValues)
    const compared = compareFormValuesWithUrlParams(values, defaultUrlParams)

    return !compared.isDirty || isLoadingProductsOrFilters
  }, [
    compareFormValuesWithUrlParams,
    hashTab,
    isLoadingProductsOrFilters,
    transformUrlParamsToValues,
    transformValuesToUrlParams,
    values,
  ])

  const isApplyDisabled: boolean = useMemo(() => {
    const compared = compareFormValuesWithUrlParams(values)

    return !compared.isDirty || isLoadingProductsOrFilters
  }, [compareFormValuesWithUrlParams, isLoadingProductsOrFilters, values])

  const isSaveAsDisabled = isLoadingProductsOrFilters

  return {
    values,

    isSaveAsModalOpen,
    handleSaveAsModalOpen,
    handleSaveAsModalClose,

    isResetDisabled,
    isApplyDisabled,
    isSaveAsDisabled,

    handleResetForm,
    handleSaveAs,
    handleFormSubmit,
  }
}
