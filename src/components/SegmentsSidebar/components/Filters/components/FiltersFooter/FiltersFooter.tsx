import React from "react"
import { <PERSON>, Button } from "@develop/fe-library"

import { FiltersSaveAsSegmentModal } from "components/SegmentsSidebar/components/FiltersSaveAsSegmentModal"

import l from "utils/intl"

import { useFiltersFooter } from "./hooks"

import { FiltersFooterProps } from "./FiltersFooterTypes"

export const FiltersFooter = ({
  onFormReset,
  onFormSubmit,
}: FiltersFooterProps) => {
  const {
    values,

    isSaveAsModalOpen,
    handleSaveAsModalOpen,
    handleSaveAsModalClose,

    isApplyDisabled,
    isResetDisabled,
    isSaveAsDisabled,

    handleResetForm,
    handleSaveAs,
    handleFormSubmit,
  } = useFiltersFooter({
    onFormReset,
    onFormSubmit,
  })

  return (
    <>
      <Box
        flexDirection="column"
        gap="m"
        padding="m"
        hasBorder={{
          top: true,
        }}
      >
        <Button
          disabled={isResetDisabled}
          variant="secondary"
          onClick={handleResetForm}
        >
          {l("Reset")}
        </Button>
        <Button disabled={isApplyDisabled} onClick={handleFormSubmit}>
          {l("Apply")}
        </Button>
        <Button disabled={isSaveAsDisabled} onClick={handleSaveAsModalOpen}>
          {l("Save as")}
        </Button>
      </Box>

      {isSaveAsModalOpen ? (
        <FiltersSaveAsSegmentModal
          values={values}
          onClose={handleSaveAsModalClose}
          onOk={handleSaveAs}
        />
      ) : null}
    </>
  )
}
