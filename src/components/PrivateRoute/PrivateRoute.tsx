import React, { lazy } from "react"
import { useSelector } from "react-redux"
import { Route, Redirect, useLocation } from "react-router-dom"

import { useSubscription } from "hooks"

import { permissionsSelector } from "selectors/mainStateSelectors"

import { SubscriptionAlert } from "./components"
import { NoAccessProps } from "./components/NoAccess"
import { FORBIDDEN_MESSAGE } from "./constants"

import { LocationState } from "types"
import { PrivateRouteProps } from "./PrivateRouteTypes"

const Forbidden = lazy(() => import("./components/NoAccess"))

export const PrivateRoute = ({
  component: RouteComponent,
  permission,
  path,
  exact,
  isRestricted = false,
}: PrivateRouteProps) => {
  const location = useLocation<LocationState>()

  const { isBasModuleStarted } = useSubscription()

  const permissions = useSelector(permissionsSelector)

  const redirectUrl = location.state?.redirectUrl || ""

  const [pathname, search] = redirectUrl.split("?")
  const redirect = {
    pathname,
    search: search ? `?${search[1]}` : "",
  } as const

  const shouldRedirect: boolean =
    !!redirectUrl && location.pathname !== redirect.pathname

  const isForbidden: boolean =
    (!!permission && !permissions[permission]) || isRestricted

  const NO_SUBSCRIPTION_MESSAGE: NoAccessProps = isBasModuleStarted
    ? FORBIDDEN_MESSAGE.BAS_SUBSCRIPTION_EXPIRED
    : FORBIDDEN_MESSAGE.BAS_MODULE_NEVER_STARTED

  const forbiddenProps: NoAccessProps = isRestricted
    ? NO_SUBSCRIPTION_MESSAGE
    : FORBIDDEN_MESSAGE.BAS_NO_PERMISSION

  return (
    <Route
      exact={exact}
      path={path}
      render={({ location }) =>
        shouldRedirect ? (
          <Redirect
            to={{
              pathname: redirect.pathname,
              search: redirect.search,
              state: {
                from: location,
                userRedirect: true,
              },
            }}
          />
        ) : (
          <>
            <SubscriptionAlert />

            {isForbidden ? (
              <Forbidden {...forbiddenProps} />
            ) : (
              <RouteComponent />
            )}
          </>
        )
      }
    />
  )
}
