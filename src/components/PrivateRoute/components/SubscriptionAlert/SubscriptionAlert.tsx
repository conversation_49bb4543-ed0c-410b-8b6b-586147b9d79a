import React from "react"
import { Alert, Box } from "@develop/fe-library"

import { useSubscriptionAlert } from "./hooks/useSubscriptionAlert/useSubscriptionAlert"

export const SubscriptionAlert = () => {
  const { action, messageText, shouldShowAlert } = useSubscriptionAlert()

  if (!shouldShowAlert) {
    return null
  }

  return (
    <Box
      display="block"
      padding="l"
      hasBorder={{
        bottom: true,
      }}
      zIndex={3}
      backgroundColor="--color-main-background"
    >
      <Alert message={messageText} hasIcon action={action} alertType="info" />
    </Box>
  )
}
