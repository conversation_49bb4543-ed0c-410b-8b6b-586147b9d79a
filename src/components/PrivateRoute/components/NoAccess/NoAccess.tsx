import React, { memo } from "react"

import { Content, Footer } from "components/Layout"
import AppHeader from "components/shared/AppHeader/AppHeader"
import SimpleFooter from "components/SimpleFooter"
import { InfoPage } from "components/InfoPage"

import { NoAccessProps } from "./NoAccessTypes"

const NoAccessComponent = ({
  descriptionMessage = "Please contact your account administrator.",
  descriptionTitle,
  pageTitle,
}: NoAccessProps) => {
  return (
    <Content>
      <AppHeader title={pageTitle} />

      <InfoPage
        description={descriptionMessage}
        title={descriptionTitle}
        type="permissionView"
      />

      <Footer>
        <SimpleFooter />
      </Footer>
    </Content>
  )
}

export const NoAccess = memo(NoAccessComponent)
