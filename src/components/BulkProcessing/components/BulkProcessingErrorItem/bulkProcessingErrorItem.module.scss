@import 'assets/styles/variables.scss';


.warningItem {
  display: inline-block;
  width: 16.6666%;
  margin-bottom: 5px;
  :global(.ant-tag) {
    line-height: 13px;
    font-size: 11px;
    padding: 0 4px;
    padding-top: 1px;
    transform: translateY(-0.5px)
  }
  a {
    color: $primary;
  }
}

.popoverBox {
  max-height: 200px;
  padding-right: var(--padding-l);
  overflow: auto;
}

.errorItem {
  font-size: 12px;
  color: $input-icon-color;
}

@media (max-width: $md) { 
  .warningItem {
    width: 20%;
  }
}

@media (max-width: $sm) { 
  .warningItem {
    width: 33.3333%;
  }
}

@media (max-width: 370px) { 
  .warningItem {
    width: 50%;
  }
}

