import React from "react"
import PropTypes from "prop-types"
import cn from "classnames"

import { Typography } from "@develop/fe-library"

import l from "utils/intl"
import { checkIsArray } from "utils/arrayHelpers"

import styles from "./bulkProcessingConfirmList.module.scss"

export const BulkProcessingConfirmList = ({ title, list = [], className }) => {
  return (
    <>
      {!checkIsArray(list) ? null : (
        <div className={cn(className, styles.listContainer)}>
          <Typography className={styles.listTitle} variant="--font-body-text-7" color="--color-text-main">
            {`${l(title)}:`}
          </Typography>

          <Typography className={styles.list} variant="--font-body-text-9" color="--color-text-second">
            {list.map((title, index) => (
              <span key={index}>
                {!!index && ", "}
                <Typography
                  className={styles.itemTitle}
                  key={index}
                  variant="--font-body-text-9"
                  color="--color-text-second"
                >
                  {title}
                </Typography>
              </span>
            ))}
          </Typography>
        </div>
      )}
    </>
  )
}

BulkProcessingConfirmList.propTypes = {
  title: PropTypes.string,
  className: PropTypes.string,
  list: PropTypes.array,
}

BulkProcessingConfirmList.defaultProps = {
  title: "",
  list: [],
  className: "",
}
