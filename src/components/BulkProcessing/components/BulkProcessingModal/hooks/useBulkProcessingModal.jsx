import { useState, useEffect } from "react"

import l from "utils/intl"
import { setConfirm } from "utils/confirm"
import activeTabSetInterval from "utils/activeTabSetInterval"
import { getBulkTitles } from "utils/bulkHelper"

import { BULK_CHANGES_KEY } from "constants/bulkEdit"
import { STATUS_TYPES } from "constants/statuses"
import {
  AUTO_PROGRESS_STEP,
  MAX_AUTO_PROGRESS,
  STEP_FOR_UPDATE,
  FINISH_ANIMATION_TIME,
  TIME_STEP,
} from "constants/bulkProcessing"

export const useBulkProcessingModal = ({
  id,
  formValues,
  fieldNames,
  customerId,

  onStop,
  onClose,
  getProcessById,
}) => {
  const [progress, setProgress] = useState(0)
  const [errors, setErrors] = useState([])
  const [errorsCount, setErrorsCount] = useState(0)
  const [cleared, setCleared] = useState([])
  const [edited, setEdited] = useState([])
  const [params, setParams] = useState({})
  const [isDone, setIsDone] = useState(false)

  useEffect(() => {
    const { edit, clear } = getBulkTitles(formValues[BULK_CHANGES_KEY])
    setEdited(edit)
    setCleared(clear)
  }, [formValues])

  useEffect(() => {
    let loadingStatus
    let progress = 0
    let realProgress = 0
    let counter = 0

    const interval = activeTabSetInterval.setInterval(async () => {
      counter++
      progress = realProgress
        ? Math.max(realProgress, progress)
        : Math.min(progress + AUTO_PROGRESS_STEP, MAX_AUTO_PROGRESS)

      if (counter >= STEP_FOR_UPDATE) {
        counter = 0
        const { data } = await getProcessById({ id, customerId })
        const {
          [fieldNames.successCount]: successCount,
          [fieldNames.totalCount]: totalCount,
          [fieldNames.errorCount]: errorCount,
          [fieldNames.errors]: errors,
          [fieldNames.status]: status,
          [fieldNames.params]: params,
        } = data

        const doneCases = successCount + errorCount

        loadingStatus = status

        if (totalCount && doneCases) {
          progress = doneCases / totalCount
          realProgress = progress
        }
        if (errors?.length) {
          setParams(params)
          setErrors(errors)
          setErrorsCount(errorCount)
        }
      }

      setProgress(progress)

      if (loadingStatus === STATUS_TYPES.done) {
        setTimeout(() => setIsDone(true), FINISH_ANIMATION_TIME)
        clearInterval(interval)
      }
    }, TIME_STEP)

    return () => {
      clearInterval(interval)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id])

  const onCancel = () => {
    if (!isDone) {
      setConfirm({
        title: l("Stop bulk edit"),
        message: l("Are you sure you want to stop this process?"),
        onOk: () => {
          onClose(isDone)
          onStop(id)
        },
      })
      return
    }
    onClose(isDone)
  }

  const hasErrors = !!errors?.length
  const cancelButtonText = !isDone ? l("Stop process") : l("Close")

  return {
    progress,
    errors,
    errorsCount,
    cleared,
    edited,
    params,
    isDone,
    onCancel,
    hasErrors,
    cancelButtonText,
  }
}
