@import 'assets/styles/variables.scss';


.subTitle  {
  font-size: 14px;
  font-weight: bold;
  color: var(--color-text-main);
}

.progressContainer {
  position: relative;
  background-color: var(--color-row-select);
  padding: 25px;
  border-radius: 4px;
  overflow: hidden;
}

.progress {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  background-color: var(--color-int-on-disable);
  z-index: 5;
  transition: 0.7s;
}

.bulkList {
  margin-top: 10px;
}

.resultStatus {
  text-align: center;
}

.content {
  position: relative;
  z-index: 10;
}

.statusIcon {
  margin-bottom: 15px;
}

.statusTitle.statusTitle {
  margin-bottom: 15px;
}

.statusDescription.statusDescription {
  margin-bottom: 15px;
}

.warningsBox {
  overflow: auto;
  max-height: 300px;
  padding: 10px;
  :global(.ant-alert-icon) {
    left: 10px;
    top: 10px;
    font-size: 21px;
  }
  :global(.ant-alert-close-icon) {
    top: 10px;
    right: 10px;
  }
}

.warningsTitle {
  font-size: 14px;
  color: var(--color-text-main);
  margin-bottom: 15px;
}
