import React from "react"
import PropTypes from "prop-types"
import { Alert, Typography, Icon, Box, Modal } from "@develop/fe-library"

import { BulkProcessingErrorItem, BulkProcessingConfirmList } from "../index"

import l from "utils/intl"

import { CONFIRM_LIST_TITLES } from "constants/bulkEdit"
import { FIELD_NAMES_DEFAULT } from "constants/bulkProcessing"

import { useBulkProcessingModal } from "./hooks"

import styles from "./bulkProcessingModal.module.scss"

const generateHandleClick =
  ({ onErrorItemClick, item, params }) =>
  () => {
    onErrorItemClick({ item, params })
  }

const BulkProcessingModal = ({
  id,
  formValues,
  fieldNames,
  customerId,

  title,
  backButtonText,
  statusDescription,
  notChangedText,

  onStop,
  onClose,
  onErrorItemClick,
  getProcessById,
}) => {
  const {
    progress,
    errors,
    errorsCount,
    cleared,
    edited,
    params,
    isDone,
    onCancel,
    hasErrors,
    cancelButtonText,
  } = useBulkProcessingModal({
    id,
    formValues,
    fieldNames,
    customerId,

    onStop,
    onClose,
    getProcessById,
  })

  return (
    <Modal
      visible
      width="--modal-size-m"
      title={l(title)}
      onOk={onClose}
      onCancel={onCancel}
      okButtonText={l(backButtonText)}
      cancelButtonText={cancelButtonText}
    >
      {!isDone ? (
        <div className={styles.progressContainer}>
          <Box className={styles.content} gap="m">
            <Icon name="icnSetting" color="--color-icon-active" />
            <div>
              <Typography
                variant="--font-body-text-2"
                color="--color-text-main"
              >
                {l("Processing")}
              </Typography>
              <BulkProcessingConfirmList
                className={styles.bulkList}
                title={CONFIRM_LIST_TITLES.changed}
                list={edited}
              />
              <BulkProcessingConfirmList
                className={styles.bulkList}
                title={CONFIRM_LIST_TITLES.cleared}
                list={cleared}
              />
            </div>
          </Box>
          <div
            className={styles.progress}
            style={{ width: `${progress * 100}%` }}
          ></div>
        </div>
      ) : (
        <div>
          <div className={styles.resultStatus}>
            <Icon
              name="icnCheck"
              className={styles.statusIcon}
              color="--color-icon-done"
            />
            <Typography
              className={styles.statusTitle}
              variant="--font-body-text-2"
              color="--color-text-main"
            >
              {l("Changing completed!")}
            </Typography>
            {!!statusDescription ? (
              <Typography
                className={styles.statusDescription}
                variant="--font-body-text-7"
                color="--color-text-second"
              >
                {statusDescription}
              </Typography>
            ) : null}
          </div>
          {hasErrors ? (
            <Alert
              message={
                <span>
                  {errorsCount} {l("warnings")}
                  <small>{` (${l(notChangedText)})`}</small>
                </span>
              }
              description={errors.map((item) => (
                <BulkProcessingErrorItem
                  key={item.id}
                  id={item.id}
                  errors={item.errors}
                  onClick={generateHandleClick({
                    onErrorItemClick,
                    item,
                    params,
                  })}
                />
              ))}
              alertType="error"
              isClosable
            />
          ) : null}
        </div>
      )}
    </Modal>
  )
}

BulkProcessingModal.propTypes = {
  id: PropTypes.number,
  formValues: PropTypes.object,
  fieldNames: PropTypes.object,
  customerId: PropTypes.number,

  title: PropTypes.string,
  backButtonText: PropTypes.string,
  statusDescription: PropTypes.string,
  notChangedText: PropTypes.string,

  onStop: PropTypes.func,
  onClose: PropTypes.func,
  onErrorItemClick: PropTypes.func,
}

BulkProcessingModal.defaultProps = {
  id: null,
  formValues: {},
  fieldNames: FIELD_NAMES_DEFAULT,

  title: "Bulk edit",
  backButtonText: "Back",
  statusDescription: undefined,
  notChangedText: "these items have not been changed",

  onStop: () => {},
  onClose: () => {},
  onErrorItemClick: () => {},
}

export { BulkProcessingModal }
