import React from "react"
import { Box, StatusTag, StatusTagProps } from "@develop/fe-library"

import l from "utils/intl"

import { ORDER_STATUSES, STATUS_TAGS_MAP } from "constants/orders"

import { OrderStatusTagProps } from "./OrderStatusTagTypes"

export const OrderStatusTag = (props: OrderStatusTagProps) => {
  const { status = "default" } = props
  const { iconName, colorStatus }: StatusTagProps =
    STATUS_TAGS_MAP[status] || STATUS_TAGS_MAP["default"]
  const tagName = l(ORDER_STATUSES[status]?.title) || ""

  return (
    <Box>
      <StatusTag colorStatus={colorStatus} iconName={iconName} name={tagName} />
    </Box>
  )
}
