import React from "react"
import PropTypes from "prop-types"

import { RecurrentSettings } from "components/RecurrentSettings"

import { importExportSettingKeys } from "constants/recurrentSettingsConstants"

import { useRecurrentExportSettings } from "./hooks"

export const RecurrentExportSettings = ({
  title,
  permissionCode,
  viewPermissionCode,
}) => {
  const {
    recurrentExportSettings,
    modalVisible,
    allExportTemplates,
    hasExportTemplates,
    activeItem,
    triggerModal,
    handleFormChange,
    handleSetActiveItem,
    handleDelete,
    handleStatusChange,
    handleSave,
    handleCancel,
  } = useRecurrentExportSettings()

  return (
    <RecurrentSettings
      activeItem={activeItem}
      hasExportTemplates={hasExportTemplates}
      modalVisible={modalVisible}
      permissionCode={permissionCode}
      recurrentSettings={recurrentExportSettings}
      templates={allExportTemplates}
      title={title}
      triggerModal={triggerModal}
      type={importExportSettingKeys.types.EXPORT}
      viewPermissionCode={viewPermissionCode}
      onCancel={handleCancel}
      onDelete={handleDelete}
      onFormChange={handleFormChange}
      onSave={handleSave}
      onSetActiveItem={handleSetActiveItem}
      onStatusChange={handleStatusChange}
    />
  )
}

RecurrentExportSettings.propTypes = {
  title: PropTypes.string.isRequired,
  permissionCode: PropTypes.string.isRequired,
  viewPermissionCode: PropTypes.string.isRequired,
}
