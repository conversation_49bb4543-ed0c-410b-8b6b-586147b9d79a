import { FILE_TYPES } from "constants/productExport"

import { ObjectType } from "types"
import { ProductExportTemplateType } from "types/ProductExport"

export type CreateExportModalProps = {
  getProducts: ({ searchOptions }: { searchOptions: ObjectType }) => void
  onClose: () => void
  searchOptions: ObjectType
  isVisible: boolean
}

export type FormFieldsTypes = {
  template_id?: string
}

export type ExportTemplateType = {
  format: keyof typeof FILE_TYPES
  id?: string
  title?: string
  handler_name?: ProductExportTemplateType
}

export type ExportTemplatesItemsTypes = ExportTemplateType[]
