import React from "react"
import { useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { useHistory } from "react-router-dom"
import { Box, FormItems, Modal } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"

import productExportActions from "actions/productExportActions"

import { allExportTemplatesSelector } from "selectors/exportTemplatesSelectors"

import { RestrictedPrimaryButton } from "components/shared/RestrictedButtons"

import l from "utils/intl"

import { TEMPLATE_HANDLER_NAMES } from "constants/exportTemplates"
import { permissionKeys, restrictPopoverMessages } from "constants/permissions"
import { FILE_TYPES } from "constants/productExport"

import {
  CreateExportModalProps,
  ExportTemplatesItemsTypes,
  ExportTemplateType,
  FormFieldsTypes,
} from "./CreateExportModalTypes"

// @ts-ignore
const { addProductExport } = productExportActions

export const CreateExportModal = ({
  getProducts = () => {},
  onClose = () => {},
  searchOptions = {},
  isVisible = false,
}: CreateExportModalProps) => {
  const history = useHistory()
  const dispatch = useDispatch()
  const allExportTemplates = useSelector(
    allExportTemplatesSelector,
  ) as ExportTemplatesItemsTypes

  const form = useForm({
    defaultValues: {
      template_id: allExportTemplates[0]?.id,
    },
  })

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = form

  const getSelectedTemplate = (id: string): ExportTemplateType | undefined => {
    const selectedTemplate = allExportTemplates.find(
      (template) => template.id === id,
    )

    return selectedTemplate
  }

  const formItems = [
    {
      type: "select",
      name: "template_id",
      inputProps: {
        options:
          allExportTemplates?.map(({ format, id, title }) => ({
            label: `${title} (${
              FILE_TYPES?.[format] ? FILE_TYPES?.[format]?.label : ""
            })`,
            value: id,
          })) || [],
        label: l("Export template"),
        isRequired: true,
      },
      gridItemProps: { always: 12 },
    },
  ]

  const handleSubmitForm = (data: FormFieldsTypes): void => {
    if (!data.template_id) {
      return
    }

    const { handler_name = TEMPLATE_HANDLER_NAMES.PRODUCT_COST_PERIODS } =
      getSelectedTemplate(data.template_id) || {}

    dispatch(
      addProductExport(
        {
          ...data,
          handler_name,
        },
        () => {
          onClose()
          getProducts({ searchOptions })
        },
        null,
      ),
    )
  }

  const handleCreateTemplate = () => {
    onClose()
    history.push({
      pathname: ROUTES.BAS_ROUTES.PATH_BAS_EXPORT_TEMPLATE,
      search: `openedDrawer=createTemplate`,
    })
  }

  return (
    <Modal
      isKeyboard={false}
      title={l("Create export")}
      visible={isVisible}
      footer={
        <Box
          gap="m"
          justify="flex-end"
          width="100%"
          tb={{
            width: "auto",
            marginLeft: "auto",
          }}
        >
          <RestrictedPrimaryButton
            fullWidth
            managePermission={permissionKeys.basProductExportTemplateView}
            popoverMessage={restrictPopoverMessages.alter}
            variant="secondary"
            onClick={handleCreateTemplate}
          >
            {l("Create template")}
          </RestrictedPrimaryButton>
          <RestrictedPrimaryButton
            fullWidth
            disabled={isSubmitting}
            managePermission={permissionKeys.productExportManage}
            popoverMessage={restrictPopoverMessages.alter}
            type="submit"
            onClick={handleSubmit(handleSubmitForm)}
          >
            {l("Save")}
          </RestrictedPrimaryButton>
        </Box>
      }
      onClose={onClose}
    >
      <FormItems
        boxContainerProps={{ display: "block" }}
        // @ts-ignore
        form={form}
        gridContainerProps={{ gap: "m" }}
        // @ts-ignore
        items={formItems}
      />
    </Modal>
  )
}
