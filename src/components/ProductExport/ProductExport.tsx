import React from "react"

import { TableWrapper } from "components/shared/TableWrapper"

import { TABLE_SETTINGS_KEY } from "constants/productExport"

import { CreateExportModal } from "./components"

import { useProductExport, useProductExportColumns } from "./hooks"

import { ProductImportProps } from "./ProductExportTypes"

export const ProductExport = ({ pageName = "" }: ProductImportProps) => {
  const {
    dataSource,
    selectFiltersOptions,
    searchOptions,
    totalCount,
    getDataHandler,
    getAdditionalDataHandler,
    handleCreateExportModalHide,
    isCreateModalVisible,
    tableGridIcons,
    tableGridButtons,
    isProductExportDataLoading,
  } = useProductExport()

  const { tableColumns } = useProductExportColumns()

  return (
    <>
      <TableWrapper
        // @ts-ignore
        isNeedSort
        actionsColumnWidth={110}
        componentTableSettings={TABLE_SETTINGS_KEY}
        dataSource={dataSource}
        getAdditionalData={getAdditionalDataHandler}
        getData={getDataHandler}
        pageTableSettings={TABLE_SETTINGS_KEY}
        searchOptions={searchOptions}
        selectFiltersOptions={selectFiltersOptions}
        tableButtons={tableGridButtons}
        tableColumns={tableColumns}
        tableGridTitle={pageName}
        tableIcons={tableGridIcons}
        tableLoading={isProductExportDataLoading}
        totalCount={totalCount}
      />

      {isCreateModalVisible ? (
        <CreateExportModal
          getProducts={getDataHandler}
          isVisible={isCreateModalVisible}
          searchOptions={searchOptions}
          onClose={handleCreateExportModalHide}
        />
      ) : null}
    </>
  )
}
