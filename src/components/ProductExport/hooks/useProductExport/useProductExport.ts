import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"

// @ts-ignore
import exportTemplatesActions from "actions/exportTemplatesActions"
// @ts-ignore
import productExportActions from "actions/productExportActions"

import { getGridSearchOptionsSelector } from "selectors/gridSelectors"
import { translationsSelector } from "selectors/mainStateSelectors"
import {
  filtersOptionsSelector,
  isProductExportDataLoadingSelector,
  productExportItemsSelector,
  productExportSelector,
  productExportsTotalCountSelector,
} from "selectors/productExportSelectors"

import { DELETE_ITEM } from "utils/confirm"
import { downloadFileFromUrl } from "utils/downloadFile"

import { permissionKeys, restrictPopoverMessages } from "constants/permissions"
import { STATUS_TYPES } from "constants/statuses"

import { ProductExportItemType } from "types"

import {
  UseProductExportHandlersType,
  UseProductExportReturn,
} from "./useProductExportTypes"

const {
  // @ts-ignore
  displayCreateExportModal,
  // @ts-ignore
  deleteItem,
  // @ts-ignore
  downloadTemplateFile,
  // @ts-ignore
  getProductExports,
} = productExportActions
// @ts-ignore
const { getAllExportTemplates } = exportTemplatesActions

export const useProductExport = (): UseProductExportReturn => {
  const dispatch = useDispatch()

  const [locale, setLocale] = useState(null)

  const selectFiltersOptions = filtersOptionsSelector()

  const { locale: language } = useSelector(translationsSelector)
  const isProductExportDataLoading = useSelector(
    isProductExportDataLoadingSelector,
  )
  const dataSource = useSelector(productExportItemsSelector)
  const searchOptions = useSelector(getGridSearchOptionsSelector)

  const { modalVisible: isCreateModalVisible } = useSelector(
    productExportSelector,
  )
  const totalCount = useSelector(productExportsTotalCountSelector)

  useEffect(() => {
    const isStateWithoutLocale = language.length && !locale
    const currentLanguage = language[0].toUpperCase() + language.slice(1)

    if (isStateWithoutLocale) {
      setLocale(currentLanguage)
    }
  }, [language, locale])

  const getDataHandler = <Type>(searchOptions: Type) => {
    dispatch(
      getProductExports(
        {
          searchOptions,
        },
        null,
      ),
    )
  }

  const getAdditionalDataHandler: UseProductExportHandlersType["getAdditionalDataHandler"] =
    () => {
      dispatch(getAllExportTemplates(null))
    }

  const handleItemDownload: UseProductExportHandlersType["handleItemDownload"] =
    ({ file_url }) => {
      downloadFileFromUrl(file_url)
    }

  const handleItemDelete: UseProductExportHandlersType["handleItemDelete"] = ({
    id,
  }: {
    id: number
  }) => {
    dispatch(deleteItem({ id }, () => getDataHandler({ searchOptions })))
  }

  const handleCreateExportModalShow: UseProductExportHandlersType["handleCreateExportModalShow"] =
    () => {
      dispatch(displayCreateExportModal(true))
    }

  const handleCreateExportModalHide: UseProductExportHandlersType["handleCreateExportModalHide"] =
    () => {
      dispatch(displayCreateExportModal(false))
    }

  const handleDownloadTemplateFile: UseProductExportHandlersType["handleDownloadTemplateFile"] =
    () => {
      dispatch(downloadTemplateFile({}))
    }

  const tableGridIcons = [
    {
      checkAvailability: ({ status }: ProductExportItemType) =>
        status === STATUS_TYPES.new,
      managePermission: permissionKeys.basProductCostExportCreate,
      popoverMessage: restrictPopoverMessages.alter,
      onClick: handleItemDelete,
      confirm: {
        template: DELETE_ITEM,
      },
      title: "Delete",
      type: "icnDeleteOutlined",
    },
    {
      checkAvailability: ({ file_url }: ProductExportItemType) => !!file_url,
      onClick: handleItemDownload,
      title: "Download",
      type: "icnDownload",
    },
  ]

  const tableGridButtons = [
    {
      icon: "icnPlus",
      onClick: handleCreateExportModalShow,
      title: "Create export",
      type: "primary",
      popoverPlacement: "topLeft",
    },
    {
      icon: "icnDownload",
      onClick: handleDownloadTemplateFile,
      title: "Download template",
      type: "primary",
    },
  ]

  return {
    dataSource,
    selectFiltersOptions,
    searchOptions,
    totalCount,
    getDataHandler,
    getAdditionalDataHandler,
    handleCreateExportModalHide,
    isCreateModalVisible,
    tableGridIcons,
    tableGridButtons,
    isProductExportDataLoading,
  }
}
