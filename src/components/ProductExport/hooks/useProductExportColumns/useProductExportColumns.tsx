import React from "react"
import {
  COLUMN_INPUT_TYPE_INPUT,
  COLUMN_INPUT_TYPE_SELECT,
  COLUMN_INPUT_TYPE_SELECT_WITH_FILTER,
  COLUMN_INPUT_TYPE_NUMBER,
} from "constants/grid"
import { ExportValue } from "components/TableGridLayout/components/ExportValue"

export const useProductExportColumns = () => {
  const tableColumns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 70,
    },
    {
      title: "Created on",
      dataIndex: "dateInsertedLabel",
      key: "created_at",
      sorter: true,
      width: 130,
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      type: COLUMN_INPUT_TYPE_SELECT,
      sorter: true,
      width: 130,
    },
    {
      title: "Export template",
      dataIndex: "exportTemplateLabel",
      key: "recurrent_data_export_id",
      sorter: false,
      type: COLUMN_INPUT_TYPE_SELECT_WITH_FILTER,
      width: 130,
    },
    {
      title: "Format",
      dataIndex: "output_format",
      key: "output_format",
      sorter: true,
      type: COLUMN_INPUT_TYPE_SELECT,
      render: (text: string) => <ExportValue>{text.toUpperCase()}</ExportValue>,
    },
    {
      title: "Status",
      dataIndex: "statusLabel",
      key: "status",
      sorter: true,
      type: COLUMN_INPUT_TYPE_SELECT,
    },
    {
      title: "Number of products",
      dataIndex: "count_exported_items",
      key: "count_exported_items",
      sorter: true,
      type: COLUMN_INPUT_TYPE_NUMBER,
      width: 170,
      isNumber: true,
    },
    {
      title: "Start date",
      dataIndex: "startDateLabel",
      key: "started_at",
      sorter: true,
      width: 130,
    },
    {
      title: "End date",
      dataIndex: "endDateLabel",
      key: "finished_at",
      sorter: true,
      width: 130,
    },
    {
      title: "Updated",
      dataIndex: "dateUpdateLabel",
      key: "updated_at",
      sorter: true,
      width: 130,
    },
  ]

  return {
    tableColumns,
  }
}
