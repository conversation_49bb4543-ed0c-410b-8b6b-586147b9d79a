import React from "react"

import { TableWrapper } from "components/shared/TableWrapper"

import { TABLE_SETTINGS_KEY } from "constants/productsSalesInfoTable"

import { URL_WHITE_LIST } from "./constants"

import { useProductsSalesInfoTable } from "./hooks/useProductsSalesInfoTable"
import { useProductsSalesInfoTableColumns } from "./hooks/useProductsSalesInfoTableColumns"

import styles from "./productsSalesInfoTable.module.scss"

export const ProductsSalesInfoTable = () => {
  const { columns } = useProductsSalesInfoTableColumns()
  const {
    productsSalesInfoTableData,
    productsSalesInfoTableTotalCount,
    selectFiltersOptions,
    globalUrlParams,
    getDataHandler,
    customExcludeKeys,
    productsSalesInfoTableSearchOptions,
    handleGridUnmount,
    isTableLoading,
  } = useProductsSalesInfoTable()

  return (
    <TableWrapper
      // @ts-ignore
      isGlobalUrlParams
      isNeedSort
      isStartFromColumnZeroIndex
      shouldWaitForLoading
      actionsColumn={false}
      componentTableSettings={TABLE_SETTINGS_KEY}
      contentClassName={styles.productsSalesInfoTable}
      customDefaultSort="-units"
      customExcludeKeys={customExcludeKeys}
      dataSource={productsSalesInfoTableData}
      filterPrefix="products"
      footerGridContainerStyles={{ height: "32px" }}
      getData={getDataHandler}
      globalUrlParams={globalUrlParams}
      handleGridUnmount={handleGridUnmount}
      isCopyrightVisibleWithNoData={false}
      isHeaderVisible={false}
      pageTableSettings={TABLE_SETTINGS_KEY}
      searchOptions={productsSalesInfoTableSearchOptions}
      selectFiltersOptions={selectFiltersOptions}
      tableClassName="gridView"
      tableColumns={columns}
      tableHeader={null}
      tableLoading={isTableLoading}
      totalCount={productsSalesInfoTableTotalCount}
      urlFiltersWhiteList={URL_WHITE_LIST}
      withExport={false}
      tableFooterStyles={{
        position: "static",
        padding: "var(--padding-m)",
        justifyContent: "space-between",
      }}
    />
  )
}
