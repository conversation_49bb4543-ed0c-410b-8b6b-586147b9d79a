import React, { useMemo } from "react"
import { useSelector } from "react-redux"
import { Box, EmptyImage, Typography } from "@develop/fe-library"

import { getSelectedTableSettingsSelector } from "selectors/tableSettingsSelectors"
import { isSLUserSelector } from "selectors/userSelectors"

import AmazonCustomerAccountColumn from "components/Grid/components/amazonCustomerAccountColumn/AmazonCustomerAccountColumn"
import { MarketplaceColumn } from "components/Grid/components/marketplaceColumn/MarketplaceColumn"
import { Blurred } from "components/shared/Blurred"
import { BlurredWithGenerator } from "components/shared/BlurredWithGenerator"
import LinkView from "components/shared/Link/LinkView"
import TextEllipsis from "components/shared/maxTextLines/TextEllipsis"

import {
  useGenerateLink,
  useGridProduct,
  useSubscription,
  useUrlParams,
} from "hooks"

import { buildCustomTableColumns } from "utils/buildCustomTableColumns"
import l from "utils/intl"
import { ln } from "utils/localeNumber"
import { checkIsNullOrUndefined } from "utils/validationHelper"

import {
  COLUMN_INPUT_TYPE_INPUT,
  COLUMN_INPUT_TYPE_SELECT,
} from "constants/grid"
import { ORDER_STATUSES } from "constants/orders"
import { TABLE_SETTINGS_KEY } from "constants/productsSalesInfoTable"

import { DashboardFiltersParams, ProductAggregatedSalesInfo } from "types"
import { ProductInfo } from "interfaces/GridInterfaces"

import { Colors } from "@develop/fe-library/dist/lib/types"

export const useProductsSalesInfoTableColumns = () => {
  const selectedTableSettingsColumns = useSelector((state) =>
    getSelectedTableSettingsSelector(state, TABLE_SETTINGS_KEY),
  )

  const { urlParams } = useUrlParams<DashboardFiltersParams>()
  const { getProductImage, getProductLink } = useGridProduct()
  const { getOrdersLink, getTransactionsLink } = useGenerateLink()
  const { isFreemiumActive } = useSubscription()
  const isSLUser = useSelector(isSLUserSelector)

  const columns = useMemo(() => {
    const NotAvailable: string = l("N/A")

    const shouldBlur: boolean = isFreemiumActive && !isSLUser

    const tableColumns = [
      {
        title: "Image",
        dataIndex: "image",
        key: "image",
        onCell: () => ({
          style: { padding: "var(--padding-s) 0" },
        }),
        width: 70,
        min: 80,
        render: (_, product: ProductAggregatedSalesInfo) => {
          const productImageUrl = getProductImage({
            seller_id: product?.seller_id,
            asin: product?.product_asin,
          } as ProductInfo)

          return (
            <Box justify="center">
              <Blurred shouldBlur={shouldBlur}>
                <EmptyImage height={53} url={productImageUrl} width={64} />
              </Blurred>
            </Box>
          )
        },
      },
      {
        title: "Title",
        dataIndex: "product_title",
        key: "product_title",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 300,
        min: 80,
        class: "left",
        onCell: () => ({
          className: "left title",
          style: { textAlign: "left" },
        }),
        render: (title: string) =>
          !title ? (
            NotAvailable
          ) : (
            <TextEllipsis
              ellipsisChars={"..."}
              fontSize={12}
              lines={3}
              tooltip={title}
            >
              {title}
            </TextEllipsis>
          ),
      },
      {
        title: "ASIN",
        dataIndex: "product_asin",
        key: "product_asin",
        min: 80,
        width: 100,
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        ellipsis: true,
        render: (value: string, product: ProductAggregatedSalesInfo) => {
          if (checkIsNullOrUndefined(value)) {
            return NotAvailable
          }

          if (isFreemiumActive) {
            return value
          }

          const productLink = getProductLink({
            marketplace_id: product?.marketplace_id,
            asin: value,
          } as ProductInfo)

          if (!productLink) {
            return value
          }

          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={value}
              type="span"
              url={productLink}
              variant="textSmall"
            />
          )
        },
      },
      {
        title: "SKU",
        dataIndex: "seller_sku",
        key: "seller_sku",
        sorter: true,
        width: 100,
        min: 80,
        type: COLUMN_INPUT_TYPE_INPUT,
        ellipsis: true,
        className: "flex-string",
        render: (value: string) => {
          return !value ? (
            NotAvailable
          ) : (
            <TextEllipsis
              ellipsisChars={"..."}
              fontSize={12}
              lines={1}
              tooltip={value}
            >
              {value}
            </TextEllipsis>
          )
        },
      },
      {
        title: "Marketplace",
        dataIndex: "marketplace_id",
        key: "marketplace_id",
        sorter: true,
        width: 120,
        min: 120,
        type: COLUMN_INPUT_TYPE_SELECT,
        ellipsis: true,
        render: (marketplaceId: string) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Marketplace")}</BlurredWithGenerator>
            )
          }

          return marketplaceId ? (
            <MarketplaceColumn marketplaceId={marketplaceId} />
          ) : (
            NotAvailable
          )
        },
      },
      {
        title: "Brand",
        dataIndex: "product_brand",
        key: "product_brand",
        sorter: true,
        ellipsis: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 120,
        min: 120,
        render: (value: string) => {
          if (shouldBlur) {
            return <BlurredWithGenerator>{l("Brand")}</BlurredWithGenerator>
          }

          return !value ? (
            NotAvailable
          ) : (
            <TextEllipsis
              ellipsisChars={"..."}
              fontSize={12}
              lines={1}
              tooltip={value}
            >
              {value}
            </TextEllipsis>
          )
        },
      },
      {
        title: "Product Type",
        dataIndex: "product_type",
        key: "product_type",
        sorter: true,
        ellipsis: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 120,
        min: 120,
        render: (value: string) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Product Type")}</BlurredWithGenerator>
            )
          }

          return !value ? (
            NotAvailable
          ) : (
            <TextEllipsis
              ellipsisChars={"..."}
              fontSize={12}
              lines={1}
              tooltip={value}
            >
              {value}
            </TextEllipsis>
          )
        },
      },
      {
        title: "Manufacturer",
        dataIndex: "product_manufacturer",
        key: "product_manufacturer",
        sorter: true,
        ellipsis: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 120,
        min: 120,
        render: (value: string) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Manufacturer")}</BlurredWithGenerator>
            )
          }

          return !value ? (
            NotAvailable
          ) : (
            <TextEllipsis
              ellipsisChars={"..."}
              fontSize={12}
              lines={1}
              tooltip={value}
            >
              {value}
            </TextEllipsis>
          )
        },
      },
      {
        title: "Units",
        dataIndex: "units",
        key: "units",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 90,
        min: 90,
        render: (value: string, product: ProductAggregatedSalesInfo) => {
          if (shouldBlur) {
            return <BlurredWithGenerator>{l("Units")}</BlurredWithGenerator>
          }

          if (checkIsNullOrUndefined(value)) {
            return NotAvailable
          }

          const formattedValue = String(value)

          const link = getOrdersLink({
            product,
            urlParams,
            ordersUrlParams: {
              order_status: [
                ORDER_STATUSES.Pending.value,
                ORDER_STATUSES.Unshipped.value,
                ORDER_STATUSES.PartiallyShipped.value,
                ORDER_STATUSES.Shipped.value,
                ORDER_STATUSES.Unfulfillable.value,
                ORDER_STATUSES.InvoiceUnconfirmed.value,
                ORDER_STATUSES.PendingAvailability.value,
              ],
            },
          })

          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={formattedValue}
              type="span"
              url={link}
              variant="textSmall"
            />
          )
        },
      },
      {
        title: "Order items",
        dataIndex: "orders",
        key: "orders",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 90,
        min: 90,
        render: (value: string, product: ProductAggregatedSalesInfo) => {
          if (checkIsNullOrUndefined(value)) {
            return NotAvailable
          }

          if (isFreemiumActive) {
            return value
          }

          const link = getOrdersLink({
            product,
            urlParams,
            ordersUrlParams: {
              order_status: [
                "Pending",
                "Unshipped",
                "PartiallyShipped",
                "Shipped",
                "Unfulfillable",
                "InvoiceUnconfirmed",
                "PendingAvailability",
              ],
            },
          })

          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={value}
              type="span"
              url={link}
              variant="textSmall"
            />
          )
        },
      },
      {
        title: "Refunds",
        key: "refunds",
        dataIndex: "refunds",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 90,
        min: 90,
        render: (value: string, product: ProductAggregatedSalesInfo) => {
          if (shouldBlur) {
            return <BlurredWithGenerator>{l("Refunds")}</BlurredWithGenerator>
          }

          if (checkIsNullOrUndefined(value)) {
            return NotAvailable
          }

          const link = getOrdersLink({
            product,
            urlParams,
            ordersUrlParams: {
              quantity_refunded: ">0",
            },
          })

          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={value}
              type="span"
              url={link}
              variant="textSmall"
            />
          )
        },
      },
      {
        title: "Revenue",
        dataIndex: "revenue_amount",
        key: "revenue_amount",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 90,
        min: 90,
        render: (value: string, product: ProductAggregatedSalesInfo) => {
          if (checkIsNullOrUndefined(value)) {
            return NotAvailable
          }

          const formattedValue = ln(value, 2, {
            currency: urlParams.currency_code,
          })

          if (isFreemiumActive) {
            return formattedValue
          }

          // Remove link for BAS-2418, will be added back in future
          return formattedValue

          const link = getTransactionsLink({
            product,
            urlParams,
            transactionsUrlParams: {
              sales_category_depth_1: ["revenue"],
              amount: ">0",
            },
          })

          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={formattedValue}
              type="span"
              url={link}
              variant="textSmall"
            />
          )
        },
      },
      {
        title: "Estimated margin",
        dataIndex: "estimated_profit_amount",
        key: "estimated_profit_amount",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 120,
        min: 120,
        render: (value: number) => {
          if (checkIsNullOrUndefined(value)) {
            return NotAvailable
          }

          const color: Colors =
            value < 0 ? "--color-text-error" : "--color-text-main"

          const formattedValue: string = ln(value, 2, {
            currency: urlParams.currency_code,
          })

          return (
            <Typography color={color} variant="--font-body-text-9">
              {formattedValue}
            </Typography>
          )
        },
      },
      {
        title: "Expenses",
        dataIndex: "expenses_amount_without_fees",
        key: "expenses_amount_without_fees",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 100,
        min: 100,
        render: (value: string, product: ProductAggregatedSalesInfo) => {
          if (shouldBlur) {
            return <BlurredWithGenerator>{l("Expenses")}</BlurredWithGenerator>
          }

          if (checkIsNullOrUndefined(value)) {
            return NotAvailable
          }

          const formattedValue: string = ln(value, 2, {
            currency: urlParams.currency_code,
          })

          // Remove link for BAS-2418, will be added back in future
          return formattedValue

          const link = getTransactionsLink({
            product,
            urlParams,
            transactionsUrlParams: {
              sales_category_depth_1: ["expenses"],
              amount: "<0",
            },
          })

          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={formattedValue}
              type="span"
              url={link}
              variant="textSmall"
            />
          )
        },
      },
      {
        title: "Amazon fees",
        key: "amazon_fees",
        dataIndex: "amazon_fees",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 110,
        min: 110,
        render: (value: string, product: ProductAggregatedSalesInfo) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Amazon fees")}</BlurredWithGenerator>
            )
          }

          if (checkIsNullOrUndefined(value)) {
            return NotAvailable
          }

          const formattedValue: string = ln(value, 2, {
            currency: urlParams.currency_code,
          })

          // Remove link for BAS-2418, will be added back in future
          return formattedValue

          const link = getTransactionsLink({
            product,
            urlParams,
            transactionsUrlParams: {
              sales_category_depth_1: ["expenses"],
              sales_category_depth_2: ["amazon_fees"],
            },
          })

          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={formattedValue}
              type="span"
              url={link}
              variant="textSmall"
            />
          )
        },
      },
      {
        title: "Margin",
        key: "margin",
        dataIndex: "margin",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 90,
        min: 90,
        render: (value: string) => {
          if (shouldBlur) {
            return <BlurredWithGenerator>{l("Margin")}</BlurredWithGenerator>
          }

          return !checkIsNullOrUndefined(value) ? `${value} %` : NotAvailable
        },
      },
      {
        title: "ROI",
        key: "roi",
        dataIndex: "roi",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 90,
        min: 90,
        render: (value: string) => {
          if (shouldBlur) {
            return <BlurredWithGenerator>{l("ROI")}</BlurredWithGenerator>
          }

          return !checkIsNullOrUndefined(value) ? `${value} %` : NotAvailable
        },
      },
      {
        title: "Markup",
        key: "markup",
        dataIndex: "markup",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 90,
        min: 90,
        render: (value: string) => {
          if (shouldBlur) {
            return <BlurredWithGenerator>{l("Markup")}</BlurredWithGenerator>
          }

          return !checkIsNullOrUndefined(value) ? `${value} %` : NotAvailable
        },
      },
      {
        title: "Amazon account name",
        dataIndex: "seller_id",
        key: "seller_id",
        width: 120,
        min: 120,
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT,
        ellipsis: true,
        className: "flex-string",
        render: (value: string, product: ProductAggregatedSalesInfo) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>
                {l("Amazon account name")}
              </BlurredWithGenerator>
            )
          }

          return !value ? (
            NotAvailable
          ) : (
            <AmazonCustomerAccountColumn
              object={product as unknown as ProductInfo}
            />
          )
        },
      },
    ]

    const customTableColumns = buildCustomTableColumns({
      tableColumns,
      columns: selectedTableSettingsColumns,
    })

    return customTableColumns
  }, [
    isFreemiumActive,
    selectedTableSettingsColumns,
    getProductImage,
    getProductLink,
    getOrdersLink,
    urlParams,
    getTransactionsLink,
  ])

  return {
    columns,
  }
}
