import { useCallback, useMemo } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Option } from "@develop/fe-library"
import { getObjectKeys } from "@develop/fe-library/dist/utils"

import { productsSalesInfoTableActions } from "actions/productsSalesInfoTableActions"

import {
  productsSalesInfoTableDataSelector,
  productsSalesInfoTableDataStatusSelector,
  productsSalesInfoTableTotalCountSelector,
} from "selectors/productsSalesInfoTableSelectors"
import { productsSalesInfoTableSearchOptionsSelector } from "selectors/productsSalesInfoTableSelectors/productsSalesInfoTableSelectors"

import {
  useGroupAccountOptions,
  useMarketplaceOptions,
  useSellerMarketplaceParams,
  useTableColumnsVisibility,
  useUrlParams,
} from "hooks"

import { removeNullAndUndefined } from "utils/objectHelpers"

import { ASYNC_STATUSES } from "constants/async"
import { SALES_CATEGORY_STRATEGIES } from "constants/general"
import { GROUP_ACCOUNT_TYPE } from "constants/groupAccount"
import { PAGE_VIEW } from "constants/pageView"
import { TABLE_SETTINGS_KEY } from "constants/productsSalesInfoTable"

import {
  DashboardFiltersParams,
  ProductAggregatedSalesInfoRequestParams,
  TableUrlParams,
} from "types"
import { ProductsSalesInfoTableColumnsUrlParams } from "types/Tables"

const { getProductsAggregatedSalesInfo, clearProductsSalesInfoTableData } =
  productsSalesInfoTableActions

export const useProductsSalesInfoTable = () => {
  const dispatch = useDispatch()

  const { urlParams } = useUrlParams<DashboardFiltersParams>()
  const { getSellerMarketplaceParams } = useSellerMarketplaceParams()

  const productsSalesInfoTableSearchOptions = useSelector(
    productsSalesInfoTableSearchOptionsSelector,
  )
  const productsSalesInfoTableData = useSelector(
    productsSalesInfoTableDataSelector,
  )
  const productsSalesInfoTableDataStatus = useSelector(
    productsSalesInfoTableDataStatusSelector,
  )
  const productsSalesInfoTableTotalCount = useSelector(
    productsSalesInfoTableTotalCountSelector,
  )
  const { getMarketplaceOptions } = useMarketplaceOptions()
  const { isOneBASAccount, filteredBasAccountsOptions } =
    useGroupAccountOptions()

  const seller_id = urlParams.seller_id || GROUP_ACCOUNT_TYPE.GLOBAL
  const selectFiltersOptions = useMemo(() => {
    const sellerIdOptions = filteredBasAccountsOptions({
      urlParams,
    })

    const marketplaceOptions: Option[] = getMarketplaceOptions(
      seller_id,
    ).filter((option) => {
      if (!urlParams.marketplace_id) {
        return option
      }

      return urlParams.marketplace_id.includes(option.value)
    })

    return {
      marketplace_id: marketplaceOptions,
      seller_id: sellerIdOptions,
    }
  }, [filteredBasAccountsOptions, getMarketplaceOptions, seller_id, urlParams])

  // DESC: This causes re-render of the TableWrapper w/o useMemo
  const globalUrlParams = useMemo(() => {
    return {
      seller_id: urlParams.seller_id,
      marketplace_id: urlParams.marketplace_id,
      adult_product: urlParams.adult_product,
      age_range: urlParams.age_range,
      asin: urlParams.asin,
      brand: urlParams.brand,
      currency_code: urlParams.currency_code,
      customerID: urlParams.customerID,
      ean: urlParams.ean,
      from: urlParams.from,
      inputMode: urlParams.inputMode,
      isbn: urlParams.isbn,
      isFromRepricer: urlParams.isFromRepricer,
      manufacturer: urlParams.manufacturer,
      offer_type: urlParams.offer_type,
      parent_asin: urlParams.parent_asin,
      period: urlParams.period,
      product_type: urlParams.product_type,
      productASIN: urlParams.productASIN,
      productId: urlParams.productId,
      productMarketplace: urlParams.productMarketplace,
      productRepricerId: urlParams.productRepricerId,
      productSeller: urlParams.productSeller,
      productSku: urlParams.productSku,
      segmentId: urlParams.segmentId,
      sku: urlParams.sku,
      stock_type: urlParams.stock_type,
      tags: urlParams.tags,
      to: urlParams.to,
      upc: urlParams.upc,
      view: urlParams.view,
    } as DashboardFiltersParams
  }, [urlParams])

  const getDataHandler = useCallback(
    (params: TableUrlParams<ProductsSalesInfoTableColumnsUrlParams>) => {
      if (productsSalesInfoTableDataStatus === ASYNC_STATUSES.PENDING) {
        return
      }

      dispatch(productsSalesInfoTableActions.changeSearchOptions(params))

      const sellerId = params.seller_id || urlParams.seller_id
      const marketplaceId = params.marketplace_id || urlParams.marketplace_id

      const { seller_id, marketplace_id, marketplaceSellerIds } =
        getSellerMarketplaceParams({
          ...urlParams,
          seller_id: sellerId,
          marketplace_id: marketplaceId,
        })

      // DESC: Merge of tableParams and urlParams
      const requestParams: ProductAggregatedSalesInfoRequestParams = {
        amazon_fees: params.amazon_fees,
        currency_id: urlParams.currency_code,
        date_end: urlParams.to,
        date_start: urlParams.from,
        estimated_profit_amount: params.estimated_profit_amount,
        expenses_amount_without_fees: params.expenses_amount_without_fees,
        is_transaction_date_mode:
          urlParams.view === PAGE_VIEW.TRANSACTION ? "1" : "0",
        margin: params.margin,
        marketplace_id,
        marketplace_seller_ids: marketplaceSellerIds,
        markup: params.markup,
        orders: params.orders,
        product_adult: urlParams.adult_product ? "1" : undefined,
        product_asin:
          urlParams.productASIN || urlParams.asin || params.product_asin,
        product_brand: urlParams.brand || params.product_brand,
        product_manufacturer:
          urlParams.manufacturer || params.product_manufacturer,
        product_stock_type: urlParams.stock_type,
        product_title: params.product_title,
        product_type: urlParams.product_type || params.product_type,
        refunds: params.refunds,
        roi: params.roi,
        revenue_amount: params.revenue_amount,
        seller_id,
        seller_sku: urlParams.productSku || urlParams.sku || params.seller_sku,
        units: params.units,
        offer_type: urlParams.offer_type,
        product_ean: urlParams.ean,
        product_isbn: urlParams.isbn,
        product_parent_asin: urlParams.parent_asin,
        product_upc: urlParams.upc,
        tag_id: urlParams.tags,
        page: params.page,
        pageSize: params.pageSize,
        sort: params.sort,
        sales_category_strategy: SALES_CATEGORY_STRATEGIES.custom,
      }

      dispatch(
        getProductsAggregatedSalesInfo({
          params: removeNullAndUndefined(requestParams),
          successCallback: () => {},
          failureCallback: () => {},
        }),
      )
    },
    [
      dispatch,
      getSellerMarketplaceParams,
      productsSalesInfoTableDataStatus,
      urlParams,
    ],
  )

  const handleGridUnmount = useCallback(() => {
    dispatch(clearProductsSalesInfoTableData())
  }, [dispatch])

  useTableColumnsVisibility({
    visibleColumns: ["seller_id"],
    tableSettingsKey: TABLE_SETTINGS_KEY,
    canUpdateSettings: !isOneBASAccount,
  })

  return {
    productsSalesInfoTableData,
    productsSalesInfoTableTotalCount,
    selectFiltersOptions,
    globalUrlParams,
    getDataHandler,
    urlParams,
    customExcludeKeys: getObjectKeys(globalUrlParams),
    handleGridUnmount,
    productsSalesInfoTableSearchOptions,
    isTableLoading: productsSalesInfoTableDataStatus === ASYNC_STATUSES.PENDING,
  }
}
