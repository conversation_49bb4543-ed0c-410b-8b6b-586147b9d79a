import { OrderStatus } from "types"

export type ProductType = {
  title: string
  asin: string
  sku: string
  unit_price: number | null
  quantity: number
  currency_id: string
  stock_type: string
  status: OrderStatus
}

export interface ProductInfoProps {
  product: ProductType
  currencyId: string
  marketplaceId: string
  imageUrl?: string
  fallbackImageUrl?: string
  imageSize?: number
  onEdit?: () => void
}
