import React from "react"
// @ts-ignore
import TextEllipsis from "react-text-ellipsis/src"
import {
  Box,
  Ellipsis,
  EmptyImage,
  Grid,
  Typography,
} from "@develop/fe-library"

import { AsinLink } from "components/AsinLink"
import { OrderStatusTag } from "components/OrderStatusTag"

import l from "utils/intl"
import ln from "utils/localeNumber"

import { ProductInfoProps } from "./ProductInfoTypes"

import styles from "./productInfo.module.scss"

export const ProductInfo = ({
  product,
  imageUrl,
  marketplaceId,
  imageSize = 80,
  currencyId,
  onEdit,
}: ProductInfoProps) => {
  const { title, asin, sku, unit_price, quantity, stock_type, status } = product

  const unitPriceValue: string | null =
    unit_price === null ? null : ln(unit_price, 2, { currency: currencyId })

  const columns = [
    {
      key: 1,
      label: "Title",
      value: title,
    },
    {
      key: 2,
      label: "ASIN",
      value: (
        <AsinLink
          asin={asin}
          exportValueProps={{ textAlign: "left" }}
          marketplaceId={marketplaceId}
        />
      ),
      hasCustomRender: true,
    },
    {
      key: 3,
      label: "SKU",
      value: onEdit ? (
        <Box component="span" cursor="pointer" onClick={onEdit}>
          <Ellipsis
            typographyProps={{
              variant: "--font-body-text-9",
              color: "--color-text-link",
            }}
          >
            {sku}
          </Ellipsis>
        </Box>
      ) : (
        sku
      ),
      hasCustomRender: !onEdit,
    },
    {
      key: 4,
      label: "Unit price",
      value: unitPriceValue,
    },
    {
      key: 5,
      label: "Units",
      value: quantity,
    },
    {
      key: 6,
      label: "Fulfillment method",
      value: stock_type,
    },
    {
      key: 7,
      label: "Order status",
      value: <OrderStatusTag status={status} />,
      hasCustomRender: true,
    },
  ]

  return (
    <div className={styles.productInfoContainer}>
      <Grid container gapMSM="m">
        <Grid item mLG="100px" mSM={12}>
          {!imageUrl ? null : (
            <EmptyImage height={imageSize} url={imageUrl} width={imageSize} />
          )}
        </Grid>
        <Grid item mLG mSM={12}>
          <Grid
            container
            flexDirection="column"
            flexWrap="nowrap"
            gapMSM="m"
            justify="flex-end"
          >
            {columns.map(({ key, label, value, hasCustomRender }) => {
              const productItemValue = value === null ? l("N/A") : value

              return (
                <Grid key={key} container align="baseline" gapTb="m">
                  <Grid item mSM={12} tb="150px">
                    <Typography variant="--font-body-text-9">
                      {l(label)}
                    </Typography>
                  </Grid>
                  <Grid item tb mSM={12}>
                    {hasCustomRender ? (
                      productItemValue
                    ) : (
                      <Typography variant="--font-body-text-9">
                        <TextEllipsis
                          ellipsisChars="..."
                          lines={2}
                          tooltip={value}
                        >
                          {productItemValue}
                        </TextEllipsis>
                      </Typography>
                    )}
                  </Grid>
                </Grid>
              )
            })}
          </Grid>
        </Grid>
      </Grid>
    </div>
  )
}
