import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"

import { operationalDashboardActions } from "actions/operationalDashboardActions"

import {
  operationalDashboardDataCompletenessSelector,
  operationalDashboardIsDataCompletenessLoadingSelector,
} from "selectors/operationalDashboardSelectors/operationalDashboardSelectors"

import { ActionColumnHandlerParams } from "./useCalculationAccuracyTableTypes"

const { getDataCompletenessFactors, setAdditionalDataModal } =
  operationalDashboardActions

export const useCalculationAccuracyTable = () => {
  const dataCompleteness = useSelector(
    operationalDashboardDataCompletenessSelector,
  )
  const isDataCompletenessLoading = useSelector(
    operationalDashboardIsDataCompletenessLoadingSelector,
  )

  const dispatch = useDispatch()

  const actionColumnHandler =
    ({
      action = () => {},
      additionalDataModal = {},
    }: ActionColumnHandlerParams) =>
    () => {
      dispatch(action)
      dispatch(setAdditionalDataModal(additionalDataModal))
    }

  useEffect(() => {
    dispatch(getDataCompletenessFactors())
  }, [])

  return {
    dataCompleteness,
    isDataCompletenessLoading,
    actionColumnHandler,
  }
}
