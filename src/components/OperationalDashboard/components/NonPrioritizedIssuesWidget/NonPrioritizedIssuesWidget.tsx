import React from "react"
import { useSelector } from "react-redux"
import { <PERSON>, Grid, Spinner, Typography } from "@develop/fe-library"
import { checkIsArray } from "@develop/fe-library/dist/utils"
import { <PERSON>, <PERSON>, PieChart, ResponsiveContainer } from "recharts"

import {
  operationalDashboardDataNonPrioritizedIssuesSelector,
  operationalDashboardIsDataCompletenessLoadingSelector,
} from "selectors/operationalDashboardSelectors/operationalDashboardSelectors"

import { GenericWidget } from "components/shared/Widget"

import l from "utils/intl"

import styles from "./nonPrioritizedIssuesWidget.module.scss"

export const NonPrioritizedIssuesWidget = () => {
  const nonPrioritizedIssues = useSelector(
    operationalDashboardDataNonPrioritizedIssuesSelector,
  )
  const isDataCompletenessLoading = useSelector(
    operationalDashboardIsDataCompletenessLoadingSelector,
  )

  return (
    <GenericWidget
      isCustomWrapper
      containerProps={{ height: "100%", minHeight: 326 }}
      content={
        <Box align="center" gap="s">
          <Typography variant="--font-body-text-2">
            {l("Non-prioritized issues")}
          </Typography>
        </Box>
      }
    >
      {isDataCompletenessLoading ? (
        <Box align="center" height="100%" justify="center">
          <Spinner tip={l("Loading...")} />
        </Box>
      ) : (
        <Box display="block" height="100%" padding="l">
          {checkIsArray(nonPrioritizedIssues) ? (
            <Grid
              container
              align="center"
              className={styles.gridContainer}
              gap="l"
              justify="center"
            >
              <Grid item dLG={6} dSM={12} mSM={12} tb={6}>
                <Box
                  height={180}
                  margin="0 auto"
                  position="relative"
                  width={180}
                >
                  <ResponsiveContainer height="100%" width="100%">
                    <PieChart>
                      <Pie
                        cx="50%"
                        cy="50%"
                        data={nonPrioritizedIssues}
                        dataKey="value"
                        endAngle={-270}
                        innerRadius={54}
                        outerRadius={90}
                        paddingAngle={0}
                        startAngle={90}
                        stroke="none"
                      >
                        {nonPrioritizedIssues.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </Grid>
              <Grid item dLG={6} dSM={12} mSM={12} tb={6}>
                <Box
                  className="legend-container"
                  flexDirection="column"
                  gap="l"
                  width="100%"
                >
                  {nonPrioritizedIssues.map((item) => (
                    <Box
                      key={item.name}
                      align="center"
                      gap="m"
                      justify="space-between"
                    >
                      <Box align="center" gap="m">
                        <Box
                          backgroundColor={item.fill}
                          borderRadius="--border-radius-circle"
                          component="span"
                          height={10}
                          width={10}
                        />
                        <Typography variant="--font-body-text-7">
                          {l(item.name)}
                        </Typography>
                      </Box>
                      <Typography variant="--font-body-text-7">
                        {item.value}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </Grid>
            </Grid>
          ) : (
            <Box align="center" height="100%" justify="center" width="100%">
              <Typography
                color="--color-text-second"
                variant="--font-headline-2"
              >
                {l("No data")}
              </Typography>
            </Box>
          )}
        </Box>
      )}
    </GenericWidget>
  )
}
