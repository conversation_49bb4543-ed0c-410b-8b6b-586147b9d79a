import { useCallback } from "react"
import { useDispatch, useSelector } from "react-redux"

import { operationalDashboardActions } from "actions/operationalDashboardActions"

import {
  operationalDashboardDataReferralFeeChangesSelector,
  operationalDashboardIsDataReferralFeeChangesLoadingSelector,
  operationalDashboardTotalCountSelector,
} from "selectors/operationalDashboardSelectors/operationalDashboardSelectors"

const { openReferralFeeChangesDrawer, getDataCompletenessReferralFeeChanges } =
  operationalDashboardActions

export const useReferralFeeChangesDrawer = () => {
  const dataReferralFeeChanges = useSelector(
    operationalDashboardDataReferralFeeChangesSelector,
  )
  const isDataReferralFeeChangesLoading = useSelector(
    operationalDashboardIsDataReferralFeeChangesLoadingSelector,
  )
  const totalCount = useSelector(operationalDashboardTotalCountSelector)

  const dispatch = useDispatch()

  const closeDrawerHandler = useCallback(() => {
    dispatch(openReferralFeeChangesDrawer(false))
  }, [])

  const getDataCompletenessReferralFeeChangesHandler = useCallback(
    (currentPage: number) => {
      dispatch(getDataCompletenessReferralFeeChanges({ currentPage }))
    },
    [],
  )

  return {
    dataReferralFeeChanges,
    isDataReferralFeeChangesLoading,
    totalCount,
    closeDrawerHandler,
    getDataCompletenessReferralFeeChangesHandler,
  }
}
