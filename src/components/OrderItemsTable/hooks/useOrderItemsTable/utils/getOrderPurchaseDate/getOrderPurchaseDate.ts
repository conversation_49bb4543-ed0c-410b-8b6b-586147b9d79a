import moment from "moment"

import { convertUserTzMomentToUtcMoment } from "utils/dateConverter"

import { SERVER_DATE_FORMAT_WITH_TIME } from "constants/dateTime"

import { GetOrderPurchaseDateParams } from "./getOrderPurchaseDateTypes"

/**
 * Gets formatted date range string with time in UTC (server date format) for request to api
 *
 * @param {String|null} rangeDateString
 * @param {String|null} dateFrom
 * @param {String|null} dateTo
 * @param {Boolean} convertToUTC
 *
 * @returns string | null
 */
export const getOrderPurchaseDate = ({
  rangeDateString = null,
  dateFrom,
  dateTo,
  convertToUTC = false,
}: GetOrderPurchaseDateParams): string | null => {
  const isDatesDefined = dateFrom && dateTo

  if (!isDatesDefined) {
    return null
  }

  const [parsedDateFrom, parsedDateTo] = rangeDateString?.split(" - ") || []

  const from = moment(parsedDateFrom || dateFrom)
  const to = moment(parsedDateTo || dateTo).add(1, "day")

  const convertedDateFrom = convertToUTC
    ? convertUserTzMomentToUtcMoment(from)
    : from
  const convertedDateTo = convertToUTC ? convertUserTzMomentToUtcMoment(to) : to

  return `${convertedDateFrom.format(
    SERVER_DATE_FORMAT_WITH_TIME,
  )} - ${convertedDateTo.format(SERVER_DATE_FORMAT_WITH_TIME)}`
}
