import { useCallback, useMemo } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Option } from "@develop/fe-library"
import { getObjectKeys } from "@develop/fe-library/dist/utils"
import { format } from "date-fns"

import { orderItemsTableActions } from "actions/orderItemsTableActions"

import { productConditionsSelector } from "selectors/gridSelectors"
import {
  orderItemsTableDataSelector,
  orderItemsTableDataStatusSelector,
  orderItemsTableOrderStatusesSelector,
  orderItemsTableSearchOptionsSelector,
  orderItemsTableTotalCountSelector,
} from "selectors/orderItemsTableSelectors"

import { useGroupAccountOptions } from "hooks/useGroupAccountOptions"
import { useMarketplaceOptions } from "hooks/useMarketplaceOptions"
import { useSellerMarketplaceParams } from "hooks/useSellerMarketplaceParams"
import { useTableColumnsVisibility } from "hooks/useTableColumnsVisibility"
import { useUrlParams } from "hooks/useUrlParams"

import { getStringRangeInDateRange } from "utils/dateConverter"
import l from "utils/intl"
import { removeNullAndUndefined } from "utils/objectHelpers"

import { ASYNC_STATUSES } from "constants/async"
import { DATE_FNS_FORMATS } from "constants/dateTime"
import { GROUP_ACCOUNT_TYPE } from "constants/groupAccount"
import { OFFER_TYPES } from "constants/offerType"
import { TABLE_SETTINGS_KEY } from "constants/orderItemsTable"
import { ORDER_STATUSES } from "constants/orders"
import { STOCK_TYPES } from "constants/stockType"

import { getOrderPurchaseDate } from "./utils"

import {
  AmazonOrderRequestType,
  DashboardFiltersParams,
  OrderStatuses,
  TableUrlParams,
} from "types"
import { OrderItemsTableColumnsUrlParams } from "types/Tables/OrderItemsTable"

const { getAmazonOrders, clearAmazonOrdersData, getAmazonOrderStatuses } =
  orderItemsTableActions

export const useOrderItemsTable = () => {
  const dispatch = useDispatch()

  const { urlParams } = useUrlParams<DashboardFiltersParams>()
  const { getSellerMarketplaceParams } = useSellerMarketplaceParams()

  const orderItemsTableSearchOptions = useSelector(
    orderItemsTableSearchOptionsSelector,
  )
  const orderItemsTableData = useSelector(orderItemsTableDataSelector)
  const orderItemsTableDataStatus = useSelector(
    orderItemsTableDataStatusSelector,
  )
  const orderItemsTableTotalCount = useSelector(
    orderItemsTableTotalCountSelector,
  )
  const orderStatuses = useSelector(orderItemsTableOrderStatusesSelector)
  const productConditions = useSelector(productConditionsSelector)

  const { getMarketplaceOptions } = useMarketplaceOptions()
  const { isOneBASAccount, filteredBasAccountsOptions } =
    useGroupAccountOptions()

  const seller_id: string = urlParams.seller_id || GROUP_ACCOUNT_TYPE.GLOBAL

  const selectFiltersOptions = useMemo(() => {
    const sellerIdOptions = filteredBasAccountsOptions({
      urlParams,
    })

    const marketplace_id: Option[] = getMarketplaceOptions(seller_id).filter(
      (option) => {
        if (!urlParams.marketplace_id) {
          return true
        }

        return urlParams.marketplace_id.includes(option.value)
      },
    )

    const product_stock_type = STOCK_TYPES.reduce<Option[]>(
      (memo, stockType) => {
        const isAvailable: boolean =
          !urlParams.stock_type || urlParams.stock_type.includes(stockType)

        if (isAvailable) {
          return [...memo, { label: stockType, value: stockType }]
        }

        return memo
      },
      [],
    )

    const offer_type = OFFER_TYPES.reduce<Option[]>((memo, offerType) => {
      const isAvailable: boolean =
        !urlParams.offer_type || urlParams.offer_type.includes(offerType)

      if (isAvailable) {
        return [...memo, { label: offerType, value: offerType }]
      }

      return memo
    }, [])

    // @ts-expect-error
    const order_status = getObjectKeys<OrderStatuses>(orderStatuses).map(
      (orderStatus) => ({
        value: orderStatus,
        label: l(
          // @ts-expect-error
          ORDER_STATUSES[orderStatus]?.title || orderStatuses[orderStatus],
        ),
      }),
    )

    return {
      marketplace_id,
      seller_id: sellerIdOptions,
      product_stock_type,
      offer_type,
      order_status,
      product_condition: productConditions,
    }
  }, [
    filteredBasAccountsOptions,
    getMarketplaceOptions,
    orderStatuses,
    productConditions,
    seller_id,
    urlParams,
  ])

  // DESC: This causes re-render of the TableWrapper w/o useMemo
  const globalUrlParams = useMemo(() => {
    return {
      seller_id: urlParams.seller_id,
      marketplace_id: urlParams.marketplace_id,
      adult_product: urlParams.adult_product,
      age_range: urlParams.age_range,
      asin: urlParams.asin,
      brand: urlParams.brand,
      currency_code: urlParams.currency_code,
      customerID: urlParams.customerID,
      ean: urlParams.ean,
      from: urlParams.from,
      inputMode: urlParams.inputMode,
      isbn: urlParams.isbn,
      isFromRepricer: urlParams.isFromRepricer,
      manufacturer: urlParams.manufacturer,
      offer_type: urlParams.offer_type,
      parent_asin: urlParams.parent_asin,
      period: urlParams.period,
      product_type: urlParams.product_type,
      productASIN: urlParams.productASIN,
      productId: urlParams.productId,
      productMarketplace: urlParams.productMarketplace,
      productRepricerId: urlParams.productRepricerId,
      productSeller: urlParams.productSeller,
      productSku: urlParams.productSku,
      segmentId: urlParams.segmentId,
      sku: urlParams.sku,
      stock_type: urlParams.stock_type,
      tags: urlParams.tags,
      to: urlParams.to,
      upc: urlParams.upc,
      view: urlParams.view,
    } as DashboardFiltersParams
  }, [urlParams])

  const getDataHandler = useCallback(
    (params: TableUrlParams<OrderItemsTableColumnsUrlParams>): void => {
      if (orderItemsTableDataStatus === ASYNC_STATUSES.PENDING) {
        return
      }

      dispatch(orderItemsTableActions.changeSearchOptions(params))

      const sellerId = params.seller_id || urlParams.seller_id
      const marketplaceId = params.marketplace_id || urlParams.marketplace_id

      const { seller_id, marketplace_id, marketplaceSellerIds } =
        getSellerMarketplaceParams({
          ...urlParams,
          seller_id: sellerId,
          marketplace_id: marketplaceId,
        })

      const getOrderPurchaseDateParam = (
        params: TableUrlParams<OrderItemsTableColumnsUrlParams>,
      ): string | null => {
        if (!params.order_purchase_date) {
          return null
        }

        const from = urlParams.from
          ? format(new Date(urlParams.from), DATE_FNS_FORMATS.SERVER)
          : null

        const to = urlParams.to
          ? format(new Date(urlParams.to), DATE_FNS_FORMATS.SERVER)
          : null

        const range = [from, to]

        return (
          getStringRangeInDateRange({
            dateRangeString: params.order_purchase_date,
            range,
          }) || null
        )
      }

      const order_purchase_date: string | undefined =
        getOrderPurchaseDate({
          rangeDateString: getOrderPurchaseDateParam(params),
          dateFrom: urlParams.from,
          dateTo: urlParams.to,
          convertToUTC: true,
        }) ?? undefined

      // DESC: Merge of tableParams and urlParams
      const requestParams: AmazonOrderRequestType = {
        adult_product: urlParams.adult_product === "true" ? "1" : undefined,
        amazon_fees_amount: params.amazon_fees_amount,
        currency_id: urlParams.currency_code,
        estimated_profit_amount: params.estimated_profit_amount,
        expenses_amount: params.expenses_amount,
        item_price: params.item_price,
        offer_type: urlParams.offer_type || params.offer_type,
        order_id: params.order_id,
        order_purchase_date,
        order_status: params.order_status,
        product_asin:
          urlParams.productASIN || urlParams.asin || params.product_asin,
        product_brand: urlParams.brand || params.product_brand,
        product_condition: params.product_condition,
        product_ean: urlParams.ean,
        product_id: params.product_id,
        product_isbn: urlParams.isbn,
        product_manufacturer:
          urlParams.manufacturer || params.product_manufacturer,
        product_parent_asin: urlParams.parent_asin,
        product_stock_type: urlParams.stock_type || params.product_stock_type,
        product_title: params.product_title,
        product_type: params.product_type || urlParams.product_type,
        product_upc: urlParams.upc,
        promotion_amount: params.promotion_amount,
        quantity: params.quantity,
        quantity_refunded: params.quantity_refunded,
        revenue_amount: params.revenue_amount,

        seller_id,
        marketplace_id,
        marketplace_seller_ids: marketplaceSellerIds,

        page: params.page,
        pageSize: params.pageSize,
        sort: params.sort,
        seller_sku: urlParams.productSku || urlParams.sku || params.seller_sku,
      }

      dispatch(
        getAmazonOrders({
          params: removeNullAndUndefined(requestParams),
          successCallback: () => {},
          failureCallback: () => {},
        }),
      )
    },
    [
      dispatch,
      getSellerMarketplaceParams,
      orderItemsTableDataStatus,
      urlParams,
    ],
  )

  const getAdditionalData = useCallback(() => {
    dispatch(getAmazonOrderStatuses({}))
  }, [dispatch])

  const handleGridUnmount = useCallback(() => {
    dispatch(clearAmazonOrdersData())
  }, [dispatch])

  useTableColumnsVisibility({
    visibleColumns: ["seller_id"],
    tableSettingsKey: TABLE_SETTINGS_KEY,
    canUpdateSettings: !isOneBASAccount,
  })

  return {
    orderItemsTableData,
    orderItemsTableTotalCount,
    selectFiltersOptions,
    globalUrlParams,
    getDataHandler,
    getAdditionalData,
    urlParams,
    customExcludeKeys: getObjectKeys(globalUrlParams),
    orderItemsTableSearchOptions,
    handleGridUnmount,
    isTableLoading: orderItemsTableDataStatus === ASYNC_STATUSES.PENDING,
  }
}
