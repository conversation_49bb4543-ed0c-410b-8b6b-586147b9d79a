import React, { useMemo } from "react"
import { useSelector } from "react-redux"
import { Box, EmptyImage, IconPopover, Typography } from "@develop/fe-library"
import { checkIsNonEmptyNumber } from "@develop/fe-library/dist/utils"

import { getSelectedTableSettingsSelector } from "selectors/tableSettingsSelectors"

import AmazonCustomerAccountColumn from "components/Grid/components/amazonCustomerAccountColumn/AmazonCustomerAccountColumn"
import { ConditionsColumn } from "components/Grid/components/conditionColumn/ConditionsColumn"
import { MarketplaceColumn } from "components/Grid/components/marketplaceColumn/MarketplaceColumn"
import { OrderNumberColumn } from "components/Grid/components/orderNumberColumn"
import { StatusColumn } from "components/Grid/components/statusColumn"
import { Blurred } from "components/shared/Blurred"
import { BlurredWithGenerator } from "components/shared/BlurredWithGenerator"
import LinkView from "components/shared/Link"
import TextEllipsis from "components/shared/maxTextLines/TextEllipsis"

import { useGridProduct, useSubscription, useUrlParams } from "hooks"
import { useGenerateLink } from "hooks/useGenerateLink/useGenerateLink"

import { buildCustomTableColumns } from "utils/buildCustomTableColumns"
import {
  convertStringDateToMoment,
  convertToLocalDateTime,
} from "utils/dateConverter"
import { getProductCostsPageProductCostDrawerLink } from "utils/getProductCostsPageProductCostDrawerLink"
import l from "utils/intl"
import ln from "utils/localeNumber"
import { checkIsNullOrUndefined } from "utils/validationHelper"

import {
  COLUMN_INPUT_TYPE_DATE_RANGE,
  COLUMN_INPUT_TYPE_INPUT,
  COLUMN_INPUT_TYPE_NUMBER,
  COLUMN_INPUT_TYPE_SELECT,
  COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
} from "constants/grid"
import { TABLE_SETTINGS_KEY } from "constants/orderItemsTable"
import { ORDER_STATUSES, STATUS_TAGS_MAP } from "constants/orders"

import { AmazonOrderExtendedViewItem, DashboardFiltersParams } from "types"
import { ProductInfo } from "interfaces/GridInterfaces"

import { Colors } from "@develop/fe-library/dist/lib/types"

export const useOrderItemsTableColumns = () => {
  const selectedTableSettingsColumns = useSelector((state) =>
    getSelectedTableSettingsSelector(state, TABLE_SETTINGS_KEY),
  )

  const { urlParams } = useUrlParams<DashboardFiltersParams>()
  const { getProductImage, getProductLink } = useGridProduct()
  const { getOrdersLink, getTransactionsLink } = useGenerateLink()
  const { isFreemiumActive } = useSubscription()

  const columns = useMemo(() => {
    const NotAvailable: string = l("N/A")

    const shouldBlur = isFreemiumActive

    const tableColumns = [
      {
        title: "Brand",
        dataIndex: "product_brand",
        key: "product_brand",
        sorter: true,
        ellipsis: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 120,
        min: 120,
        render: (value: string) => {
          if (shouldBlur) {
            return <BlurredWithGenerator>{l("Brand")}</BlurredWithGenerator>
          }

          return value ? (
            <TextEllipsis
              ellipsisChars={"..."}
              fontSize={12}
              lines={1}
              tooltip={value}
            >
              {value}
            </TextEllipsis>
          ) : (
            NotAvailable
          )
        },
      },
      {
        title: "Product Type",
        dataIndex: "product_type",
        key: "product_type",
        sorter: true,
        ellipsis: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 100,
        min: 100,
        render: (value: string) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Product Type")}</BlurredWithGenerator>
            )
          }

          return value ? (
            <TextEllipsis
              ellipsisChars={"..."}
              fontSize={12}
              lines={1}
              tooltip={value}
            >
              {value}
            </TextEllipsis>
          ) : (
            NotAvailable
          )
        },
      },
      {
        title: "Manufacturer",
        dataIndex: "product_manufacturer",
        key: "product_manufacturer",
        sorter: true,
        ellipsis: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 100,
        min: 100,
        render: (value: string) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Manufacturer")}</BlurredWithGenerator>
            )
          }

          return value ? (
            <TextEllipsis
              ellipsisChars={"..."}
              fontSize={12}
              lines={1}
              tooltip={value}
            >
              {value}
            </TextEllipsis>
          ) : (
            NotAvailable
          )
        },
      },
      {
        title: "Order number",
        dataIndex: "order_id",
        key: "order_id",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 120,
        min: 120,
        render: (value: string, product: AmazonOrderExtendedViewItem) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Order number")}</BlurredWithGenerator>
            )
          }

          return value ? (
            <OrderNumberColumn product={product as unknown as ProductInfo} />
          ) : (
            NotAvailable
          )
        },
      },
      {
        title: "Order status",
        dataIndex: "order_status",
        key: "order_status",
        sorter: true,
        width: 220,
        min: 220,
        type: COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
        render: (status: string) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Order status")}</BlurredWithGenerator>
            )
          }

          return status ? (
            <StatusColumn
              isCenter
              status={status}
              statuses={ORDER_STATUSES}
              tagsConfig={STATUS_TAGS_MAP}
            />
          ) : (
            NotAvailable
          )
        },
      },
      {
        title: "Order date",
        dataIndex: "order_purchase_date",
        key: "order_purchase_date",
        sorter: true,
        type: COLUMN_INPUT_TYPE_DATE_RANGE,
        render: (date: string) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Order date")}</BlurredWithGenerator>
            )
          }

          return date ? convertToLocalDateTime(date, true) : NotAvailable
        },
      },
      {
        title: "Amazon account name",
        dataIndex: "seller_id",
        key: "seller_id",
        width: 120,
        min: 80,
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT,
        ellipsis: true,
        className: "flex-string",
        render: (value: string, product: AmazonOrderExtendedViewItem) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>
                {l("Amazon account name")}
              </BlurredWithGenerator>
            )
          }

          return value ? (
            <AmazonCustomerAccountColumn
              object={product as unknown as ProductInfo}
            />
          ) : (
            NotAvailable
          )
        },
      },
      {
        title: "Marketplace",
        dataIndex: "marketplace_id",
        key: "marketplace_id",
        sorter: true,
        width: 120,
        min: 120,
        type: COLUMN_INPUT_TYPE_SELECT,
        ellipsis: true,
        render: (marketplaceId: string) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Marketplace")}</BlurredWithGenerator>
            )
          }

          return marketplaceId ? (
            // @ts-expect-error
            <MarketplaceColumn marketplaceId={marketplaceId} />
          ) : (
            NotAvailable
          )
        },
      },
      {
        title: "ASIN",
        dataIndex: "product_asin",
        key: "product_asin",
        min: 80,
        width: 100,
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        ellipsis: true,
        render: (value: string, product: AmazonOrderExtendedViewItem) => {
          if (checkIsNullOrUndefined(value)) {
            return NotAvailable
          }

          if (isFreemiumActive) {
            return value
          }

          const productLink = getProductLink({
            marketplace_id: product?.marketplace_id,
            asin: value,
          } as ProductInfo)

          if (!productLink) {
            return value
          }

          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={value}
              type="span"
              url={productLink}
              variant="textSmall"
            />
          )
        },
      },
      {
        title: "SKU",
        dataIndex: "seller_sku",
        key: "seller_sku",
        sorter: true,
        width: 100,
        min: 80,
        type: COLUMN_INPUT_TYPE_INPUT,
        ellipsis: true,
        className: "flex-string",
        render: (value: string, orderItem) => {
          if (isFreemiumActive || !value) {
            return value ? (
              <TextEllipsis
                ellipsisChars={"..."}
                fontSize={12}
                lines={1}
                tooltip={value}
              >
                {value}
              </TextEllipsis>
            ) : (
              NotAvailable
            )
          }

          return (
            <LinkView
              internal
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={value}
              type="span"
              variant="textSmall"
              url={getProductCostsPageProductCostDrawerLink({
                marketplaceId: orderItem.marketplace_id,
                sellerSku: orderItem.seller_sku,
                shouldIncludeOrigin: false,
              })}
            />
          )
        },
      },
      {
        title: "Image",
        dataIndex: "image",
        key: "image",
        onCell: () => ({
          style: { padding: "var(--padding-s) 0" },
        }),
        width: 70,
        min: 80,
        render: (value: string, product: AmazonOrderExtendedViewItem) => {
          const productImageUrl = getProductImage({
            seller_id: product?.seller_id,
            asin: product?.product_asin,
          } as ProductInfo)

          return (
            <Box justify="center">
              <Blurred shouldBlur={shouldBlur}>
                <EmptyImage height={53} url={productImageUrl} width={64} />
              </Blurred>
            </Box>
          )
        },
      },
      {
        title: "Title",
        dataIndex: "product_title",
        key: "product_title",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 300,
        min: 80,
        class: "left",
        onCell: () => ({
          className: "left title",
          style: { textAlign: "left" },
        }),
        render: (title: string) => {
          return title ? (
            <TextEllipsis
              ellipsisChars={"..."}
              fontSize={12}
              lines={3}
              tooltip={title}
            >
              {title}
            </TextEllipsis>
          ) : (
            NotAvailable
          )
        },
      },
      {
        title: "Product ID",
        dataIndex: "product_id",
        key: "product_id",
        sorter: true,
        type: COLUMN_INPUT_TYPE_INPUT,
        width: 100,
        render: (value: string) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Product ID")}</BlurredWithGenerator>
            )
          }

          return value ?? NotAvailable
        },
      },
      {
        title: "Condition",
        dataIndex: "product_condition",
        key: "product_condition",
        width: 120,
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT,
        onCell: () => ({ className: "condition" }),
        render: (value: string) => {
          if (shouldBlur) {
            return <BlurredWithGenerator>{l("Condition")}</BlurredWithGenerator>
          }

          return value ? (
            // @ts-ignore
            <ConditionsColumn conditionId={+value} />
          ) : (
            NotAvailable
          )
        },
      },
      {
        title: "Fulfillment method",
        dataIndex: "product_stock_type",
        key: "product_stock_type",
        width: 120,
        min: 80,
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT,
        ellipsis: true,
        render: (value: string) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>
                {l("Fulfillment method")}
              </BlurredWithGenerator>
            )
          }

          return value ?? NotAvailable
        },
      },
      {
        title: "Offer type",
        dataIndex: "offer_type",
        key: "offer_type",
        width: 100,
        min: 80,
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT,
        ellipsis: true,
        render: (value: string) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Offer type")}</BlurredWithGenerator>
            )
          }

          return value ?? NotAvailable
        },
      },
      {
        title: "Unit price",
        dataIndex: "item_price",
        key: "item_price",
        sorter: true,
        type: COLUMN_INPUT_TYPE_NUMBER,
        width: 80,
        render: (value: number, product: AmazonOrderExtendedViewItem) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Unit price")}</BlurredWithGenerator>
            )
          }

          return value === null
            ? NotAvailable
            : ln(value, 2, {
                currency: product.currency_id,
              })
        },
      },
      {
        title: "Units",
        dataIndex: "quantity",
        key: "quantity",
        sorter: true,
        type: COLUMN_INPUT_TYPE_NUMBER,
        width: 80,
        render: (value: number, product: AmazonOrderExtendedViewItem) => {
          if (shouldBlur) {
            return <BlurredWithGenerator>{l("Units")}</BlurredWithGenerator>
          }

          if (!checkIsNonEmptyNumber(value)) {
            return NotAvailable
          }

          const ordersLink = getOrdersLink({
            product,
            urlParams,

            ordersUrlParams: {
              order_status: [
                ORDER_STATUSES.Pending.value,
                ORDER_STATUSES.Unshipped.value,
                ORDER_STATUSES.PartiallyShipped.value,
                ORDER_STATUSES.Shipped.value,
                ORDER_STATUSES.Unfulfillable.value,
                ORDER_STATUSES.InvoiceUnconfirmed.value,
                ORDER_STATUSES.PendingAvailability.value,
              ],
              order_id: product.order_id,
            },
          })

          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={value}
              type="span"
              url={ordersLink}
              variant="textSmall"
            />
          )
        },
      },
      {
        title: "Revenue",
        dataIndex: "revenue_amount",
        key: "revenue_amount",
        sorter: true,
        type: COLUMN_INPUT_TYPE_NUMBER,
        width: 80,
        render: (value: number, product: AmazonOrderExtendedViewItem) => {
          if (checkIsNullOrUndefined(value)) {
            return NotAvailable
          }

          const formattedValue = ln(value, 2, {
            currency: product.currency_id,
          })

          if (isFreemiumActive) {
            return formattedValue
          }

          // Remove link for BAS-2418, will be added back in future
          return formattedValue

          const transactionsLink = getTransactionsLink({
            product,
            urlParams,
            transactionsUrlParams: {
              sales_category_depth_1: ["revenue"],
              amount: ">0",
              amazon_order_id: product.order_id,
            },
          })

          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={formattedValue}
              type="span"
              url={transactionsLink}
              variant="textSmall"
            />
          )
        },
      },
      {
        title: "Promotion",
        dataIndex: "promotion_amount",
        key: "promotion_amount",
        sorter: true,
        type: COLUMN_INPUT_TYPE_NUMBER,
        width: 80,
        render: (value: number, product: AmazonOrderExtendedViewItem) => {
          if (shouldBlur) {
            return <BlurredWithGenerator>{l("Promotion")}</BlurredWithGenerator>
          }

          return value === null
            ? NotAvailable
            : ln(value, 2, {
                currency: product.currency_id,
              })
        },
      },
      {
        title: "Amazon fees",
        dataIndex: "amazon_fees_amount",
        key: "amazon_fees_amount",
        sorter: true,
        type: COLUMN_INPUT_TYPE_NUMBER,
        width: 80,
        render: (value: number, product: AmazonOrderExtendedViewItem) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Amazon fees")}</BlurredWithGenerator>
            )
          }

          if (checkIsNullOrUndefined(value)) {
            return NotAvailable
          }

          const formattedValue = ln(value, 2, {
            currency: product.currency_id,
          })

          // Remove link for BAS-2418, will be added back in future
          return formattedValue

          const transactionsLink = getTransactionsLink({
            product,
            urlParams,
            transactionsUrlParams: {
              sales_category_depth_1: ["expenses"],
              sales_category_depth_2: ["amazon_fees"],
              amazon_order_id: product.order_id,
            },
          })

          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={formattedValue}
              type="span"
              url={transactionsLink}
              variant="textSmall"
            />
          )
        },
      },
      {
        title: "Expenses",
        dataIndex: "expenses_amount",
        key: "expenses_amount",
        sorter: true,
        type: COLUMN_INPUT_TYPE_NUMBER,
        width: 80,
        render: (value: number, product: AmazonOrderExtendedViewItem) => {
          if (shouldBlur) {
            return <BlurredWithGenerator>{l("Expenses")}</BlurredWithGenerator>
          }

          if (checkIsNullOrUndefined(value)) {
            return NotAvailable
          }

          const formattedValue = ln(value, 2, {
            currency: product.currency_id,
          })

          // Remove link for BAS-2418, will be added back in future
          return formattedValue

          const transactionsLink = getTransactionsLink({
            product,
            urlParams,
            transactionsUrlParams: {
              sales_category_depth_1: ["expenses"],
              amount: "<0",
              amazon_order_id: product.order_id,
            },
          })

          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={formattedValue}
              type="span"
              url={transactionsLink}
              variant="textSmall"
            />
          )
        },
      },
      {
        title: "Estimated margin",
        dataIndex: "estimated_profit_amount",
        key: "estimated_profit_amount",
        sorter: true,
        type: COLUMN_INPUT_TYPE_NUMBER,
        width: 80,
        render: (value: number, product: AmazonOrderExtendedViewItem) => {
          if (checkIsNullOrUndefined(value)) {
            return NotAvailable
          }

          const color: Colors =
            value < 0 ? "--color-text-error" : "--color-text-main"

          const formattedValue: string = ln(value, 2, {
            currency: product.currency_id,
          })

          return (
            <Typography color={color} variant="--font-body-text-9">
              {formattedValue}
            </Typography>
          )
        },
      },
      {
        title: "Refunded units",
        dataIndex: "quantity_refunded",
        key: "quantity_refunded",
        sorter: true,
        type: COLUMN_INPUT_TYPE_NUMBER,
        width: 80,
        render: (value: number, product: AmazonOrderExtendedViewItem) => {
          if (shouldBlur) {
            return (
              <BlurredWithGenerator>{l("Refunded units")}</BlurredWithGenerator>
            )
          }

          if (!checkIsNonEmptyNumber(value)) {
            return NotAvailable
          }

          const ordersLink = getOrdersLink({
            product,
            urlParams,
            ordersUrlParams: {
              quantity_refunded: ">0",
              order_id: product.order_id,
            },
          })

          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={value}
              type="span"
              url={ordersLink}
              variant="textSmall"
            />
          )
        },
      },
    ]

    const transformedTableColumns = tableColumns.map((column) => {
      if (column.key !== "order_purchase_date") {
        return column
      }

      return {
        ...column,
        showTimeZoneToggle: false,
        filterType: "between",
        isClearDatesToReset: true,
        convertUtcToUserTimeZone: false,
        clearToDates: [
          convertStringDateToMoment(urlParams.from),
          convertStringDateToMoment(urlParams.to),
        ],
        isDatePickerWithFilter: false,
        disabledDatePickerDates: (currentDate: moment.Moment) => {
          return !currentDate.isBetween(
            // @ts-expect-error
            convertStringDateToMoment(urlParams.from),
            convertStringDateToMoment(urlParams.to),
            "days",
            "[]",
          )
        },
      }
    })

    const selectionColumn = {
      key: "selection_column",
      sorter: false,
      type: "action",
      dataIndex: "selection_column",
      width: 20,
      min: 20,
      onCell: () => ({
        style: { padding: "0 var(--padding-m)" },
      }),
      render: (value: boolean, product: AmazonOrderExtendedViewItem) => {
        return (
          <Box gap="m" zIndex={1}>
            {product.is_approximate_amounts_calculation ? (
              <IconPopover
                color="--color-icon-warning"
                name="icnWarning"
                placement="topLeft"
                size="--icon-size-3"
                content={l(
                  "The order includes estimated values. The data will change once actual transactions are reported.",
                )}
              />
            ) : null}
          </Box>
        )
      },
    }

    const customTableColumns = buildCustomTableColumns({
      tableColumns: transformedTableColumns,
      columns: selectedTableSettingsColumns,
    })

    return [selectionColumn, ...customTableColumns]
  }, [
    getOrdersLink,
    getProductImage,
    getProductLink,
    getTransactionsLink,
    isFreemiumActive,
    selectedTableSettingsColumns,
    urlParams,
  ])

  return {
    columns,
  }
}
