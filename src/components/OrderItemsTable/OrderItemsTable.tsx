import React from "react"

import { TableWrapper } from "components/shared/TableWrapper"

import { TABLE_SETTINGS_KEY } from "constants/orderItemsTable"

import { URL_WHITE_LIST } from "./constants"

import { useOrderItemsTable } from "./hooks/useOrderItemsTable"
import { useOrderItemsTableColumns } from "./hooks/useOrderItemsTableColumns"

import styles from "./orderItemsTable.module.scss"

export const OrderItemsTable = () => {
  const { columns } = useOrderItemsTableColumns()
  const {
    orderItemsTableData,
    orderItemsTableTotalCount,
    selectFiltersOptions,
    globalUrlParams,
    getDataHandler,
    customExcludeKeys,
    orderItemsTableSearchOptions,
    handleGridUnmount,
    isTableLoading,
    getAdditionalData,
  } = useOrderItemsTable()

  return (
    <TableWrapper
      // @ts-ignore
      isCustomHeader
      isCustomTableColumns
      isGlobalUrlParams
      isNeedSort
      isStartFromColumnZeroIndex
      actionsColumn={false}
      componentTableSettings={TABLE_SETTINGS_KEY}
      contentClassName={styles.orderItemsTable}
      customDefaultSort="-quantity"
      customExcludeKeys={customExcludeKeys}
      customTableColumns={columns}
      dataSource={orderItemsTableData}
      filterPrefix="oi"
      footerGridContainerStyles={{ height: "32px" }}
      getAdditionalData={getAdditionalData}
      getData={getDataHandler}
      globalUrlParams={globalUrlParams}
      handleGridUnmount={handleGridUnmount}
      isCopyrightVisibleWithNoData={false}
      isHeaderVisible={false}
      pageTableSettings={TABLE_SETTINGS_KEY}
      searchOptions={orderItemsTableSearchOptions}
      selectFiltersOptions={selectFiltersOptions}
      tableClassName="gridView"
      tableLoading={isTableLoading}
      totalCount={orderItemsTableTotalCount}
      urlFiltersWhiteList={URL_WHITE_LIST}
      withExport={false}
      tableFooterStyles={{
        position: "static",
        padding: "var(--padding-m)",
        justifyContent: "space-between",
      }}
    />
  )
}
