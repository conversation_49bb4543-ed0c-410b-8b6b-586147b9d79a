import React, { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Box, Modal, Typography } from "@develop/fe-library"

import { ordersActions } from "actions/ordersActions"

import {
  getAmazonFeesBreakdownSelector,
  getIsModalDataLoadingSelector,
  getIsModalVisibleSelector,
  getModalNameSelector,
} from "selectors/ordersSelectors"

import { OrderInfo } from "components/OrderInfo"
import { getAmount } from "components/Orders/utils"
import { List, ListItem } from "components/shared/List"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import { EstimatedValuesAlert } from "../../components"

import styles from "../../orders.module.scss"

const { displayModal, getAmazonOrderFeesBreakdown } = ordersActions

export const OrderFeesBreakdownModal = () => {
  const dispatch = useDispatch()

  const isModalVisible: boolean = useSelector(getIsModalVisibleSelector)
  const isModalDataLoading: boolean = useSelector(getIsModalDataLoadingSelector)
  const modalName = useSelector(getModalNameSelector)

  const amazonFeesBreakdown = useSelector(getAmazonFeesBreakdownSelector)
  const {
    items = [],
    footer,
    currencyId = "",
    is_approximate_amounts_calculation: isApproximateAmountsCalculation,
  } = amazonFeesBreakdown ?? {}

  const { amount: footerAmount = null } = footer || {}

  const handleModalClose = () => {
    dispatch(displayModal({ isModalVisible: false }))
  }

  useEffect(() => {
    if (isModalVisible) {
      dispatch(getAmazonOrderFeesBreakdown({}))
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModalVisible])

  if (!modalName) {
    return null
  }

  return (
    <Modal
      visible
      bodyClassName={styles.modalWrapper}
      title={l("Amazon fees breakdown")}
      width="--modal-size-m"
      onCancel={handleModalClose}
      onClose={handleModalClose}
    >
      {isModalDataLoading ? null : (
        <Box flexDirection="column" gap="l">
          {isApproximateAmountsCalculation ? <EstimatedValuesAlert /> : null}

          <OrderInfo />

          {!checkIsArray(items)
            ? null
            : items.map(({ hasChildren, id, name, children }) => {
                return hasChildren ? (
                  <Box key={id} flexDirection="column" gap="s">
                    <Typography variant="--font-body-text-3">
                      {l(name)}
                    </Typography>

                    <List>
                      {!checkIsArray(children)
                        ? null
                        : children.map(({ name, amount }) => {
                            return (
                              <ListItem
                                name={l(name)}
                                {...getAmount({ amount, currencyId })}
                              />
                            )
                          })}
                    </List>
                  </Box>
                ) : null
              })}

          <Box flexDirection="column" gap="s">
            <List>
              <ListItem
                isFilled
                name={l("Total")}
                type="total"
                {...getAmount({ amount: footerAmount, currencyId })}
              />
            </List>
          </Box>
        </Box>
      )}
    </Modal>
  )
}
