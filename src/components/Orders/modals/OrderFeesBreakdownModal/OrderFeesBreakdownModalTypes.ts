import type { OrderType } from "types/OrderType"

export type OrderFeeChildrenItemType = {
  id: string
  name: string
  depth: number
  color_hex: string
  amount: number
  type: string
  children: OrderFeeChildrenItemType[]
  hasChildren: boolean
}

export type OrderFeeTotalType = Omit<
  OrderFeeChildrenItemType,
  "depth" | "color_hex"
>

type PickedProps = Partial<
  Pick<OrderType, "is_approximate_amounts_calculation">
>

export type FeesBreakdownTypes = PickedProps & {
  items?: OrderFeeChildrenItemType[]
  footer?: OrderFeeTotalType
  currencyId?: string
}
