import { useCallback, useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"

import gridActions from "actions/gridActions"
import marketplaceGroupsActions from "actions/marketplaceGroupsActions"
import { ordersActions } from "actions/ordersActions"
import userSettingsActions from "actions/userSettingsActions"

import {
  getLastUpdateOrdersDateSelector,
  ordersItemsSelector,
  ordersSelector,
} from "selectors/ordersSelectors"

import { usePageName } from "components/shared/Page/MainFilter/hooks"

import type { HandleSetUrlParams, OrdersUrlParams } from "types/UrlParams"

const { updateMainFilterPageSettings } = userSettingsActions
const { updateUrlParams } = ordersActions
const { pushUrl } = gridActions
const { getAllMarketplaceGroups } = marketplaceGroupsActions

export const useOrdersHeader = () => {
  const dispatch = useDispatch()

  const pageName = usePageName()

  const lastUpdateOrdersDate = useSelector(getLastUpdateOrdersDateSelector)
  const { searchOptions } = useSelector(ordersSelector)
  const ordersItems = useSelector(ordersItemsSelector)
  const isOrderItemsEmpty: boolean = !ordersItems?.length

  const handleUpdateUrlParams: HandleSetUrlParams<OrdersUrlParams> =
    useCallback(
      (params) => {
        dispatch(updateUrlParams(params))
        dispatch(
          updateMainFilterPageSettings({
            params,
            page: pageName,
          }),
        )

        dispatch(
          pushUrl({
            ...searchOptions,
            ...params,
          }),
        )
      },
      [dispatch, pageName, searchOptions],
    )

  useEffect(() => {
    dispatch(getAllMarketplaceGroups())
  }, [dispatch])

  return {
    lastUpdateOrdersDate,
    handleUpdateUrlParams,
    isOrderItemsEmpty,
  }
}
