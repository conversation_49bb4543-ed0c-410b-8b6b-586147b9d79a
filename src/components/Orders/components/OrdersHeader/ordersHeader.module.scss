@import "assets/styles/variables";

.filterContainer {
  @media screen and (min-width: $md) {
    padding: var(--padding-m) var(--padding-l);
    flex-direction: row;
  }
}

.filterLeftContainer {
  flex-direction: column;

  @media screen and (min-width: $xs) {
    flex-direction: row;
  }

  @media screen and (min-width: $md) {
    margin-right: auto;
  }

  :global(.marketplace-selector-container),
  :global(.groups-selector-container),
  :global(.groups-selector),
  .datePickerContainer {
    max-width: 100%;
    width: 100%;
    margin: 0;
    
    @media screen and (min-width: $size320) {
      flex: 1 0;
    }

    @media screen and (min-width: $md) {
      min-width: unset;
      max-width: 200px;
    }
  }
}

.filterRightContainer {
  @media screen and (min-width: $md) {
    margin-left: auto;
  }

  button {
    flex-shrink: 0;
  }
}

.editGroupsButton {
  display: none;

  @media screen and (min-width: 860px) {
    display: inline-flex;
  }
}

.editGroupsButtonMobile {
  @media screen and (min-width: 860px) {
    display: none;
  }
}

.currencySelector {
  min-width: 120px;
  width: 100%;

  @media screen and (min-width: $md) {
    width: 120px;
  }
}
