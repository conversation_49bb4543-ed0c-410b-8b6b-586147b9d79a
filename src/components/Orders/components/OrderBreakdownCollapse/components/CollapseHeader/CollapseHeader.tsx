import React from "react"
import { Box, Icon, Typography } from "@develop/fe-library"
import cn from "classnames"

import { formatWidgetValue } from "utils/formatWidgetValue"
import l from "utils/intl"

import { UNIT_TYPES } from "constants/widgets"

import { CollapseHeaderProps } from "./CollapseHeaderTypes"

import styles from "./collapseHeader.module.scss"

export const CollapseHeader = ({
  title,
  value,
  hasChildren,
  depth,
  currencyId,
  isParent = false,
  isExpanded,
  toggle,
  type,
  is_default,
}: CollapseHeaderProps): JSX.Element => {
  const isExpandedFirstDepth = hasChildren && isExpanded && depth === 1
  const isInDeepDepth = depth && depth > 2
  const chevronIcnName = isExpanded ? "icnChevronUp" : "icnChevronDown"
  const isValueNegative = value && typeof value === "number" && value < 0
  const textVariant = isParent ? "--font-body-text-3" : "--font-body-text-9"
  const textColor = isValueNegative ? "--color-text-error" : "--color-text-main"
  const isWithoutChildInDeepDepth = isInDeepDepth && !hasChildren

  const handleClickDisabled = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation()
  }

  const itemValue = formatWidgetValue({
    value,
    type: UNIT_TYPES[type] || "count",
    currency: currencyId,
  })

  const correctTitle: CollapseHeaderProps["title"] = is_default
    ? l(title)
    : title

  return (
    <Box
      gap="m"
      justify="space-between"
      padding="m"
      className={cn(styles.header, {
        [styles.hasChildren]: hasChildren,
        [styles.hasHeaderBorder]: isExpandedFirstDepth,
      })}
      onClick={hasChildren ? toggle : handleClickDisabled}
    >
      <Box
        align="center"
        gap="m"
        className={cn(styles.headerContent, {
          [styles.customHeaderContent]: !hasChildren,
          [styles.expandedInDepth]: isWithoutChildInDeepDepth,
        })}
      >
        {!hasChildren ? null : (
          <Icon name={chevronIcnName} size="--icon-size-5" onClick={toggle} />
        )}
        <Typography variant={textVariant} onClick={handleClickDisabled}>
          {correctTitle}
        </Typography>
      </Box>
      <Typography
        color={textColor}
        variant={textVariant}
        onClick={handleClickDisabled}
      >
        {itemValue}
      </Typography>
    </Box>
  )
}
