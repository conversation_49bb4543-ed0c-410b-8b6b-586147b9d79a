@import "assets/styles/variables";

.header.header {
  cursor: default;

  @media (min-width: $sm) {
    padding: var(--padding-m) var(--padding-l);
  }
}

.hasChildren.hasChildren {
  cursor: pointer;
}

.hasHeaderBorder {
  border-bottom: var(--border-main);
}

.customHeaderContent {
  cursor: default;
  padding-left: 30px;

  :global(div[data-component-type="contents"]) {
    border-bottom: none;
  }
}

.expandedInDepth.expandedInDepth {
  @media (min-width: $sm) {
    padding-left: 50px;
  }
}
