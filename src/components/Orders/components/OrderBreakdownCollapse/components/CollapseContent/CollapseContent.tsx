import React from "react"
import { Collapse, CollapseProps } from "@develop/fe-library"

import { CollapseHeader, CollapseHeaderOwnProps } from "../CollapseHeader"

import { CollapseContentProps } from "./CollapseContentTypes"

export const CollapseContent = ({
  hasChildren,
  children,
  currencyId = null,
}: CollapseContentProps): JSX.Element | null => {
  if (!hasChildren) {
    return null
  }

  const items: CollapseProps["items"] =
    children?.map(({ id, name, amount, ...childrenItem }) => ({
      key: `${id}`,
      header: ({ isExpanded, toggle }: CollapseHeaderOwnProps) => (
        <CollapseHeader
          currencyId={currencyId}
          isExpanded={isExpanded}
          title={name}
          toggle={toggle}
          value={amount}
          {...childrenItem}
        />
      ),
      contents: <CollapseContent {...childrenItem} currencyId={currencyId} />,
    })) || []

  return (
    <Collapse
      hasChevron={false}
      hasTopAndBottomBorders={false}
      headerGap="0"
      headerPadding="0"
      items={items}
      variant="outlined"
    />
  )
}
