import React from "react"
import { Collapse } from "@develop/fe-library"

import { checkIsArray } from "utils/arrayHelpers"

import { CollapseContent } from "./components"

import {
  CollapseHeader,
  CollapseHeaderOwnProps,
} from "./components/CollapseHeader"

import { OrderProductItemProps } from "./OrderBreakdownCollapseTypes"

export const OrderBreakdownCollapse = ({
  collapseData = [],
  currencyId = null,
}: OrderProductItemProps) => {
  if (!checkIsArray(collapseData)) {
    return null
  }

  const items = collapseData.map((collapseItem) => {
    const { id, amount, name, ...restCollapseData } = collapseItem

    return {
      key: `${id}`,
      header: ({ isExpanded, toggle }: CollapseHeaderOwnProps) => (
        <CollapseHeader
          isParent
          currencyId={currencyId}
          isExpanded={isExpanded}
          title={name}
          toggle={toggle}
          value={amount}
          {...restCollapseData}
        />
      ),
      contents: <CollapseContent {...collapseItem} currencyId={currencyId} />,
    }
  })

  return (
    <Collapse
      hasLeftAndRightBorders
      hasChevron={false}
      headerGap="0"
      headerPadding="0"
      items={items}
    />
  )
}
