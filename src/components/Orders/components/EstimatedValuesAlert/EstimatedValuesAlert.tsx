import React from "react"
import { Alert } from "@develop/fe-library"

import l from "utils/intl"

import { EstimatedValuesAlertProps } from "./EstimatedValuesAlertTypes"

export const EstimatedValuesAlert = ({
  message = "The order includes estimated values. The data will change once actual transactions are reported.",
}: EstimatedValuesAlertProps) => {
  return message ? <Alert message={l(message)} alertType="warning" /> : null
}
