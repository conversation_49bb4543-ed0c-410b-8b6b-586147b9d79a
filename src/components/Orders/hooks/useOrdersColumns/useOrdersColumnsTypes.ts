import { UseOrdersReturn } from "components/Orders/hooks/useOrders/useOrderTypes"

import { AmazonOrderExtendedViewItem } from "types"

export type UseOrdersColumnsReturn = {
  gridColumns: any
}

type UseOrdersColumnsTypeProps = {
  handleOrderDetailModalShow: UseOrdersReturn["handleOrderDetailModalShow"]
  handleEdit: (order: AmazonOrderExtendedViewItem) => void
}

export type UseOrdersColumnsType = ({
  handleOrderDetailModalShow,
}: UseOrdersColumnsTypeProps) => UseOrdersColumnsReturn
