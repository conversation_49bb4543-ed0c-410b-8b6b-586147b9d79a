import React, { useMemo } from "react"
import { useSelector } from "react-redux"
import { Box, Ellipsis, EmptyImage, IconPopover } from "@develop/fe-library"
import {
  checkIsNonEmptyNumber,
  getUrlSearchParams,
} from "@develop/fe-library/dist/utils"

import { getSelectedTableSettingsSelector } from "selectors/tableSettingsSelectors"

import AmazonCustomerAccountColumn from "components/Grid/components/amazonCustomerAccountColumn/AmazonCustomerAccountColumn"
import AsinColumn from "components/Grid/components/asinColumn/AsinColumn"
import { ConditionsColumn } from "components/Grid/components/conditionColumn/ConditionsColumn"
import { MarketplaceColumn } from "components/Grid/components/marketplaceColumn/MarketplaceColumn"
import { OrderModalInfoColumn } from "components/Grid/components/orderModalInfoColumn"
import { OrderNumberColumn } from "components/Grid/components/orderNumberColumn"
import { StatusColumn } from "components/Grid/components/statusColumn"
import TextEllipsis from "components/shared/maxTextLines/TextEllipsis"
import { ExportValue } from "components/TableGridLayout/components/ExportValue"

import { useSubscription } from "hooks"

import { buildCustomTableColumns } from "utils/buildCustomTableColumns"
import {
  convertStringDateToMoment,
  convertToLocalDateTime,
} from "utils/dateConverter"
import l from "utils/intl"
import ln from "utils/localeNumber"

import {
  COLUMN_INPUT_TYPE_DATE_RANGE,
  COLUMN_INPUT_TYPE_INPUT,
  COLUMN_INPUT_TYPE_NUMBER,
  COLUMN_INPUT_TYPE_SELECT,
  COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
} from "constants/grid"
import {
  ORDER_INFO_MODALS,
  STATUS_TAGS_MAP,
  TABLE_SETTINGS_KEY,
} from "constants/orders"

import GridProduct from "models/GridProduct"

import type { OrderType } from "types"
import type { OrdersUrlParams } from "types/UrlParams"
import type { ProductInfo } from "interfaces/GridInterfaces"

import type { UseOrdersColumnsType } from "./useOrdersColumnsTypes"

export const useOrdersColumns: UseOrdersColumnsType = ({
  handleOrderDetailModalShow,
  handleEdit,
}) => {
  const urlSearchParams = getUrlSearchParams<OrdersUrlParams>({
    locationSearch: document.location.search,
  })

  const selectedTableSettingsColumns = useSelector((state) =>
    getSelectedTableSettingsSelector(state, TABLE_SETTINGS_KEY),
  )

  const { isFreemiumActive } = useSubscription()

  const memoizedTableColumns = useMemo(
    () => {
      const buildHandleEdit = (order) => (): void => {
        handleEdit(order)
      }

      const tableColumns = [
        {
          title: "Brand",
          dataIndex: "product_brand",
          key: "product_brand",
          sorter: true,
          ellipsis: true,
          type: COLUMN_INPUT_TYPE_INPUT,
          width: 100,
          render: (value: string) => {
            return !value ? (
              l("N/A")
            ) : (
              <ExportValue>
                <TextEllipsis
                  ellipsisChars={"..."}
                  fontSize={12}
                  lines={1}
                  tooltip={value}
                >
                  {value}
                </TextEllipsis>
              </ExportValue>
            )
          },
        },
        {
          title: "Product Type",
          dataIndex: "product_type",
          key: "product_type",
          sorter: true,
          ellipsis: true,
          type: COLUMN_INPUT_TYPE_INPUT,
          width: 100,
          render: (value: string) => {
            return !value ? (
              l("N/A")
            ) : (
              <ExportValue>
                <TextEllipsis
                  ellipsisChars={"..."}
                  fontSize={12}
                  lines={1}
                  tooltip={value}
                >
                  {value}
                </TextEllipsis>
              </ExportValue>
            )
          },
        },
        {
          title: "Manufacturer",
          dataIndex: "product_manufacturer",
          key: "product_manufacturer",
          sorter: true,
          ellipsis: true,
          type: COLUMN_INPUT_TYPE_INPUT,
          width: 100,
          render: (value: string) => {
            return !value ? (
              l("N/A")
            ) : (
              <ExportValue>
                <TextEllipsis
                  ellipsisChars={"..."}
                  fontSize={12}
                  lines={1}
                  tooltip={value}
                >
                  {value}
                </TextEllipsis>
              </ExportValue>
            )
          },
        },
        {
          title: "Order number",
          dataIndex: "order_id",
          key: "order_id",
          sorter: true,
          type: COLUMN_INPUT_TYPE_INPUT,
          width: 120,
          render: (value: string, product: ProductInfo) => {
            return !value ? l("N/A") : <OrderNumberColumn product={product} />
          },
        },
        {
          title: "Order status",
          dataIndex: "order_status",
          key: "order_status",
          sorter: true,
          width: 220,
          min: 85,
          type: COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
          render: (status: string) => {
            return !status ? (
              l("N/A")
            ) : (
              <ExportValue>
                <StatusColumn status={status} tagsConfig={STATUS_TAGS_MAP} />
              </ExportValue>
            )
          },
        },
        {
          title: "Order date",
          dataIndex: "order_purchase_date",
          type: COLUMN_INPUT_TYPE_DATE_RANGE,
          key: "order_purchase_date",
          sorter: true,
          render: (date: string) => {
            return !date ? (
              l("N/A")
            ) : (
              <ExportValue>{convertToLocalDateTime(date, true)}</ExportValue>
            )
          },
        },
        {
          title: "Amazon account name",
          dataIndex: "seller_id",
          key: "seller_id",
          width: 120,
          min: 80,
          sorter: true,
          type: COLUMN_INPUT_TYPE_SELECT,
          ellipsis: true,
          className: "flex-string",
          render: (value: string, object: ProductInfo) =>
            !value ? (
              l("N/A")
            ) : (
              <ExportValue>
                <AmazonCustomerAccountColumn object={object} />
              </ExportValue>
            ),
        },
        {
          title: "Marketplace",
          dataIndex: "marketplace_id",
          key: "marketplace_id",
          sorter: true,
          width: 120,
          min: 120,
          type: COLUMN_INPUT_TYPE_SELECT,
          ellipsis: true,
          render: (marketplaceId: string) =>
            !marketplaceId ? (
              l("N/A")
            ) : (
              <ExportValue>
                {/* @ts-ignore */}
                <MarketplaceColumn marketplaceId={marketplaceId} />
              </ExportValue>
            ),
        },
        {
          title: "ASIN",
          dataIndex: "product_asin",
          key: "product_asin",
          min: 80,
          width: 100,
          sorter: true,
          type: COLUMN_INPUT_TYPE_INPUT,
          ellipsis: true,
          render: (value: string, productObject: ProductInfo) => {
            return !value ? (
              l("N/A")
            ) : (
              <AsinColumn productObject={new GridProduct(productObject)} />
            )
          },
        },
        {
          title: "SKU",
          dataIndex: "seller_sku",
          key: "seller_sku",
          sorter: true,
          width: 100,
          min: 80,
          type: COLUMN_INPUT_TYPE_INPUT,
          ellipsis: true,
          className: "flex-string",
          render: (value: string, order) => {
            if (isFreemiumActive) {
              return !value ? (
                l("N/A")
              ) : (
                <ExportValue>
                  <TextEllipsis
                    ellipsisChars={"..."}
                    fontSize={12}
                    lines={1}
                    tooltip={value}
                  >
                    {value}
                  </TextEllipsis>
                </ExportValue>
              )
            }

            return (
              <Box
                component="span"
                cursor="pointer"
                onClick={buildHandleEdit(order)}
              >
                <Ellipsis
                  typographyProps={{
                    variant: "--font-body-text-9",
                    color: "--color-text-link",
                  }}
                >
                  {value}
                </Ellipsis>
              </Box>
            )
          },
        },
        {
          title: "Image",
          dataIndex: "image",
          key: "image",
          onCell: () => ({
            style: { padding: "var(--padding-s) 0" },
          }),
          width: 70,
          min: 80,
          render: (value: string, object: ProductInfo) => {
            const product = new GridProduct(object)
            const imageUrl = product.getImageUrl()

            return (
              <Box justify="center">
                <EmptyImage height={53} url={imageUrl} width={64} />
                <ExportValue isHidden>{imageUrl}</ExportValue>
              </Box>
            )
          },
        },
        {
          title: "Title",
          dataIndex: "product_title",
          key: "product_title",
          sorter: true,
          type: COLUMN_INPUT_TYPE_INPUT,
          width: 300,
          min: 80,
          class: "left",
          onCell: () => ({
            className: "left title",
            style: { textAlign: "left" },
          }),
          render: (title: string) =>
            !title ? (
              l("N/A")
            ) : (
              <TextEllipsis
                ellipsisChars={"..."}
                fontSize={12}
                lines={3}
                tooltip={title}
              >
                <ExportValue>{title}</ExportValue>
              </TextEllipsis>
            ),
        },
        {
          title: "Product ID",
          dataIndex: "product_id",
          key: "product_id",
          sorter: true,
          type: COLUMN_INPUT_TYPE_INPUT,
          width: 100,
          render: (value: string) =>
            !value ? l("N/A") : <ExportValue>{value}</ExportValue>,
        },
        {
          title: "Condition",
          dataIndex: "product_condition",
          key: "product_condition",
          width: 120,
          sorter: true,
          type: COLUMN_INPUT_TYPE_SELECT,
          onCell: () => ({ className: "condition" }),
          render: (value: string) => {
            return !value ? (
              l("N/A")
            ) : (
              <ExportValue>
                {/* @ts-ignore */}
                <ConditionsColumn conditionId={+value} />
              </ExportValue>
            )
          },
        },
        {
          title: "Fulfillment method",
          dataIndex: "product_stock_type",
          key: "product_stock_type",
          width: 120,
          min: 80,
          sorter: true,
          type: COLUMN_INPUT_TYPE_SELECT,
          ellipsis: true,
          render: (value: string) =>
            !value ? l("N/A") : <ExportValue>{value}</ExportValue>,
        },
        {
          title: "Offer type",
          dataIndex: "offer_type",
          key: "offer_type",
          width: 100,
          min: 80,
          sorter: true,
          type: COLUMN_INPUT_TYPE_SELECT,
          ellipsis: true,
          render: (value: string) =>
            !value ? l("N/A") : <ExportValue>{value}</ExportValue>,
        },
        {
          title: "Unit price",
          dataIndex: "item_price",
          key: "item_price",
          sorter: true,
          type: COLUMN_INPUT_TYPE_NUMBER,
          width: 80,
          render: (value: number, product: ProductInfo) => {
            return value === null ? (
              l("N/A")
            ) : (
              <ExportValue>{`${ln(value, 2, {
                currency: product.currency_id,
              })}`}</ExportValue>
            )
          },
        },
        {
          title: "Units",
          dataIndex: "quantity",
          key: "quantity",
          sorter: true,
          type: COLUMN_INPUT_TYPE_NUMBER,
          width: 80,
          render: (value: number) => {
            return !checkIsNonEmptyNumber(value) ? (
              l("N/A")
            ) : (
              <ExportValue>{value}</ExportValue>
            )
          },
        },
        {
          title: "Revenue",
          dataIndex: "revenue_amount",
          key: "revenue_amount",
          sorter: true,
          type: COLUMN_INPUT_TYPE_NUMBER,
          width: 80,
          render: (value: number, product: ProductInfo) => {
            return value === null ? (
              l("N/A")
            ) : (
              <ExportValue>
                {`${ln(value, 2, { currency: product.currency_id })}`}
              </ExportValue>
            )
          },
        },
        {
          title: "Promotion",
          dataIndex: "promotion_amount",
          key: "promotion_amount",
          sorter: true,
          type: COLUMN_INPUT_TYPE_NUMBER,
          width: 80,
          render: (value: number, product: ProductInfo) => {
            return value === null ? (
              l("N/A")
            ) : (
              <ExportValue>
                {`${ln(value, 2, { currency: product.currency_id })}`}
              </ExportValue>
            )
          },
        },
        {
          title: "Amazon fees",
          dataIndex: "amazon_fees_amount",
          key: "amazon_fees_amount",
          sorter: true,
          type: COLUMN_INPUT_TYPE_NUMBER,
          width: 80,
          render: (value: number, product: ProductInfo) => {
            return value === null ? (
              l("N/A")
            ) : (
              <ExportValue>
                <OrderModalInfoColumn
                  modalName={ORDER_INFO_MODALS.amazonFees}
                  product={product}
                  value={value}
                />
              </ExportValue>
            )
          },
        },
        {
          title: "Other expenses",
          dataIndex: "expenses_amount",
          key: "expenses_amount",
          sorter: true,
          type: COLUMN_INPUT_TYPE_NUMBER,
          width: 80,
          render: (value: number, product: ProductInfo) => {
            return value === null ? (
              l("N/A")
            ) : (
              <ExportValue>
                <OrderModalInfoColumn
                  modalName={ORDER_INFO_MODALS.expenses}
                  product={product}
                  value={value}
                />
              </ExportValue>
            )
          },
        },
        {
          title: "Estimated margin",
          dataIndex: "estimated_profit_amount",
          key: "estimated_profit_amount",
          sorter: true,
          type: COLUMN_INPUT_TYPE_NUMBER,
          width: 80,
          render: (value: number, product: ProductInfo) => {
            const isNegative = value !== null && value < 0
            const amount = `${ln(value, 2, { currency: product.currency_id })}`

            if (isNegative) {
              return (
                <ExportValue color="--color-text-error">{amount}</ExportValue>
              )
            }

            return value === null ? (
              l("N/A")
            ) : (
              <ExportValue>{amount}</ExportValue>
            )
          },
        },
        {
          title: "Refunded units",
          dataIndex: "quantity_refunded",
          key: "quantity_refunded",
          sorter: true,
          type: COLUMN_INPUT_TYPE_NUMBER,
          width: 80,
          render: (value: number) => {
            return !checkIsNonEmptyNumber(value) ? (
              l("N/A")
            ) : (
              <ExportValue>{value}</ExportValue>
            )
          },
        },
      ]

      const transformedTableColumns = tableColumns.reduce(
        (result: any[], column: any) => {
          if (column?.key === "order_purchase_date") {
            column = {
              ...column,
              showTimeZoneToggle: false,
              filterType: "between",
              isClearDatesToReset: true,
              convertUtcToUserTimeZone: false,
              clearToDates: [
                convertStringDateToMoment(urlSearchParams?.from),
                convertStringDateToMoment(urlSearchParams?.to),
              ],
              isDatePickerWithFilter: false,
              disabledDatePickerDates: (currentDate: moment.Moment) => {
                return !currentDate.isBetween(
                  // @ts-expect-error
                  convertStringDateToMoment(urlSearchParams?.from),
                  convertStringDateToMoment(urlSearchParams?.to),
                  "days",
                  "[]",
                )
              },
            }
          }

          return [...result, column]
        },
        [],
      )

      return [
        {
          key: "selection_column",
          sorter: false,
          type: "action",
          dataIndex: "selection_column",
          width: 65,
          min: 65,
          onCell: () => ({
            style: { padding: "0 var(--padding-m)" },
          }),
          render: (value: boolean, order: OrderType) => {
            const { is_approximate_amounts_calculation } = order

            return (
              <Box gap="m" zIndex={1}>
                <IconPopover
                  content={l("Edit order")}
                  name="icnEdit"
                  size="--icon-size-3"
                  onClick={buildHandleEdit(order)}
                />

                <IconPopover
                  content={l("Order details")}
                  name="icnEye"
                  placement="topLeft"
                  size="--icon-size-3"
                  onClick={handleOrderDetailModalShow(order)}
                />

                {is_approximate_amounts_calculation ? (
                  <IconPopover
                    color="--color-icon-warning"
                    name="icnWarning"
                    placement="topLeft"
                    size="--icon-size-3"
                    content={l(
                      "The order includes estimated values. The data will change once actual transactions are reported.",
                    )}
                  />
                ) : null}
              </Box>
            )
          },
        },
        ...buildCustomTableColumns({
          tableColumns: transformedTableColumns,
          columns: selectedTableSettingsColumns,
        }),
      ]
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      urlSearchParams?.from,
      urlSearchParams?.to,
      selectedTableSettingsColumns,
      isFreemiumActive,
    ],
  )

  return {
    gridColumns: memoizedTableColumns,
  }
}
