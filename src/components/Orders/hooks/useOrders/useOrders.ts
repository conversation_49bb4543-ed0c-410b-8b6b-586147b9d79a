import { useCallback, useEffect, useMemo, useRef, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useHistory } from "react-router-dom"
import { Option } from "@develop/fe-library"
import {
  getObjectKeys,
  getUrlSearchParamsString,
} from "@develop/fe-library/dist/utils"
import { format } from "date-fns"

import { ordersActions } from "actions/ordersActions"
import productsCostActions from "actions/productCostActions"
import userSettingsActions from "actions/userSettingsActions"

import { productConditionsSelector } from "selectors/gridSelectors"
import {
  marketplaceGroupsSelector,
  userGroupedMarketplaceSelector,
} from "selectors/marketplaceSelectors"
import {
  getAvailableAmazonCustomerAccountsSelector,
  getIsModalVisibleSelector,
  getModalNameSelector,
  getOrderStatusesSelector,
  isOrdersDataLoadingSelector,
  ordersItemsSelector,
  ordersSelector,
} from "selectors/ordersSelectors"
import { isCustomerUserSelector } from "selectors/userSelectors"

import getMainApp from "components/MainAppProvider"
import { useInitialUrlParams } from "components/shared/Page/MainFilter/hooks"

import {
  useMarketplaceOptions,
  useMinStatsDate,
  useUrlParams,
  useUserSettings,
} from "hooks"
import { useTableColumnsVisibility } from "hooks/useTableColumnsVisibility"

import { getStringRangeInDateRange } from "utils/dateConverter"
import l from "utils/intl"

import { DATE_FNS_FORMATS } from "constants/dateTime"
import { STOCK_TYPES } from "constants/formConstants"
import { GROUP_ACCOUNT_TYPE } from "constants/groupAccount"
import {
  OFFER_TYPES_MAP,
  ORDER_INFO_MODALS,
  ORDER_STATUSES,
  PRODUCT_FIELDS,
  TABLE_SETTINGS_KEY,
} from "constants/orders"
import {
  FORM_TYPES,
  OPEN_PRODUCT_COST_DRAWER_HASH,
} from "constants/productCost"
import { STOCK_TYPES as stockTypesArray } from "constants/stockType"
import { USER_SETTINGS_KEYS } from "constants/user"

import type { ProductCostFormType } from "types/ProductCostFormType"
import { AmazonOrderRequestType } from "types/RequestParams"
import type { OrdersUrlParams } from "types/UrlParams"

const { startEditProduct } = productsCostActions

const {
  getOrders,
  updateUrlParams,
  getOrderStatuses,
  getLastUpdateOrdersDate,
  displayModal,
} = ordersActions
const { getUserSettings } = userSettingsActions
const { actions } = getMainApp()

export const useOrders = () => {
  const dispatch = useDispatch()
  const history = useHistory()

  const { urlParams } = useUrlParams<OrdersUrlParams>()

  useMinStatsDate({
    shouldLoadOnMount: true,
  })

  const isOrdersDataLoading = useSelector(isOrdersDataLoadingSelector)
  const isCustomerUser = useSelector(isCustomerUserSelector)
  const productConditions = useSelector(productConditionsSelector)

  const ordersItems = useSelector(ordersItemsSelector)
  const isModalVisible = useSelector(getIsModalVisibleSelector)
  const modalName = useSelector(getModalNameSelector)
  const marketplaceGroups = useSelector(marketplaceGroupsSelector)
  const { searchOptions, totalCount } = useSelector(ordersSelector)
  const { basAccounts } = useSelector(userGroupedMarketplaceSelector)
  const orderStatuses = useSelector(getOrderStatusesSelector)
  const availableAmazonCustomerAccounts = useSelector(
    getAvailableAmazonCustomerAccountsSelector,
  )
  const isInitiated = useRef(false)

  const { fetchUserSettings } = useUserSettings()
  const { getInitialUrlParams } = useInitialUrlParams<OrdersUrlParams>()
  const [globalUrlParams, setGlobalUrlParams] = useState<OrdersUrlParams>()

  const { getMarketplaceOptions } = useMarketplaceOptions()

  const selectFiltersOptions = useMemo(() => {
    const seller_id: string =
      urlParams.sellerId || urlParams.groupId || GROUP_ACCOUNT_TYPE.GLOBAL

    const sellerIdOptions: Option[] = availableAmazonCustomerAccounts.map(
      ({ sellerId, customerAccount }) => ({
        value: sellerId,
        label: `${customerAccount?.title} (${sellerId})`,
      }),
    )

    const marketplacesOptions = getMarketplaceOptions(seller_id).filter(
      (option) => {
        if (!urlParams.marketplaces) {
          return option
        }

        return urlParams.marketplaces.includes(option.value)
      },
    )

    const product_stock_type: Option[] = stockTypesArray.map((key) => ({
      value: key,
      label: key,
    }))

    const order_status: Option[] = orderStatuses
      ? getObjectKeys(orderStatuses).map((key) => ({
          value: key,
          label: l(ORDER_STATUSES[key]?.title || orderStatuses[key]),
        }))
      : []

    return {
      product_condition: productConditions,
      marketplace_id: marketplacesOptions,
      seller_id: sellerIdOptions,
      product_stock_type,
      order_status,
      offer_type: OFFER_TYPES_MAP,
    }
  }, [
    availableAmazonCustomerAccounts,
    getMarketplaceOptions,
    orderStatuses,
    productConditions,
    urlParams.groupId,
    urlParams.marketplaces,
    urlParams.sellerId,
  ])

  useEffect(() => {
    if (isInitiated.current) {
      return
    }

    const initGlobalUrlParams = async (): Promise<void> => {
      let userSettingsUrlParams: OrdersUrlParams = {}

      if (isCustomerUser) {
        userSettingsUrlParams = await fetchUserSettings(
          USER_SETTINGS_KEYS.mainFilterPageSettings,
        )
      }

      const nextInitialUrlParams = getInitialUrlParams(userSettingsUrlParams)

      setGlobalUrlParams(nextInitialUrlParams)

      isInitiated.current = true
    }

    initGlobalUrlParams()
  }, [fetchUserSettings, getInitialUrlParams, isCustomerUser])

  const handleOrderDetailModalShow =
    (product): (() => void) =>
    () => {
      dispatch(
        displayModal({
          modalName: ORDER_INFO_MODALS.orderDetails,
          isModalVisible: true,
          currentOrderId: product?.order_id,
          currentOrderCurrencyId: product?.currency_id,
        }),
      )
    }

  const getDataHandler = (params: OrdersUrlParams): void => {
    let order_purchase_date: any = params?.order_purchase_date

    if (params.order_purchase_date) {
      const from: string | null = params.from
        ? format(new Date(params.from), DATE_FNS_FORMATS.SERVER)
        : null

      const to: string | null = params.to
        ? format(new Date(params.to), DATE_FNS_FORMATS.SERVER)
        : null

      const range = [from, to]

      order_purchase_date =
        getStringRangeInDateRange({
          dateRangeString: params.order_purchase_date,
          range,
        }) || null
    }

    const buildRequestParams = (): AmazonOrderRequestType => {
      const mergedParams = {
        ...params,
        order_purchase_date,
      }

      // DESC: There are some redundant keys in the urlParams
      //       and we should exclude from the request params
      const { inputMode, period, customerID, segmentId, ...requestParams } =
        mergedParams

      return requestParams
    }

    dispatch(
      getOrders({
        params: {
          searchOptions: buildRequestParams(),
        },
      }),
    )
  }

  const getAdditionalData = (): void => {
    actions.getAmazonMarketplaces()

    dispatch(
      getUserSettings({ key: USER_SETTINGS_KEYS.confirmationSettings }, null),
    )

    dispatch(getLastUpdateOrdersDate())

    if (globalUrlParams) {
      dispatch(updateUrlParams(globalUrlParams))
    }

    dispatch(getOrderStatuses())
  }

  const handleFilterChange = ({ newSearchOptions: searchOptions }) =>
    searchOptions

  // GRID: RESET selected marketplace
  // HINT: this has to be refactored
  useEffect(() => {
    const shouldResetSelectedMarketplace: boolean =
      urlParams.marketplace_id && urlParams.marketplaces
        ? !urlParams.marketplaces.split(",").includes(urlParams.marketplace_id)
        : false

    if (shouldResetSelectedMarketplace) {
      const { marketplace_id, ...restParams } = urlParams
      const nextParams = getUrlSearchParamsString({
        params: restParams,
      })

      history.replace({
        ...history.location,
        search: nextParams,
      })
    }
  }, [urlParams.marketplace_id, urlParams.marketplaces])

  // random mess
  // DESC: RESET global seller_id if ... what?
  useEffect(() => {
    let shouldResetSelectedSellerId = false

    const isSellerSelected = !!urlParams.seller_id

    const isGroupSelected = !!urlParams.groupId

    const isGroupAndSeller: boolean = isGroupSelected && isSellerSelected

    if (isGroupAndSeller) {
      const isSellerInGroup = marketplaceGroups?.some((group) => {
        const isGroupEqual = group.id === parseInt(urlParams.groupId || "", 10)

        if (isGroupEqual) {
          return group.amazonCustomerAccount?.some(
            (account) => account.sellerId === urlParams.seller_id,
          )
        }

        return false
      })

      if (!isSellerInGroup) {
        shouldResetSelectedSellerId = true
      }
    }

    const isMarketplaceSellerSelected: boolean =
      !!urlParams.sellerId && urlParams.sellerId !== "all"

    const isSellersDifferent: boolean =
      isMarketplaceSellerSelected &&
      isSellerSelected &&
      urlParams.sellerId !== urlParams.seller_id

    if (isSellersDifferent) {
      shouldResetSelectedSellerId = true
    }

    if (shouldResetSelectedSellerId) {
      const { seller_id, ...restParams } = urlParams
      const nextParams = getUrlSearchParamsString({
        params: restParams,
      })

      history.replace({
        search: nextParams,
      })
    }
  }, [
    urlParams.sellerId,
    urlParams.seller_id,
    urlParams.groupId,
    marketplaceGroups,
  ])

  const isOrderDetailsModalVisible: boolean =
    modalName === ORDER_INFO_MODALS.orderDetails && isModalVisible
  const isOrderFeesModalVisible: boolean =
    modalName === ORDER_INFO_MODALS.amazonFees && isModalVisible
  const isOrderExpensesModalVisible: boolean =
    modalName === ORDER_INFO_MODALS.expenses && isModalVisible

  const customExcludeKeys = getObjectKeys(globalUrlParams)
  const isOneAccount = basAccounts?.length === 1

  useTableColumnsVisibility({
    visibleColumns: [PRODUCT_FIELDS.seller_id],
    tableSettingsKey: TABLE_SETTINGS_KEY,
    canUpdateSettings: !isOneAccount,
  })

  const successGetDataHandler = (): void => getDataHandler(searchOptions)

  const isProductCostsDrawerOpen = useMemo(() => {
    return history.location.hash.includes(OPEN_PRODUCT_COST_DRAWER_HASH)
  }, [])

  const isDrawerStateHashProcessed = useRef(false)

  const handleEdit = useCallback((order) => {
    const product = {
      adult_product: order.product_adult,
      asin: order.product_asin,
      brand: order.product_brand,
      condition: order.product_condition,
      id: order.product_id,
      isbn: order.product_isbn,
      manufacturer: order.product_manufacturer,
      parent_asin: order.product_parent_asin,
      stock_type: order.product_stock_type,
      title: order.product_title,
      type: order.product_type,
      upc: order.product_upc,
      marketplace_id: order.marketplace_id,
      sku: order.seller_sku,
      seller_id: order.seller_id,
    }

    const formType: ProductCostFormType =
      order.product_stock_type === STOCK_TYPES.fbm
        ? FORM_TYPES.shipping_cost
        : FORM_TYPES.buying_price

    dispatch(
      startEditProduct({
        product,
        order,
        formType,
      }),
    )
  }, [])

  useEffect(() => {
    if (!isProductCostsDrawerOpen) {
      isDrawerStateHashProcessed.current = true

      return
    }

    if (isOrdersDataLoading || isDrawerStateHashProcessed.current) {
      return
    }

    const order = ordersItems[0]

    if (!order) {
      return
    }

    handleEdit(order)

    isDrawerStateHashProcessed.current = true
  }, [isProductCostsDrawerOpen, isOrdersDataLoading])

  return {
    // url mess
    globalUrlParams,
    searchOptions,
    // grid
    ordersItems,
    totalCount,
    getDataHandler,
    getAdditionalData,
    // filter
    selectFiltersOptions,
    handleFilterChange,
    // modals
    isOrderDetailsModalVisible,
    isOrderFeesModalVisible,
    isOrderExpensesModalVisible,
    handleOrderDetailModalShow,
    // page specific
    productConditions,
    customExcludeKeys,
    successGetDataHandler,
    isOrdersDataLoading,
    handleEdit,
  }
}
