import React from "react"

import { useGridExport } from "components/shared/Page/MainFilter/hooks"
import { usePageName } from "components/shared/Page/MainFilter/hooks/usePageName"
import { TableWrapper } from "components/shared/TableWrapper"

import { TABLE_SETTINGS_KEY } from "constants/orders"

import { OrdersHeader } from "./components"

import { useOrders, useOrdersColumns } from "./hooks"

import {
  OrderDetailsModal,
  OrderExpensesModal,
  OrderFeesBreakdownModal,
} from "./modals"

import { ProductCostDrawer } from "../ProductCostDrawer/ProductCostDrawer"

export const Orders = () => {
  const {
    customExcludeKeys,
    selectFiltersOptions,
    getAdditionalData,
    getDataHandler,
    handleFilterChange,
    handleOrderDetailModalShow,
    globalUrlParams,
    isOrderDetailsModalVisible,
    isOrderExpensesModalVisible,
    isOrderFeesModalVisible,
    ordersItems,
    searchOptions,
    totalCount,
    successGetDataHandler,
    isOrdersDataLoading,
    handleEdit,
  } = useOrders()

  const {
    handleExportGridClose,
    handleExportGridOpen,
    handleExportWidgetOpen,
    isExportGridModalVisible,
  } = useGridExport()

  const pageName = usePageName()

  const { gridColumns } = useOrdersColumns({
    handleOrderDetailModalShow,
    handleEdit,
  })

  return (
    <>
      <OrdersHeader
        onExportGridOpen={handleExportGridOpen}
        onExportWidgetOpen={handleExportWidgetOpen}
      />

      <TableWrapper
        // @ts-ignore
        isCustomHeader
        isCustomTableColumns
        isGlobalUrlParams
        isNeedSort
        isStartFromColumnZeroIndex
        actionsColumn={false}
        additionalLogicForOnChangeFilter={handleFilterChange}
        componentTableSettings={TABLE_SETTINGS_KEY}
        customDefaultSort="-order_purchase_date"
        customExcludeKeys={customExcludeKeys}
        customTableColumns={gridColumns}
        dataSource={ordersItems}
        exportFileName={pageName}
        getAdditionalData={getAdditionalData}
        getData={getDataHandler}
        globalUrlParams={globalUrlParams}
        isExportGridModalVisible={isExportGridModalVisible}
        pageTableSettings={TABLE_SETTINGS_KEY}
        searchOptions={searchOptions}
        selectFiltersOptions={selectFiltersOptions}
        tableClassName="gridView"
        tableLoading={isOrdersDataLoading}
        totalCount={totalCount}
        withExport={false}
        onExportModalClose={handleExportGridClose}
      />

      {isOrderDetailsModalVisible ? (
        <OrderDetailsModal onEdit={handleEdit} />
      ) : null}
      {isOrderFeesModalVisible ? <OrderFeesBreakdownModal /> : null}
      {isOrderExpensesModalVisible ? <OrderExpensesModal /> : null}

      <ProductCostDrawer onClose={successGetDataHandler} />
    </>
  )
}
