import l from "utils/intl"
import ln from "utils/localeNumber"

import { GetAmount } from "./getAmountTypes"

export const getAmount: GetAmount = ({ amount = null, currencyId = null }) => {
  if (amount === null || !currencyId) {
    return {
      isValueNegative: false,
      value: l("N/A"),
    }
  }

  return {
    isValueNegative: amount < 0,
    value: ln(amount, 2, { currency: currencyId }),
  }
}
