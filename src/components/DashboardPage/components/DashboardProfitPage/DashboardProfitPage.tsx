import React from "react"
import { Box } from "@develop/fe-library"

import { KPIWidgetList } from "components/KPIWidgets"
import { Content } from "components/Layout"

import { useDashboard } from "hooks"

import { DASHBOARD_DEFAULT_VALUES_STATUS } from "constants/dashboard"

import { DashboardFilters } from "../DashboardFilters"
import { DashboardTableWidgets } from "../DashboardTableWidgets"

export const DashboardProfitPage = () => {
  const { defaultValuesStatus } = useDashboard()

  // DESC: We can modify Url Params couple of times (do we?)
  // and we want to prevent false fetches from components on the page
  if (defaultValuesStatus !== DASHBOARD_DEFAULT_VALUES_STATUS.URL_PARAMS_DONE) {
    return null
  }

  return (
    <Content>
      <Box
        flexDirection="column"
        gap="m"
        minHeight="100%"
        overflowX="hidden"
        padding="m m 0"
        mXL={{
          padding: "l l 0",
          gap: "l",
        }}
        tb={{
          maxWidth: "calc(100vw - 75px)",
        }}
      >
        <DashboardFilters />

        <KPIWidgetList />

        <DashboardTableWidgets />
      </Box>
    </Content>
  )
}
