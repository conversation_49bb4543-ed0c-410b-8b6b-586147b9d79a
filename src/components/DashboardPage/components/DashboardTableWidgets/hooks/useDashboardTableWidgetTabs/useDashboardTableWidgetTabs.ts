import { Tab } from "@develop/fe-library"

import { DASHBOARD_TABLE_WIDGETS_TABS_KEYS } from "components/DashboardPage/constants"

import { useHashTab } from "hooks"

import l from "utils/intl"

export const useDashboardTableWidgetTabs = () => {
  const { hashTab, setHashTab } = useHashTab({ tabKey: "table" })

  const productsTab: Tab = {
    key: DASHBOARD_TABLE_WIDGETS_TABS_KEYS.PRODUCTS,
    disabled: false,
    icon: "icnInbox",
    label: l("Products"),
  }

  const orderItemsTab: Tab = {
    key: DASHBOARD_TABLE_WIDGETS_TABS_KEYS.ORDERS,
    disabled: false,
    icon: "icnShoppingCart",
    label: l("Order items"),
  }

  const tabs: Tab[] = [productsTab, orderItemsTab]

  const getActiveHashTab = (): Tab | undefined => {
    return tabs.find((tab) => tab.key === hashTab) || tabs[0]
  }

  const handleChangeTab = (tab: Tab): void => {
    const tabKey = String(tab.key)
    const isSameTab: boolean = tabKey === hashTab

    if (isSameTab) {
      return
    }

    setHashTab({ tab: tabKey })
  }

  return {
    tabs,
    activeTab: getActiveHashTab()?.key,
    handleChangeTab,
  }
}
