import React from "react"

import { DASHBOARD_TABLE_WIDGETS_TABS_KEYS } from "components/DashboardPage/constants"
import { OrderItemsTable } from "components/OrderItemsTable"
import { ProductAggregatedSalesInfoTable } from "components/ProductAggregatedSalesInfoTable"

import { useUrlParams } from "hooks"

import { TabContentContainer } from "../TabContentContainer"

import { DashboardFiltersParams } from "types"

import { DashboardTableWidgetsTabsContentProps } from "./DashboardTableWidgetsTabsContentTypes"

export const DashboardTableWidgetsTabsContent = ({
  activeTab,
}: DashboardTableWidgetsTabsContentProps) => {
  const { search } = useUrlParams<DashboardFiltersParams>()

  return (
    <>
      {activeTab === DASHBOARD_TABLE_WIDGETS_TABS_KEYS.PRODUCTS ? (
        <TabContentContainer>
          <ProductAggregatedSalesInfoTable urlSearchDefaultValue={search} />
        </TabContentContainer>
      ) : null}

      {activeTab === DASHBOARD_TABLE_WIDGETS_TABS_KEYS.ORDERS ? (
        <TabContentContainer>
          <OrderItemsTable />
        </TabContentContainer>
      ) : null}
    </>
  )
}
