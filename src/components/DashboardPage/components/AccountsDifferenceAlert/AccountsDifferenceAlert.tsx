import React from "react"
import { Alert, Box } from "@develop/fe-library"

import { useAccountDifferenceAlert } from "./hooks/useAccountDifferenceAlert"

export const AccountsDifferenceAlert = () => {
  const { action, messageText, shouldShowAlert } = useAccountDifferenceAlert()

  if (!shouldShowAlert) {
    return null
  }

  return (
    <Box display="block" margin="l">
      <Alert message={messageText} hasIcon action={action} alertType="info" />
    </Box>
  )
}
