import { useMemo } from "react"

import { useUrlParams } from "hooks"

import {
  DASHBOARD_PRODUCT_FILTERS_KEYS,
  DASHBOARD_PRODUCT_KEYS,
} from "./constants"

import { DashboardFiltersParams } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

export const useDashboardFilters = () => {
  const { urlParams } = useUrlParams<DashboardFiltersParams>()

  const { isProductApplied, isProductFiltersApplied } = useMemo(() => {
    const isProductApplied: boolean = DASHBOARD_PRODUCT_KEYS.some(
      (key) => !!urlParams[key],
    )

    const isProductFiltersApplied: boolean =
      DASHBOARD_PRODUCT_FILTERS_KEYS.some((key) => !!urlParams[key]) &&
      !isProductApplied

    return {
      isProductApplied,
      isProductFiltersApplied,
    }
  }, [urlParams])

  return {
    isProductApplied,
    isProductFiltersApplied,
  }
}
