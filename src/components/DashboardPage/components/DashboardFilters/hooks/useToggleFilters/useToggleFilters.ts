import { MouseEvent } from "react"
import { useDispatch, useSelector } from "react-redux"

import segmentsActions from "actions/segmentsActions"

import { activeSidebarTabIdSelector } from "selectors/segmentsSelectors"

import { DASHBOARD_SIDEBAR_TABS } from "constants/dashboard"

const { selectSidebarTabId } = segmentsActions

export const useToggleFilters = () => {
  const dispatch = useDispatch()

  const activeSidebarTabId = useSelector(activeSidebarTabIdSelector)

  const handleToggleSidebar = (event?: MouseEvent<HTMLButtonElement>) => {
    event?.stopPropagation()

    if (activeSidebarTabId) {
      dispatch(selectSidebarTabId(DASHBOARD_SIDEBAR_TABS.CLOSED))
    } else {
      dispatch(selectSidebarTabId(DASHBOARD_SIDEBAR_TABS.FILTERS))
    }
  }

  return {
    handleToggleSidebar,
  }
}
