import { useCallback, useMemo } from "react"
import { useHistory } from "react-router-dom"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"

import { useCurrency, useUrlParams } from "hooks"

import { ASYNC_STATUSES } from "constants/async"
import { CURRENCIES_NAMES, DEFAULT_CURRENCY_CODE } from "constants/currencies"

import { DashboardFiltersParams } from "types"

import { Value } from "@develop/fe-library/dist/lib/components/Select/SelectTypes"

export const useCurrencyFilter = () => {
  const history = useHistory()

  const { currencyOptions, currenciesStatus } = useCurrency()

  const { isLoadingProductsOrFilters } = useProductsAndFilters()
  const { urlParams } = useUrlParams<DashboardFiltersParams>()

  const value = useMemo<string>(() => {
    return urlParams.currency_code || DEFAULT_CURRENCY_CODE
  }, [urlParams.currency_code])

  const prefixIcons = useMemo(
    () =>
      value
        ? [
            {
              isFlag: true,
              locale: CURRENCIES_NAMES[value]?.countryCode.toLowerCase(),
            },
          ]
        : [],
    [value],
  )

  const handleCurrencyChange = useCallback(
    (value: Value) => {
      history.push({
        ...history.location,
        search: getUrlSearchParamsString({
          params: {
            ...urlParams,
            currency_code: value,
          },
        }),
      })
    },
    [history, urlParams],
  )

  return {
    value,
    currencyOptions,
    prefixIcons,
    handleCurrencyChange,
    isDisabled:
      isLoadingProductsOrFilters ||
      currenciesStatus === ASYNC_STATUSES.PENDING ||
      !value,
  }
}
