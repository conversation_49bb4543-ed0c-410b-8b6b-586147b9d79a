import React from "react"
import { Box, Modal, Radio, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { PAGE_VIEW_MODAL_OPTIONS } from "constants/pageView"

import { useChangeViewModal } from "./hooks"

import { ChangeViewModalProps } from "./ChangeViewModalTypes"

import styles from "./changeViewModal.module.scss"

export const ChangeViewModal = ({
  isModalOpen,
  onCloseChangeViewModal,
}: ChangeViewModalProps) => {
  const { getIsChecked, handleSubmitChangeViewModal, handleSetView } =
    useChangeViewModal({
      onCloseChangeViewModal,
    })

  return (
    <Modal
      cancelButtonText={l("Cancel")}
      okButtonText={l("Change")}
      title={l("Change view")}
      visible={isModalOpen}
      onCancel={onCloseChangeViewModal}
      onClose={onCloseChangeViewModal}
      onOk={handleSubmitChangeViewModal}
    >
      <Box flexDirection="column" gap="m">
        {PAGE_VIEW_MODAL_OPTIONS.map(({ description, title, value }, index) => (
          <Box key={index} flexDirection="column" gap="s">
            <Radio
              key={value}
              isChecked={getIsChecked(value)}
              label={l(title)}
              labelVariant="--font-body-text-6"
              name="view"
              value={value}
              onChange={handleSetView}
            />
            <Box className={styles.radioDescription}>
              <Typography
                color="--color-text-second"
                variant="--font-body-text-9"
              >
                {l(description)}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Modal>
  )
}
