import { useCallback, useEffect, useState } from "react"
import { useHistory } from "react-router"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { useUrlParams } from "hooks"

import { DashboardFiltersParams } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { PageView } from "types/PageView"

import { UseChangeViewModalParams } from "./useChangeViewModalTypes"

export const useChangeViewModal = ({
  onCloseChangeViewModal,
}: UseChangeViewModalParams) => {
  const history = useHistory()
  const { urlParams } = useUrlParams<DashboardFiltersParams>()

  const [view, setView] = useState<PageView>(urlParams.view as PageView)

  const handleSetView = useCallback((value: PageView) => setView(value), [])

  const handleSubmitChangeViewModal = useCallback(() => {
    history.push({
      ...history.location,
      search: getUrlSearchParamsString({
        params: {
          ...urlParams,
          view,
        },
      }),
    })

    onCloseChangeViewModal()
  }, [history, onCloseChangeViewModal, urlParams, view])

  const getIsChecked = useCallback(
    (value: string): boolean => view === value,
    [view],
  )

  useEffect(() => {
    setView(urlParams.view as PageView)
  }, [urlParams.view])

  return {
    handleSubmitChangeViewModal,
    getIsChecked,
    handleSetView,
  }
}
