import { useCallback, useState } from "react"

export const useChangeViewFilter = () => {
  const [isModalOpen, setModalOpen] = useState(false)

  const handleOpenChangeViewModal = useCallback(() => {
    setModalOpen(true)
  }, [])

  const handleCloseChangeViewModal = useCallback(() => {
    setModalOpen(false)
  }, [])

  return {
    isModalOpen,
    handleCloseChangeViewModal,
    handleOpenChangeViewModal,
  }
}
