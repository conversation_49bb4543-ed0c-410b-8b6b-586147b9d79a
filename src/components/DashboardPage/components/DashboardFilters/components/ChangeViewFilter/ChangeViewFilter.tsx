import React from "react"
import { But<PERSON>, IconNames } from "@develop/fe-library"

import { useUrlParams } from "hooks"

import l from "utils/intl"

import { ChangeViewModal } from "./components"

import { useChangeViewFilter } from "./hooks"

import { PageViewUrlParams } from "types"

export const ChangeViewFilter = () => {
  const { urlParams } = useUrlParams<PageViewUrlParams>()
  const { handleCloseChangeViewModal, handleOpenChangeViewModal, isModalOpen } =
    useChangeViewFilter()

  const icon: IconNames =
    !urlParams.view || urlParams.view === "order"
      ? "icnShoppingCart"
      : "icnImportExport"

  return (
    <>
      <Button
        icon={icon}
        variant="secondary"
        onClick={handleOpenChangeViewModal}
      >
        {l("Change view")}
      </Button>

      <ChangeViewModal
        isModalOpen={isModalOpen}
        onCloseChangeViewModal={handleCloseChangeViewModal}
      />
    </>
  )
}
