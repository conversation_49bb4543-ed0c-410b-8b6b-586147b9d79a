import React from "react"
import { Select } from "@develop/fe-library"

import { GroupAccountOption } from "components/SegmentsSidebar/components/Filters/components/MainFilter/components"
import { getGroupAccountIconName } from "components/SegmentsSidebar/utils"

import l from "utils/intl"

import { useGroupAccountFilter } from "./hooks"

export const GroupAccountFilter = () => {
  const {
    groupAccountOptions,
    isDisabled,
    renderDropdownMenu,
    selectedGroupAccount,
    value,
    handleGroupAccountChange,
    handleTogglePrevented,
  } = useGroupAccountFilter()

  return (
    <Select
      isFullWidth
      isGlobal
      isDisabled={isDisabled}
      isTogglePrevented={handleTogglePrevented}
      label={l("Group/Account")}
      options={groupAccountOptions}
      renderDropdownMenu={renderDropdownMenu}
      // @ts-expect-error
      renderOption={GroupAccountOption}
      value={value}
      prefixIcons={[
        {
          name: getGroupAccountIconName(selectedGroupAccount?.type),
          color: "--color-icon-static",
          size: "--icon-size-3",
        },
      ]}
      onChangeValue={handleGroupAccountChange}
    />
  )
}
