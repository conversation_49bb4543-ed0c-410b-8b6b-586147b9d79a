import React from "react"
import { DateRangePicker } from "@develop/fe-library"

import { LOCALES } from "constants/locales"

import { useDateRangePickerFilter } from "./hooks"

export const DateRangePickerFilter = () => {
  const {
    fromDate,
    handleDateRangePickerSelect,
    inputMode,
    isDisabled,
    labels,
    language,
    locale,
    selected,
    toDate,
  } = useDateRangePickerFilter()

  return (
    <DateRangePicker
      isFullWidth
      isGlobal
      fromDate={fromDate}
      inputMode={inputMode}
      isClearIconVisible={false}
      isDisabled={isDisabled}
      labels={labels}
      language={LOCALES[locale]}
      locale={LOCALES[language]}
      selected={selected}
      toDate={toDate}
      onSelect={handleDateRangePickerSelect}
    />
  )
}
