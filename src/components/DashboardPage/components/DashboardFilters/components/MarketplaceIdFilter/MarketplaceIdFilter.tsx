import React from "react"
import { Select } from "@develop/fe-library"

import { MarketplacesOption } from "components/SegmentsSidebar/components/Filters/components/MainFilter/components"

import l from "utils/intl"

import { useMarketplaceIdFilter } from "./hooks"

export const MarketplaceIdFilter = () => {
  const {
    options,
    isDisabled,
    renderSelectedCount,
    value,
    prefixIcons,
    handleChangeMarketplaces,
    handleSelectMarketplaces,
    handleClearMarketplaces,
    handleTogglePrevented,
  } = useMarketplaceIdFilter()

  return (
    <Select
      hasClearIcon
      isFullWidth
      isGlobal
      isMultiSelect
      isDisabled={isDisabled}
      isTogglePrevented={handleTogglePrevented}
      label={l("Marketplace")}
      options={options}
      prefixIcons={prefixIcons}
      renderOption={MarketplacesOption}
      renderSelectedCount={renderSelectedCount}
      tagsMode="count"
      value={value}
      onChangeValue={handleChangeMarketplaces}
      onClear={handleClearMarketplaces}
      onSelectValue={handleSelectMarketplaces}
    />
  )
}
