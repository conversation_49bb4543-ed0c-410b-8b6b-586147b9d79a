import { useCallback, useEffect, useMemo, useState } from "react"
import { useHistory } from "react-router"
import {
  checkIsArray,
  getUrlSearchParamsString,
} from "@develop/fe-library/dist/utils"

import {
  useDashboardFilters,
  useToggleFilters,
} from "components/DashboardPage/components/DashboardFilters/hooks"
import { DASHBOARD_PRODUCT_RESET } from "components/DashboardPage/constants"
import { useProductsAndFilters } from "components/SegmentsSidebar/hooks"

import { useMarketplaceOptions, useUrlParams } from "hooks"

import { isArraysShallowEqual } from "utils/arrayHelpers"
import l from "utils/intl"

import { GLOBAL_GROUP_ACCOUNT_OPTION } from "constants/groupAccount"

import { DashboardFiltersParams } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { MarketplaceOption } from "types"
import { ProductsSalesInfoTableUrlParams } from "types/Tables/ProductsSalesInfoTable/ProductsSalesInfoTable"

import { TableMarketplaceIdUrlParams } from "./useMarketplaceIdFilterTypes"
import {
  IsTogglePreventedParams,
  Value,
} from "@develop/fe-library/dist/lib/components/Select/SelectTypes"

export const useMarketplaceIdFilter = () => {
  const history = useHistory()

  const { isLoadingProductsOrFilters } = useProductsAndFilters()
  const { urlParams } = useUrlParams<
    DashboardFiltersParams & ProductsSalesInfoTableUrlParams
  >()
  const { getMarketplaceOptions } = useMarketplaceOptions()
  const { isProductFiltersApplied } = useDashboardFilters()
  const { handleToggleSidebar } = useToggleFilters()

  const [selected, setSelected] = useState<string[]>([])

  const prefixIcons = useMemo(
    () => [
      {
        name: "icnFlag",
        color: "--color-icon-static",
      },
    ],
    [],
  )
  const { disabledOptions, renderSelectedCount } = useMemo(() => {
    const seller_id = urlParams.seller_id || GLOBAL_GROUP_ACCOUNT_OPTION.value
    const marketplacesOptions = getMarketplaceOptions(seller_id)
    const options = checkIsArray(marketplacesOptions)
      ? [...marketplacesOptions].sort(
          ({ country: prevCountry }, { country: nextCountry }) =>
            prevCountry.localeCompare(nextCountry),
        )
      : []

    const disabledOptions: MarketplaceOption[] = options.map((option) => ({
      ...option,
      disabled: isLoadingProductsOrFilters,
    }))

    const renderSelectedCount = (count: number): string =>
      `${count} ${l("selected")}`

    return {
      disabledOptions,
      renderSelectedCount,
    }
  }, [getMarketplaceOptions, isLoadingProductsOrFilters, urlParams.seller_id])

  const handleChangeMarketplaces = useCallback(
    (values: Value[]) => {
      setSelected(values as string[])
    },
    [setSelected],
  )

  const handleSelectMarketplaces = useCallback(
    (values: Value[]) => {
      const currentMarketplaces = urlParams.marketplace_id?.split(",") || []

      const hasMarketplacesChanges = !isArraysShallowEqual(
        values as string[],
        currentMarketplaces,
      )

      if (!hasMarketplacesChanges) {
        return
      }

      const shouldResetProduct: boolean =
        !!urlParams.productSku &&
        !!checkIsArray(values) &&
        !!urlParams.productMarketplace &&
        !values.includes(urlParams.productMarketplace)

      const products_marketplace_id: TableMarketplaceIdUrlParams =
        !values.length || values.includes(urlParams["products-marketplace_id"]!)
          ? {}
          : {
              "products-marketplace_id": undefined,
            }

      history.push({
        ...history.location,
        search: getUrlSearchParamsString({
          params: {
            ...urlParams,
            marketplace_id: (values as string[])?.join(",") || undefined,
            ...(shouldResetProduct ? DASHBOARD_PRODUCT_RESET : {}),
            ...products_marketplace_id,
          },
        }),
      })
    },
    [history, urlParams],
  )

  const handleClearMarketplaces = useCallback(() => {
    setSelected([])

    history.push({
      ...history.location,
      search: getUrlSearchParamsString({
        params: {
          ...urlParams,
          marketplace_id: undefined,
        },
      }),
    })
  }, [history, urlParams])

  const handleTogglePrevented = useCallback(
    ({ open, event }: IsTogglePreventedParams): boolean => {
      if (isProductFiltersApplied) {
        event.stopPropagation()

        handleToggleSidebar()

        return true
      }

      return false
    },
    [handleToggleSidebar, isProductFiltersApplied],
  )

  useEffect(() => {
    setSelected(urlParams.marketplace_id?.split(",") || [])
  }, [urlParams.marketplace_id])

  return {
    isDisabled: isLoadingProductsOrFilters,
    options: disabledOptions,
    renderSelectedCount,
    value: selected,
    prefixIcons,

    handleChangeMarketplaces,
    handleSelectMarketplaces,
    handleClearMarketplaces,
    handleTogglePrevented,
  }
}
