import React from "react"
import { Select } from "@develop/fe-library"

import l from "utils/intl"

import { useProductFilter } from "./hooks"

export const ProductFilter = () => {
  const {
    handleClear,
    handleIsTogglePrevented,
    handleSearchValueChange,
    handleProductSelect,
    isDisabled,
    isEmpty,
    isIdle,
    isLoading,
    options,
    label,
    renderOption,
    renderProductDropdownMenu,
    searchValue,
    selectedValue,
    handleFilterOptions,
    handleDropdownClose,
  } = useProductFilter()

  return (
    <Select
      hasClearIcon
      hasSearch
      hasSearchIcon
      isContentWidthMatchedWithTrigger
      isFullWidth
      isInitialSearchVisisble
      filterOptions={handleFilterOptions}
      hasChevronIcon={false}
      hasLabelTooltip={false}
      isDisabled={isDisabled}
      isTogglePrevented={handleIsTogglePrevented}
      label={label}
      optionHeight={60}
      options={options}
      // @ts-expect-error
      renderOption={renderOption}
      searchValue={searchValue}
      value={selectedValue}
      renderDropdownMenu={renderProductDropdownMenu({
        title: l("SKU, ASIN, Product name"),
        isIdle,
        isLoading,
        isEmpty,
      })}
      onChange={handleProductSelect}
      onClear={handleClear}
      onSearch={handleSearchValueChange}
      onVisibilityChange={handleDropdownClose}
    />
  )
}
