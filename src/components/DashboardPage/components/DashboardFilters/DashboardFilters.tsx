import React from "react"
import { Box } from "@develop/fe-library"

import {
  ChangeViewFilter,
  CurrencyFilter,
  GroupAccountFilter,
  MarketplaceIdFilter,
  ProductFilter,
  ToggleFilters,
} from "./components"

import { DateRangePickerFilter } from "./components/DateRangePickerFilter"

export const DashboardFilters = () => {
  return (
    <Box
      display="none"
      flexWrap="wrap"
      gap="m"
      tb={{
        display: "flex",
      }}
    >
      <Box
        flexGrow={1}
        flexWrap="wrap"
        gap="m"
        width="100%"
        dMD={{
          maxWidth: 620,
        }}
        tb={{
          flexWrap: "nowrap",
        }}
      >
        <Box
          width="100%"
          tb={{
            width: "33%",
          }}
        >
          <GroupAccountFilter />
        </Box>
        <Box
          width="100%"
          tb={{
            width: "33%",
          }}
        >
          <MarketplaceIdFilter />
        </Box>
        <Box
          width="100%"
          tb={{
            width: "33%",
          }}
        >
          <DateRangePickerFilter />
        </Box>
      </Box>
      <Box
        flexGrow={1}
        flexWrap="wrap"
        gap="m"
        tb={{
          flexWrap: "nowrap",
        }}
      >
        <Box width="100%">
          <ProductFilter />
        </Box>

        <Box
          flexGrow={1}
          gap="m"
          tb={{
            flexGrow: 0,
          }}
        >
          <ToggleFilters />

          <Box flexGrow={1} minWidth="80px" width="100%">
            <CurrencyFilter />
          </Box>

          <Box flexGrow={1}>
            <ChangeViewFilter />
          </Box>
        </Box>
      </Box>
    </Box>
  )
}
