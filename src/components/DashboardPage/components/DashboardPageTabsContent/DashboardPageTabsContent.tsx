import React from "react"

import { DASHBOARD_TABS_KEYS } from "constants/dashboard"

import { DashboardProfitPage } from "../DashboardProfitPage"
import { DashboardStatisticsPage } from "../DashboardStatisticsPage"

import { DashboardPageTabsContentProps } from "./DashboardPageTabsContentTypes"

export const DashboardPageTabsContent = ({
  activeTab,
}: DashboardPageTabsContentProps) => {
  return (
    <>
      {activeTab === DASHBOARD_TABS_KEYS.PROFIT ? (
        <DashboardProfitPage />
      ) : null}

      {activeTab === DASHBOARD_TABS_KEYS.STATISTICS ? (
        <DashboardStatisticsPage />
      ) : null}
    </>
  )
}
