import { useEffect } from "react"
import { useHistory } from "react-router-dom"
import { Tab } from "@develop/fe-library"
import { breakpoints } from "@develop/fe-library/dist/consts"
import { useMediaBreakpoints } from "@develop/fe-library/dist/hooks"
import {
  getUrlSearchParams,
  getUrlSearchParamsString,
} from "@develop/fe-library/dist/utils"

import {
  useCustomer,
  useDashboard,
  useDashboardDefaultUrlParams,
  useGroupAccountOptions,
  useHashTab,
  useUserSession,
} from "hooks"

import l from "utils/intl"

import {
  DASHBOARD_DEFAULT_VALUES_STATUS,
  DASHBOARD_TABS_INITIAL_URL_PARAMS,
  DASHBOARD_TABS_KEYS,
  DASHBOARD_USER_SESSION_BY_TAB,
} from "constants/dashboard"

import { DashboardFiltersParams } from "types"

import { DashboardPageTabsWithSettings } from "./useDashboardPageTabsTypes"

export const useDashboardPageTabs = () => {
  const history = useHistory()
  const { search } = history.location

  const { hashTab, setHashTab } = useHashTab({ tabKey: "tab" })

  const isDesktop = useMediaBreakpoints({
    start: breakpoints.tb,
  })

  const { customerId, prevCustomerId, isCustomerUser } = useCustomer()
  const { setDefaultValuesStatus, defaultValuesStatus } = useDashboard()
  const { buildDefaultUrlParams } = useDashboardDefaultUrlParams()
  const { updateUserSessionKey } = useUserSession()
  const { isOptionsReady } = useGroupAccountOptions()

  const profitTab: Tab = {
    key: DASHBOARD_TABS_KEYS.PROFIT as string,
    disabled: false,
    icon: "icnPieChart",
    label: isDesktop ? l("Profit") : "",
  }

  // TODO: This is for task BAS-2007
  // const insightsTab: Tab = {
  //   key: DASHBOARD_TABS_KEYS.INSIGHTS,
  //   disabled: false,
  //   icon: "icnSyncSetting",
  //   label: isDesktop ? l("Insights") : "",
  // }

  const statisticsTab: Tab = {
    key: DASHBOARD_TABS_KEYS.STATISTICS,
    disabled: false,
    icon: "icnBarChart",
    label: isDesktop ? l("Statistics") : "",
  }

  const tabsWithUrlParams: DashboardPageTabsWithSettings = {
    [DASHBOARD_TABS_KEYS.PROFIT]: {
      tab: profitTab,
      initialUrlParams:
        DASHBOARD_TABS_INITIAL_URL_PARAMS[DASHBOARD_TABS_KEYS.PROFIT],
    },
    [DASHBOARD_TABS_KEYS.STATISTICS]: {
      tab: statisticsTab,
    },
  }

  const tabs: Tab[] = [profitTab, statisticsTab]

  const getActiveHashTab = (): Tab | undefined => {
    return tabs.find((tab) => tab.key === hashTab) || tabs[0]
  }

  const handleChangeTab = (tab: Tab): void => {
    const tabKey = String(tab.key)
    const isSameTab: boolean = tabKey === hashTab

    if (isSameTab) {
      return
    }

    const nextSearch = getUrlSearchParamsString({
      params: buildDefaultUrlParams({
        userSessionKey: DASHBOARD_USER_SESSION_BY_TAB[tab.key],
        initialUrlParams: tabsWithUrlParams[tabKey]?.initialUrlParams,
      }),
    })

    setHashTab({ tab: tabKey, nextSearch })
  }

  // DESC: Build new or re-validate existing params
  // ONLY called ONCE on mount
  // But there can be sessionStorage params to restore
  useEffect(() => {
    const shouldWaitToInitialize: boolean =
      !isOptionsReady ||
      defaultValuesStatus !==
        DASHBOARD_DEFAULT_VALUES_STATUS.URL_PARAMS_INITIALIZE

    if (shouldWaitToInitialize) {
      return
    }

    const nextHashTab: string = hashTab || (profitTab.key as string)
    const currentUrlParams = getUrlSearchParams<DashboardFiltersParams>({
      locationSearch: search,
    })

    const params = buildDefaultUrlParams({
      userSessionKey: DASHBOARD_USER_SESSION_BY_TAB[nextHashTab],
      initialUrlParams: tabsWithUrlParams[nextHashTab]?.initialUrlParams,
      currentUrlParams,
    })

    const nextSearch = getUrlSearchParamsString({ params })

    setHashTab({ tab: nextHashTab, nextSearch })

    if (isCustomerUser) {
      updateUserSessionKey({
        key: DASHBOARD_USER_SESSION_BY_TAB[nextHashTab],
        search: nextSearch,
      })
    }

    setDefaultValuesStatus(
      DASHBOARD_DEFAULT_VALUES_STATUS.URL_PARAMS_READY_TO_SET,
    )
  }, [isOptionsReady, defaultValuesStatus])

  // DESC: Re-initialize values on customer change/unmount
  // For SL users ONLY
  useEffect(() => {
    if (customerId !== prevCustomerId) {
      setDefaultValuesStatus(
        DASHBOARD_DEFAULT_VALUES_STATUS.URL_PARAMS_INITIALIZE,
      )
    }

    return () => {
      setDefaultValuesStatus(
        DASHBOARD_DEFAULT_VALUES_STATUS.URL_PARAMS_INITIALIZE,
      )
    }
  }, [customerId, prevCustomerId])

  // DESC: Store and update when url params are ready
  useEffect(() => {
    if (
      defaultValuesStatus !== DASHBOARD_DEFAULT_VALUES_STATUS.URL_PARAMS_DONE
    ) {
      return
    }

    updateUserSessionKey({
      key: DASHBOARD_USER_SESSION_BY_TAB[hashTab || (profitTab.key as string)],
      search,
    })
  }, [search])

  return {
    tabs,
    activeTab: getActiveHashTab()?.key,
    handleChangeTab,
    isDesktop,
  }
}
