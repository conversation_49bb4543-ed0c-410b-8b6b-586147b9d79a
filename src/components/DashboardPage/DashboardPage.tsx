import React from "react"
import { SidebarLayout, Tabs } from "@develop/fe-library"

import { SegmentsSidebar } from "components/SegmentsSidebar"
import SimpleFooter from "components/SimpleFooter"

import { useMandatoryData } from "hooks"

import { AccountsDifferenceAlert, DashboardPageTabsContent } from "./components"

import { useDashboardPage, useDashboardPageTabs } from "./hooks"

export const DashboardPage = () => {
  useDashboardPage()

  const { isMandatoryDataReady } = useMandatoryData()
  const { activeTab, handleChangeTab, tabs, isDesktop } = useDashboardPageTabs()

  if (!isMandatoryDataReady) {
    return null
  }

  return (
    <>
      <SidebarLayout sidebar={<SegmentsSidebar />}>
        <AccountsDifferenceAlert />

        <Tabs
          activeKey={activeTab}
          isFullWidth={!isDesktop}
          items={tabs}
          onChange={handleChangeTab}
        />

        <DashboardPageTabsContent activeTab={activeTab} />
      </SidebarLayout>

      <SimpleFooter />
    </>
  )
}
