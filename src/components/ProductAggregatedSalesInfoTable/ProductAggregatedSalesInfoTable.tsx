import React from "react"

import type { ProductAggregatedSalesInfoTableProps } from "components/ProductAggregatedSalesInfoTable/ProductAggregatedSalesInfoTableTypes"

import { TableComponent } from "./components"

import { useInitialize } from "./hooks"

export const ProductAggregatedSalesInfoTable = ({
  urlSearchDefaultValue,
}: ProductAggregatedSalesInfoTableProps) => {
  const { isInitialized } = useInitialize()

  if (!isInitialized) {
    return null
  }

  return <TableComponent urlSearchDefaultValue={urlSearchDefaultValue} />
}
