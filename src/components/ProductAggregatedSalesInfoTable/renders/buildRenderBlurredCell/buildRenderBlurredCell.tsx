import React from "react"

import { BlurredWithGenerator } from "components/shared/BlurredWithGenerator"

import type { BuildRenderBlurredCell } from "./BuildRenderBlurredCellTypes"

export const buildRenderBlurredCell: BuildRenderBlurredCell =
  ({ renderValue, label, shouldBlur }) =>
  (tableEntryParams) => {
    if (shouldBlur) {
      return <BlurredWithGenerator>{label}</BlurredWithGenerator>
    }

    return <>{renderValue(tableEntryParams)}</>
  }
