import React from "react"

import { OrdersLinkCell } from "../../components"

import { ORDERS_URL_PARAMS_MAP } from "../../constants"

import type { RenderProductAggregatedSalesInfoTableCell } from "../../ProductAggregatedSalesInfoTableTypes"
import type { BuildRenderOrdersLinkCells } from "./BuildRenderOrdersLinkCellsTypes"

export const buildRenderOrdersLinkCells: BuildRenderOrdersLinkCells = ({
  dateRangeUrlParams,
}) => {
  const renderUnitsCell: RenderProductAggregatedSalesInfoTableCell = (
    tableEntryParams,
  ) => {
    return (
      <OrdersLinkCell
        tableEntryParams={tableEntryParams}
        ordersUrlParams={{
          ...ORDERS_URL_PARAMS_MAP.units,
          ...dateRangeUrlParams,
        }}
      />
    )
  }

  const renderOrdersCell: RenderProductAggregatedSalesInfoTableCell = (
    tableEntryParams,
  ) => {
    return (
      <OrdersLinkCell
        tableEntryParams={tableEntryParams}
        ordersUrlParams={{
          ...ORDERS_URL_PARAMS_MAP.orders,
          ...dateRangeUrlParams,
        }}
      />
    )
  }

  const renderRefundsCell: RenderProductAggregatedSalesInfoTableCell = (
    tableEntryParams,
  ) => {
    return (
      <OrdersLinkCell
        tableEntryParams={tableEntryParams}
        ordersUrlParams={{
          ...ORDERS_URL_PARAMS_MAP.refunds,
          ...dateRangeUrlParams,
        }}
      />
    )
  }

  return {
    renderUnitsCell,
    renderOrdersCell,
    renderRefundsCell,
  }
}
