import type {
  RenderProductAggregatedSalesInfoTableCell,
  TransactionsLinkParams,
} from "../../ProductAggregatedSalesInfoTableTypes"

type BuildRenderTransactionsLinkCellsReturn = {
  renderRevenueAmountCell: RenderProductAggregatedSalesInfoTableCell
  renderExpensesAmountWithoutFeesCell: RenderProductAggregatedSalesInfoTableCell
  renderAmazonFeesCell: RenderProductAggregatedSalesInfoTableCell
}

export type BuildRenderTransactionsLinkCells = (
  params: TransactionsLinkParams,
) => BuildRenderTransactionsLinkCellsReturn
