import React from "react"

import { TransactionsLinkCell } from "../../components"

import { TRANSACTIONS_URL_PARAMS_MAP } from "../../constants"

import type {
  RenderProductAggregatedSalesInfoTableCell,
  TransactionsLinkParams,
} from "../../ProductAggregatedSalesInfoTableTypes"
import type { BuildRenderTransactionsLinkCells } from "./BuildRenderTransactionsLinkCellsTypes"

export const buildRenderTransactionsLinkCells: BuildRenderTransactionsLinkCells =
  ({ view, minStatsDate, today, postedDateRangeParams, currencyCode }) => {
    const commonProps: TransactionsLinkParams = {
      view,
      minStatsDate,
      today,
      postedDateRangeParams,
      currencyCode,
    }

    const renderRevenueAmountCell: RenderProductAggregatedSalesInfoTableCell = (
      tableEntryParams,
    ) => {
      return (
        <TransactionsLinkCell
          {...commonProps}
          tableEntryParams={tableEntryParams}
          transactionsUrlParams={TRANSACTIONS_URL_PARAMS_MAP.revenue}
        />
      )
    }

    const renderExpensesAmountWithoutFeesCell: RenderProductAggregatedSalesInfoTableCell =
      (tableEntryParams) => {
        return (
          <TransactionsLinkCell
            {...commonProps}
            tableEntryParams={tableEntryParams}
            transactionsUrlParams={
              TRANSACTIONS_URL_PARAMS_MAP.expenses_amount_without_fees
            }
          />
        )
      }

    const renderAmazonFeesCell: RenderProductAggregatedSalesInfoTableCell = (
      tableEntryParams,
    ) => {
      return (
        <TransactionsLinkCell
          {...commonProps}
          tableEntryParams={tableEntryParams}
          transactionsUrlParams={TRANSACTIONS_URL_PARAMS_MAP.amazon_fees}
        />
      )
    }

    return {
      renderRevenueAmountCell,
      renderExpensesAmountWithoutFeesCell,
      renderAmazonFeesCell,
    }
  }
