import { SellerSkuCell } from "components/ProductAggregatedSalesInfoTable/components"
import type { ProductAggregatedSalesInfoTableEntryParams } from "components/ProductAggregatedSalesInfoTable/ProductAggregatedSalesInfoTableTypes"

export const buildRenderSellerSkuCell =
  (isFreemiumActive: boolean) =>
  (tableEntryParams: ProductAggregatedSalesInfoTableEntryParams) => {
    return (
      <SellerSkuCell
        isFreemiumActive={isFreemiumActive}
        tableEntryParams={tableEntryParams}
      />
    )
  }
