import React from "react"

import { BlurredWithGenerator } from "components/shared/BlurredWithGenerator"

import l from "utils/intl"

import { SellerIdCell } from "../../components/SellerIdCell"

import type { BuildRenderSellerIdCell } from "./BuildRenderSellerIdCellTypes"

export const buildRenderSellerIdCell: BuildRenderSellerIdCell = ({
  amazonCustomerAccounts,
  shouldBlur,
}) => {
  return (tableEntryParams) => {
    if (shouldBlur) {
      return (
        <BlurredWithGenerator>{l("Amazon account name")}</BlurredWithGenerator>
      )
    }

    return (
      <SellerIdCell
        amazonCustomerAccounts={amazonCustomerAccounts}
        tableEntryParams={tableEntryParams}
      />
    )
  }
}
