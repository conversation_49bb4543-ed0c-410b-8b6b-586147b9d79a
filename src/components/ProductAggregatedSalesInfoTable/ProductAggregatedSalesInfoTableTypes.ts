import type { ReactNode } from "react"
import type {
  // InputMode,
  // RenderTableRowActions,
  TableBaseData,
  TableEntryParams,
} from "@develop/fe-library"
import type {
  FilterPaginationValues,
  FilterSortValues,
} from "@develop/fe-library/dist/lib/types"

import type {
  DashboardFiltersParams,
  ProductAggregatedSalesInfo,
  TransactionsUrlParams,
} from "types"
import { GetProductAggregatedSalesInfosRequestParams } from "types/RequestParams/GetProductAggregatedSalesInfosRequestParams"

export type ProductAggregatedSalesInfoTableData = ProductAggregatedSalesInfo &
  TableBaseData

export type ProductAggregatedSalesInfoTableEntryParams = TableEntryParams<
  ProductAggregatedSalesInfo & TableBaseData
>

export type RenderProductAggregatedSalesInfoTableCell = (
  params: ProductAggregatedSalesInfoTableEntryParams,
) => ReactNode

export type TransactionsLinkParams = Pick<DashboardFiltersParams, "view"> & {
  minStatsDate: Date
  today: Date
  transactionsUrlParams?: TransactionsUrlParams
  postedDateRangeParams?: Pick<
    DashboardFiltersParams,
    "from" | "to" | "inputMode"
  >
  currencyCode?: string
}

export type ProductAggregatedSalesInfoTableGlobalParams = {
  currency_id?: string
  date_end?: string
  date_start?: string
  is_transaction_date_mode?: "1" | "0"
  product_adult?: "1" | "0"
  product_asin?: string
  product_brand?: string
  product_manufacturer?: string
  product_stock_type?: string
  product_type?: string
  seller_sku?: string
  offer_type?: string
  product_ean?: string
  product_isbn?: string
  product_parent_asin?: string
  product_upc?: string
  tag_id?: string
  seller_id?: string
  marketplace_id?: string
  marketplace_seller_ids?: string
}

export type ProductAggregatedSalesInfoTableFilterValues = FilterSortValues &
  FilterPaginationValues &
  Pick<
    GetProductAggregatedSalesInfosRequestParams,
    | "product_title"
    | "product_asin"
    | "seller_sku"
    | "product_brand"
    | "product_type"
    | "product_manufacturer"
    | "units"
    | "orders"
    | "refunds"
    | "revenue_amount"
    | "estimated_profit_amount"
    | "expenses_amount_without_fees"
    | "amazon_fees"
    | "margin"
    | "roi"
    | "markup"
    | "seller_id"
  > & {
    marketplace_id: Array<string>
    globalParams: ProductAggregatedSalesInfoTableGlobalParams
  }

export type ProductAggregatedSalesInfoTableProps = {
  urlSearchDefaultValue: string
}
