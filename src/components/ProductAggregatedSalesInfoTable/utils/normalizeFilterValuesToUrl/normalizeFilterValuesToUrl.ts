import {
  normalizeFilterValuesToUrl as normalizeFilterValuesToUrlDefault,
  NormalizeFilterValuesToUrlParams,
} from "@develop/fe-library/dist/utils"

import type { ProductAggregatedSalesInfoTableFilterValues } from "components/ProductAggregatedSalesInfoTable/ProductAggregatedSalesInfoTableTypes"

export const normalizeFilterValuesToUrl = ({
  values,
  dateKeys,
  dateTimeKeys,
  dateRangeKeys,
  selectWithSearchKeys,
}: NormalizeFilterValuesToUrlParams<ProductAggregatedSalesInfoTableFilterValues>): Record<
  string,
  string
> => {
  const { globalParams, ...rest } = values

  return normalizeFilterValuesToUrlDefault({
    values: rest,
    dateKeys,
    dateTimeKeys,
    dateRangeKeys,
    selectWithSearchKeys,
  })
}
