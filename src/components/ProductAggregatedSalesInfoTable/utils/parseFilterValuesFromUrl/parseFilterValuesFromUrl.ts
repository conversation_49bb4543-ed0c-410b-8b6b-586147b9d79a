import {
  parseFilterValuesFromUrl as parseFilterValuesFromUrlDefault,
  ParseFilterValuesFromUrlParams,
  removeNullAndUndefined,
} from "@develop/fe-library/dist/utils"

import type {
  ProductAggregatedSalesInfoTableFilterValues,
  ProductAggregatedSalesInfoTableGlobalParams,
} from "components/ProductAggregatedSalesInfoTable/ProductAggregatedSalesInfoTableTypes"

import { PAGE_VIEW } from "constants/pageView"

export const parseFilterValuesFromUrl = ({
  values,
  restValues = {},
  numberKeys,
  dateKeys,
  dateTimeKeys,
  dateRangeKeys,
  selectWithSearchKeys,
}: ParseFilterValuesFromUrlParams): ProductAggregatedSalesInfoTableFilterValues => {
  const globalParamsNew: ProductAggregatedSalesInfoTableGlobalParams =
    removeNullAndUndefined({
      currency_id: restValues.currency_code,
      date_end: restValues.to,
      date_start: restValues.from,
      is_transaction_date_mode:
        restValues.view === PAGE_VIEW.TRANSACTION ? "1" : "0",
      product_adult: restValues.adult_product ? "1" : undefined,
      product_asin: restValues.productASIN || restValues.asin,
      product_brand: restValues.brand,
      product_manufacturer: restValues.manufacturer,
      product_stock_type: restValues.stock_type,
      product_type: restValues.product_type,
      seller_sku: restValues.productSku || restValues.sku,
      offer_type: restValues.offer_type,
      product_ean: restValues.ean,
      product_isbn: restValues.isbn,
      product_parent_asin: restValues.parent_asin,
      product_upc: restValues.upc,
      tag_id: restValues.tags,
      seller_id: restValues.seller_id,
      marketplace_id: restValues.marketplace_id,
    })

  const parsedTargetParams =
    parseFilterValuesFromUrlDefault<ProductAggregatedSalesInfoTableFilterValues>(
      {
        values,
        numberKeys,
        dateKeys,
        dateTimeKeys,
        dateRangeKeys,
        selectWithSearchKeys,
      },
    )

  return {
    ...parsedTargetParams,
    globalParams: globalParamsNew,
  }
}
