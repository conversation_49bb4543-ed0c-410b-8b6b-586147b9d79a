import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"
import { checkIsNullOrUndefined } from "utils/validationHelper"

import { NOT_AVAILABLE } from "constants/common"

import type { AmazonCustomerAccount } from "types/Models"

import type { GetSellerIdFormattedValue } from "./GetSellerIdFormattedValueTypes"

export const getSellerIdFormattedValue: GetSellerIdFormattedValue = ({
  productAggregatedSalesInfo,
  amazonCustomerAccounts,
}) => {
  const { seller_id } = productAggregatedSalesInfo

  const hasValue =
    !checkIsNullOrUndefined(seller_id) && checkIsArray(amazonCustomerAccounts)

  if (!hasValue) {
    return l(NOT_AVAILABLE)
  }

  const account = amazonCustomerAccounts.find(
    (amazonCustomerAccount: AmazonCustomerAccount) =>
      amazonCustomerAccount.sellerId === seller_id,
  )

  if (!account) {
    return l(NOT_AVAILABLE)
  }

  const { customerAccount } = account

  return `${customerAccount?.title} ${seller_id}`
}
