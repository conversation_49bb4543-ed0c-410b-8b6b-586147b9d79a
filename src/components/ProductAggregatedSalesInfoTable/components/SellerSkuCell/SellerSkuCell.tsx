import React from "react"
import { TextLink } from "@develop/fe-library"

import { getProductCostsPageProductCostDrawerLink } from "utils/getProductCostsPageProductCostDrawerLink"
import l from "utils/intl"
import { checkIsNullOrUndefined } from "utils/validationHelper"

import { NOT_AVAILABLE } from "constants/common"

import { SellerSkuCellProps } from "components/ProductAggregatedSalesInfoTable/components/SellerSkuCell/SellerSkuCellTypes"

export const SellerSkuCell = ({
  isFreemiumActive,
  tableEntryParams,
}: SellerSkuCellProps) => {
  const { item } = tableEntryParams

  const { seller_sku, marketplace_id } = item

  if (checkIsNullOrUndefined(seller_sku)) {
    return <>{l(NOT_AVAILABLE)}</>
  }

  if (isFreemiumActive) {
    return <>{seller_sku}</>
  }

  return (
    <TextLink
      rel="noopener noreferrer"
      target="_blank"
      href={getProductCostsPageProductCostDrawerLink({
        sellerSku: seller_sku,
        marketplaceId: marketplace_id,
      })}
      typographyProps={{
        variant: "--font-body-text-9",
      }}
    >
      {seller_sku}
    </TextLink>
  )
}
