import React from "react"
import { Box, EmptyImage } from "@develop/fe-library"

import { Blurred } from "components/shared/Blurred"

import { useSubscription } from "hooks"

import { getProductAggregatedSalesInfoImageUrl } from "../../utils"

import type { ProductAggregatedSalesInfoTableEntryParams } from "../../ProductAggregatedSalesInfoTableTypes"

export const ImageCell = ({
  item,
}: ProductAggregatedSalesInfoTableEntryParams) => {
  const { isFreemiumActive: shouldBlur } = useSubscription()

  const url = getProductAggregatedSalesInfoImageUrl(item)

  return (
    <Box justify="center">
      <Blurred shouldBlur={shouldBlur}>
        <EmptyImage height={53} url={url} width={64} />
      </Blurred>
    </Box>
  )
}
