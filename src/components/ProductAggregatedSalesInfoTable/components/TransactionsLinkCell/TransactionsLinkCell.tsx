import React from "react"
import { TextLink } from "@develop/fe-library"

import l from "utils/intl"
import ln from "utils/localeNumber"
import { checkIsNullOrUndefined } from "utils/validationHelper"

import { NOT_AVAILABLE } from "constants/common"

import { getTransactionsLink } from "../../utils"

import type { TransactionsLinkCellProps } from "./TransactionsLinkCellTypes"

export const TransactionsLinkCell = ({
  tableEntryParams,
  view,
  minStatsDate,
  today,
  transactionsUrlParams,
  postedDateRangeParams,
  currencyCode,
}: TransactionsLinkCellProps) => {
  const { value, item } = tableEntryParams

  if (checkIsNullOrUndefined(value)) {
    return <>{l(NOT_AVAILABLE)}</>
  }

  const formattedValue: string = ln(value, 2, {
    currency: currencyCode,
  })

  // Remove link for BAS-2418, will be added back in future
  return <>{formattedValue}</>

  const link = getTransactionsLink({
    productAggregatedSalesInfo: item,
    view,
    minStatsDate,
    today,
    transactionsUrlParams,
    postedDateRangeParams,
  })

  return (
    <TextLink
      href={link}
      rel="noopener noreferrer"
      target="_blank"
      typographyProps={{
        variant: "--font-body-text-9",
      }}
    >
      {formattedValue}
    </TextLink>
  )
}
