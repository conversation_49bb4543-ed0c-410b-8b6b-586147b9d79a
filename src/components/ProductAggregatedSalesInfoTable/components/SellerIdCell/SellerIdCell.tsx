import React from "react"
import { Ellipsis } from "@develop/fe-library"

import { getSellerIdFormattedValue } from "../../utils/getSellerIdFormattedValue"

import type { SellerIdCellProps } from "./SellerIdCellTypes"

export const SellerIdCell = ({
  amazonCustomerAccounts,
  tableEntryParams,
}: SellerIdCellProps) => {
  const { item } = tableEntryParams

  const formattedValue = getSellerIdFormattedValue({
    productAggregatedSalesInfo: item,
    amazonCustomerAccounts,
  })

  return (
    <Ellipsis
      rows={1}
      typographyProps={{
        variant: "--font-body-text-9",
      }}
    >
      {formattedValue}
    </Ellipsis>
  )
}
