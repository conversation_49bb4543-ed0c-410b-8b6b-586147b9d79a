import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { addLocationChangeEvent } from "@develop/fe-library/dist/utils"

import { productAggregatedSalesInfoActions } from "actions/productAggregatedSalesInfoActions"

import { productAggregatedSalesInfoTableSettingsSelector } from "selectors/productAggregatedSalesInfoSelectors/productAggregatedSalesInfoSelectors"

import { checkIsArray } from "utils/arrayHelpers"

const { getTableSettings } = productAggregatedSalesInfoActions

export const useInitialize = () => {
  const dispatch = useDispatch()

  // ts-expect-error
  const { settings } = useSelector(
    productAggregatedSalesInfoTableSettingsSelector,
  )

  const hasSettings = checkIsArray(settings)

  const [isInitialized, setIsInitialized] = useState(hasSettings)

  useEffect(() => {
    addLocationChangeEvent()

    if (hasSettings) {
      return
    }

    const getAll = async () => {
      // Run all the actions in parallel to minimize the time it takes to initialize the table
      const promises = [dispatch(getTableSettings({}))]

      await Promise.all(promises)

      setIsInitialized(true)
    }

    getAll()
  }, [])

  return {
    isInitialized,
  }
}
