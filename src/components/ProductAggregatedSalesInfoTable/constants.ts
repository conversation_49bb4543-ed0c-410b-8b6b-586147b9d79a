import type { BoxProps } from "@develop/fe-library"

import type { OrdersUrlParams, TransactionsUrlParams } from "types"

const ORDER_STATUS_COMMON_KEYS: Array<string> = [
  "Pending",
  "Unshipped",
  "PartiallyShipped",
  "Shipped",
  "Unfulfillable",
  "InvoiceUnconfirmed",
  "PendingAvailability",
]

export const ORDERS_URL_PARAMS_MAP: Record<string, OrdersUrlParams> = {
  units: {
    order_status: ORDER_STATUS_COMMON_KEYS,
  },
  orders: {
    order_status: ORDER_STATUS_COMMON_KEYS,
  },
  refunds: {
    quantity_refunded: ">0",
  },
}

export const TRANSACTIONS_URL_PARAMS_MAP: Record<
  string,
  TransactionsUrlParams
> = {
  revenue_amount: {
    sales_category_depth_1: ["revenue"],
    amount: ">0",
  },
  expenses_amount_without_fees: {
    sales_category_depth_1: ["expenses"],
    amount: "<0",
  },
  amazon_fees: {
    sales_category_depth_1: ["expenses"],
    sales_category_depth_2: ["amazon_fees"],
  },
}

export const FILTER_BASE_INPUT_PROPS = {
  hasClearIcon: true,
} as const

export const TABLE_MAIN_BOX_PROPS: BoxProps = {
  height: 640,
  maxHeight: 640,
}
