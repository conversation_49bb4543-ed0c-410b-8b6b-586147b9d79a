import React from "react"
import { useSelector } from "react-redux"

import { navigationHasErrorSelector } from "selectors/mainStateSelectors"

import SimpleErrorBoundary from "components/hocs/SimpleErrorBoundary"

import styles from "./header.module.scss"

export const Header = ({ children }) => {
  const hasError = useSelector(navigationHasErrorSelector)

  return (
    <SimpleErrorBoundary hasError={hasError} showErrorMessage>
      <div className={styles.header}>{children}</div>
    </SimpleErrorBoundary>
  )
}
