import React from "react"
import { <PERSON><PERSON>, Popover } from "@develop/fe-library"
import { useHistory, useLocation } from "react-router-dom"
import { useDispatch } from "react-redux"

import marketplaceGroupsActions from "actions/marketplaceGroupsActions"

import l from "utils/intl"

import { RestrictedButtonPopover } from "components/shared/RestrictedButtonPopover"

import { EditGroupButtonProps } from "./EditGroupButtonTypes"
import { ROUTES } from "@develop/fe-library/dist/routes"

const { setEditGroupsInitialPage } = marketplaceGroupsActions

// TODO: To be removed
export const EditGroupButton = ({
  isMobile = false,
  className = "",
  managePermission = true,
  popoverMessage,
}: EditGroupButtonProps) => {
  const history = useHistory()
  const location = useLocation()
  const dispatch = useDispatch()

  const handleToGroups = () => {
    // TODO: is this OKAY solution?
    dispatch(setEditGroupsInitialPage(history.createHref(location)))

    history.push(ROUTES.BAS_ROUTES.PATH_BAS_EDIT_GROUPS)
  }

  if (isMobile) {
    const shouldRestrict: boolean = !!managePermission || !!popoverMessage

    if (shouldRestrict) {
      return (
        <RestrictedButtonPopover
          managePermission={managePermission}
          popoverMessage={popoverMessage}
          icon="icnFolder"
          variant="secondary"
          iconOnly
          className={className}
        >
          {l("Edit groups")}
        </RestrictedButtonPopover>
      )
    }

    return (
      <Popover content={l("Edit groups")}>
        <Button
          title={l("Edit groups")}
          icon="icnFolder"
          onClick={handleToGroups}
          variant="secondary"
          iconOnly
          className={className}
        />
      </Popover>
    )
  }

  return (
    <RestrictedButtonPopover
      managePermission={managePermission}
      popoverMessage={popoverMessage}
      icon="icnFolder"
      onClick={handleToGroups}
      variant="secondary"
      className={className}
    >
      {l("Edit groups")}
    </RestrictedButtonPopover>
  )
}
