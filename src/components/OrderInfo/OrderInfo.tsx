import React from "react"
import { useSelector } from "react-redux"
// @ts-ignore
import Text<PERSON>lli<PERSON> from "react-text-ellipsis/src"
import { Box, EmptyImage, Grid, Typography } from "@develop/fe-library"
import { getObjectKeys } from "@develop/fe-library/dist/utils"

import { getOrderInfoDataSelector } from "selectors/ordersSelectors"

import { AsinLink } from "components/AsinLink"
import { OrderNumberLink } from "components/OrderNumberLink"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import { OrderInfoProps, RenderValueProps } from "./OrderInfoTypes"

import styles from "./orderInfo.module.scss"

export const OrderInfo = () => {
  const {
    title,
    items = {},
    imageUrl,
    marketplace_id,
    asin,
    order_id,
  }: OrderInfoProps = useSelector(getOrderInfoDataSelector)

  const listItems = getObjectKeys(items)

  const renderValue = ({ label, value }: RenderValueProps) => {
    switch (label.toLowerCase()) {
      case "asin":
        return <AsinLink asin={asin} marketplaceId={marketplace_id} />
      case "order number":
        return (
          <OrderNumberLink marketplaceId={marketplace_id} orderId={order_id} />
        )
      default:
        return value
    }
  }

  return (
    <Grid container gapMSM="m">
      <Grid item mSM="60px">
        {!imageUrl ? null : (
          <EmptyImage
            className={styles.productImage}
            height={40}
            url={imageUrl}
            width={40}
          />
        )}
      </Grid>
      <Grid item mSM>
        <Grid container columnGap="s" flexDirection="column">
          <Grid container gap="s">
            <Grid item>
              {!title ? null : (
                <TextEllipsis ellipsisChars="..." lines={1} tooltip={title}>
                  <Typography
                    color="--color-text-main"
                    variant="--font-body-text-9"
                  >
                    {title}
                  </Typography>
                </TextEllipsis>
              )}
            </Grid>
          </Grid>
          <Grid container>
            <Grid container columnGap="s" rowGap="l">
              {!checkIsArray(listItems)
                ? null
                : listItems.map((itemKey) => {
                    const value = items[itemKey]

                    return (
                      <Grid item>
                        <Box gap="s">
                          <Typography
                            color="--color-text-second"
                            variant="--font-body-text-9"
                          >
                            {l(itemKey)}:
                          </Typography>

                          <Typography
                            color="--color-text-main"
                            variant="--font-body-text-9"
                          >
                            <TextEllipsis
                              ellipsisChars="..."
                              lines={1}
                              tooltip={value}
                            >
                              {renderValue({ label: itemKey, value })}
                            </TextEllipsis>
                          </Typography>
                        </Box>
                      </Grid>
                    )
                  })}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}
