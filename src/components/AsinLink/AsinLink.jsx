import React from "react"
import PropTypes from "prop-types"
import { useSelector } from "react-redux"

import { allAmazonMarketplacesSelector } from "selectors/mainStateSelectors"

import { ExportValue } from "components/TableGridLayout/components/ExportValue"
import Link from "components/shared/Link"

import { getAmazonProductLink } from "utils/links"

export const AsinLink = ({
  asin,
  marketplaceId,
  product_asin,
  exportValueProps = {},
}) => {
  const marketPlaces = useSelector(allAmazonMarketplacesSelector) || []

  const marketPlace = marketPlaces.find(({ id }) => id === marketplaceId)

  if (!marketPlace) {
    return null
  }

  const { sales_channel } = marketPlace
  const asinValue = asin || product_asin

  return (
    <ExportValue {...exportValueProps}>
      <Link
        internal={false}
        url={getAmazonProductLink(sales_channel, asinValue)}
        styleType="primary"
        type="span"
        variant="textSmall"
        text={asinValue?.toLocaleUpperCase()}
        target="_blank"
        rel="noopener noreferrer"
      />
    </ExportValue>
  )
}

AsinLink.propTypes = {
  asin: PropTypes.string,
  marketplaceId: PropTypes.string,
  product_asin: PropTypes.string,
  exportValueProps: PropTypes.object,
}
