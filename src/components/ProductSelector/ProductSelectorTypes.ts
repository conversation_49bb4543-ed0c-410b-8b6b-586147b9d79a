import {
  HandleSetUrlParams,
  MarketplacesUrlParams,
  ProductFiltersUrlParams,
} from "types/UrlParams"

export type ProductType = {
  id?: string
  title?: string
  sku?: string
  seller_id?: string
  marketplace_id?: string
  marketplaceTitle?: string
  country?: string
  amazonProductLink?: string
  asin?: string
  label: string
  sellerId?: string
  value: number | string
  marketplaces?: string
}

export type ProductSelectorProps = {
  onSelect: (product: ProductType | null) => void
  onSetUrlParams?: HandleSetUrlParams<
    ProductFiltersUrlParams & MarketplacesUrlParams
  >
  isResetting?: boolean
  initialSearchParams?: {
    productSku?: string
    productSeller?: string
    productMarketplace?: string
    productASIN?: string
    marketplaces?: string
  }
  dropdownOptionsMaxWidth?: string
  shouldResetMarketplacesOnSelectProduct?: boolean
  isModal?: boolean
}

export type InputValueType = string | undefined

export type GetProductRequestParams = Pick<
  ProductType,
  "sku" | "seller_id" | "marketplace_id"
> & {
  exact_sku?: string
  search?: string
  marketplaceId?: string
  marketplaceSellerIds?: string
}

export type HandleGetProductsType = (
  params: GetProductRequestParams,
  successCallback?: (products: ProductType[]) => void,
) => void

export type ProductSelectorApiResponseType = {
  products: ProductType[] | undefined
}
