import React, { useCallback, useEffect, useMemo, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useLocation } from "react-router-dom"
import { Box, Select } from "@develop/fe-library"
import { getUrlSearchParams } from "@develop/fe-library/dist/utils"
import debounce from "lodash/debounce"

import { salesHistoryActions } from "actions/salesHistoryActions"

import { marketplaceGroupsSelector } from "selectors/marketplaceSelectors"

import { checkIsArray } from "utils/arrayHelpers"
import { formatSellerIds } from "utils/formatSellerIdsQuery"
import l from "utils/intl"
import { checkIsFunction } from "utils/validationHelper"

import { SelectItem } from "./components/SelectItem"

import { MarketplacesUrlParams, ProductFiltersUrlParams } from "types/UrlParams"

import {
  HandleGetProductsType,
  InputValueType,
  ProductSelectorApiResponseType,
  ProductSelectorProps,
  ProductType,
} from "./ProductSelectorTypes"

const { getProducts } = salesHistoryActions

export const ProductSelector = ({
  onSelect,
  onSetUrlParams,
  isResetting = false,
  initialSearchParams = {},
  dropdownOptionsMaxWidth = "",
  shouldResetMarketplacesOnSelectProduct = false,
  isModal = false,
  ...selectProps
}: ProductSelectorProps) => {
  const dispatch = useDispatch()
  const location = useLocation()

  const [options, setOptions] = useState<ProductType[]>([])
  const [selectedValue, setSelectedValue] = useState<InputValueType>("")
  const [selectedProduct, setSelectedProduct] = useState<ProductType | null>(
    null,
  )
  const [searchValue, setSearchValue] = useState<InputValueType>("")

  const groups = useSelector(marketplaceGroupsSelector)

  const urlParams = useMemo(
    () => ({
      ...getUrlSearchParams({
        locationSearch: location.search,
      }),
      ...initialSearchParams,
    }),
    [initialSearchParams, location.search],
  )

  const { productSku, productSeller, productMarketplace, marketplaces } =
    urlParams

  const marketplacesList = useMemo(() => marketplaces, [marketplaces])
  const marketplaceSellerIds = useMemo(
    () => formatSellerIds(urlParams, groups),
    [urlParams, groups],
  )

  const fetchProducts: HandleGetProductsType = useCallback(
    (params, successCallback = () => {}) => {
      dispatch(
        getProducts(params, ({ products }: ProductSelectorApiResponseType) => {
          const formattedProducts = checkIsArray(products)
            ? products.map((product: any) => ({
                ...product,
                label: product.title || "",
                value: product.id || "",
              }))
            : []

          setSelectedValue("")
          setOptions(formattedProducts)

          successCallback(formattedProducts)
        }),
      )
    },
    [dispatch],
  )

  const handleProductSelect = (product: ProductType): void => {
    if (!product) {
      return
    }

    const params = getUrlSearchParams<MarketplacesUrlParams>({
      locationSearch: location.search,
    })

    const marketplaces = params.marketplaces?.split(",") || []
    const shouldResetMarketplaces: boolean =
      shouldResetMarketplacesOnSelectProduct &&
      !!marketplaces.length &&
      !marketplaces.every(
        (marketplace) => marketplace === product.marketplace_id,
      )

    // HINT: Typescript want onSetUrlParams to check inline
    if (shouldResetMarketplaces && checkIsFunction(onSetUrlParams)) {
      onSetUrlParams({ marketplaces: undefined })
    }

    onSelect(product)

    setOptions([product])
    setSelectedValue(product.id)
    setSelectedProduct(product)
  }

  const searchProduct = useMemo(
    () =>
      debounce((value: InputValueType): void => {
        if (!value) {
          return
        }

        const isAllowToSearch = value.trim().length >= 3

        const marketplaceSellerIdsParam = checkIsArray(marketplaceSellerIds)
          ? { marketplaceSellerIds: JSON.stringify(marketplaceSellerIds) }
          : {}

        if (isAllowToSearch) {
          fetchProducts({
            search: value,
            marketplace_id: marketplacesList,
            ...marketplaceSellerIdsParam,
          })
        }
      }, 500),
    [fetchProducts, marketplaceSellerIds, marketplacesList],
  )

  const handleProductSkuClick = (product: ProductType): void => {
    setSearchValue(product?.sku)
  }

  const handleClear = useCallback((): void => {
    setSearchValue("")
    setSelectedValue("")
    setSelectedProduct(null)
    setOptions([])
    onSelect(null)
  }, [])

  const setInitialValue = (products: ProductType[]): void => {
    const initialItem = products.find(({ sku, seller_id, marketplace_id }) => {
      return (
        sku === productSku &&
        seller_id === productSeller &&
        marketplace_id === productMarketplace
      )
    })

    // const hasInitialItem: boolean = !!initialItem && !inputValue
    if (initialItem && !selectedValue) {
      setSelectedValue(initialItem.id)
      setSelectedProduct(initialItem)
    }
  }

  const handleFilterOptions = (): boolean => {
    return true
  }

  const handleSelectProduct = (products: ProductType[]): void => {
    const params = getUrlSearchParams<ProductFiltersUrlParams>({
      locationSearch: location.search,
    })

    const initialItem =
      products?.find(({ sku, seller_id, marketplace_id }) => {
        return (
          sku === params.productSku &&
          seller_id === params.productSeller &&
          marketplace_id === params.productMarketplace
        )
      }) || null

    onSelect(initialItem)
  }

  const handleTogglePrevented = ({ open }: { open: boolean }): boolean => {
    return open
  }

  const handleSearchValueChange = (value: InputValueType): void => {
    setSearchValue(value)
  }

  useEffect(() => {
    if (isResetting) {
      handleClear()
    }
  }, [handleClear, isResetting])

  // DESC: Initial params for set default value
  useEffect(() => {
    const hasProductUrlParams =
      productSku && productSeller && productMarketplace

    const hasInitialProductParams =
      initialSearchParams && initialSearchParams?.productSku
    const shouldGetProducts = hasProductUrlParams || hasInitialProductParams

    if (shouldGetProducts) {
      fetchProducts(
        {
          exact_sku: productSku,
          seller_id: productSeller,
          marketplace_id: productMarketplace,
        },
        (products) => {
          setInitialValue(products)

          if (hasInitialProductParams) {
            handleSelectProduct(products)
          }
        },
      )
    }
  }, [])

  // DESC: Initiate selectedProduct
  useEffect(() => {
    const hasProducts = !!options.length

    if (hasProducts) {
      const initialItem = options.find(({ sku, seller_id, marketplace_id }) => {
        return (
          sku === productSku &&
          seller_id === productSeller &&
          marketplace_id === productMarketplace
        )
      })

      const hasInitialItem = initialItem && !selectedValue

      if (hasInitialItem) {
        setSelectedValue(initialItem.id)
        setSelectedProduct(initialItem)
      }
    }
  }, [selectedValue, options, productMarketplace, productSeller, productSku])

  // DESC: Update searchValue
  useEffect(() => {
    if (searchValue) {
      searchProduct(searchValue)

      return
    }

    const hasInputValueAndSelectedProduct = selectedValue && selectedProduct

    if (hasInputValueAndSelectedProduct) {
      setOptions([selectedProduct])

      return
    }

    setOptions([])
  }, [searchValue])

  // DESC: Clear selectedProduct if any of the product url params missed
  useEffect(() => {
    const hasProductInUrlParams: boolean = !!(
      productSku &&
      productSeller &&
      productMarketplace
    )
    const shouldResetProduct: boolean =
      !hasProductInUrlParams && !!selectedProduct && !isModal

    if (shouldResetProduct) {
      handleClear()
    }
  }, [
    productSku,
    productSeller,
    productMarketplace,
    selectedProduct,
    handleClear,
  ])

  const selectLabel = selectedValue
    ? l("Product")
    : l("Search by (SKU, ASIN, Product name)")

  return (
    <Select
      hasClearIcon
      hasSearch
      isFullWidth
      isInitialSearchVisisble
      filterOptions={handleFilterOptions}
      hasLabelTooltip={false}
      hasSearchIcon={false}
      isTogglePrevented={handleTogglePrevented}
      label={selectLabel}
      optionHeight={60}
      options={options}
      prefixIcons={[{ name: "icnSearch" }]}
      searchValue={searchValue}
      value={selectedValue}
      renderDropdownMenu={({ menu }) => (
        <Box display="block" maxWidth={dropdownOptionsMaxWidth} minWidth="100%">
          {menu}
        </Box>
      )}
      renderOption={({ option }: { option: ProductType }) => (
        <SelectItem
          option={option}
          searchValue={searchValue}
          onSkuSelect={handleProductSkuClick}
        />
      )}
      onChange={handleProductSelect}
      onClear={handleClear}
      onSearch={handleSearchValueChange}
      {...selectProps}
    />
  )
}
