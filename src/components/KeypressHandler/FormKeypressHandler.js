import React, { useCallback, useState } from 'react'
import PropTypes from 'prop-types'
import {
  touchableClassNames,
  keypressTagNames,
  dropdownTouchableClassNames,
} from 'utils/formConstants'
import KeypressHandler from './KeypressHandlerRedux'

export const FormKeypressHandler = ({
  onEscape,
  onEnter,
  onKeypress: propOnKeypress,
  onSubmit,
  onClose,
  priority,
}) => {
  const [selectPressCounter, setSelectPressCounter] = useState(0)
  const onKeypress = useCallback(
    event => {
      const {
        target: { tagName, classList, name },
      } = event
      propOnKeypress && propOnKeypress(event)

      switch (event.key) {
        case 'Tab':
          setSelectPressCounter(0)
          break
        case 'Escape':
          onClose && onClose()
          event.preventDefault()
          event.stopPropagation()
          break
        case 'Enter': {
          if (name === 'Close') {
            onClose && onClose()
            return
          }
          if (
            dropdownTouchableClassNames.some(name => classList.contains(name))
          ) {
            if (selectPressCounter === 1) {
              event.target.blur()
            }
            if (selectPressCounter === 2) {
              onSubmit && onSubmit()
              setSelectPressCounter(0)
            }
            setSelectPressCounter(selectPressCounter + 1)
            return
          }
          if (
            keypressTagNames.includes(tagName) ||
            touchableClassNames.some(name => classList?.contains(name))
          )
            return
          onSubmit && onSubmit()
          event.preventDefault()
          event.stopPropagation()
          return
        }
        default:
          return
      }
    },
    [selectPressCounter, onClose, onSubmit, propOnKeypress]
  )
  return (
    <>
      <KeypressHandler
        onKeypress={onKeypress}
        onEnter={onEnter}
        onEscape={onEscape}
        priority={priority}
      />
    </>
  )
}

FormKeypressHandler.propTypes = {
  priority: PropTypes.number,
  onEscape: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
  onEnter: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
  onKeypress: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
  onSubmit: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
  onClose: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
}

export default FormKeypressHandler
