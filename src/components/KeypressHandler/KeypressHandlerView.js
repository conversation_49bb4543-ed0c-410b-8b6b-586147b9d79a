import React, { useCallback, Component } from 'react'
import useEventListener from 'hooks/useEventListener'

export const Keypress = ({
  onEscape,
  onEnter,
  onArrowUp,
  onArrowDown,
  onKeypress,
}) => {
  const keypressHandler = useCallback(
    event => {
      onKeypress && onKeypress(event)
      switch (event.key) {
        case 'Escape':
          onEscape && onEscape(event)
          break
        case 'Enter':
          onEnter && onEnter(event)
          break
        case 'ArrowUp':
          onArrowUp && onArrowUp(event)
          break
        case 'ArrowDown':
          onArrowDown && onArrowDown(event)
          break
        default:
          break
      }
    },
    [onEnter, onEscape, onKeypress, onArrowUp, onArrowDown]
  )
  useEventListener('keydown', keypressHandler, true)
  return <></>
}

class KeypressHandlerView extends Component {
  state = {
    priority: 1,
  }

  componentDidMount() {
    const { priorityArray, priority: propPriority, setPriority } = this.props
    const priority =
      propPriority ||
      Math.max(Math.max(...priorityArray, 0) + 1, this.state.priority)
    this.setState({ priority })
    setPriority(priority, true)
  }
  componentWillUnmount() {
    const { setPriority } = this.props
    setPriority(this.state.priority, false)
  }
  render() {
    const {
      children,
      onEscape,
      onEnter,
      onKeypress,
      onArrowUp,
      onArrowDown,
      priorityArray,
    } = this.props
    return (
      <>
        {Math.max(...priorityArray) === this.state.priority && (
          <Keypress
            onEscape={onEscape}
            onEnter={onEnter}
            onArrowUp={onArrowUp}
            onArrowDown={onArrowDown}
            onKeypress={onKeypress}
          />
        )}
        {children}
      </>
    )
  }
}

export default KeypressHandlerView
