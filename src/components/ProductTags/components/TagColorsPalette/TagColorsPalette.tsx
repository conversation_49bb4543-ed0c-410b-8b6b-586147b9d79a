import React from "react"
import {
  <PERSON>ert,
  Box,
  Button,
  ColorPicker,
  DeletableColor,
  Grid,
  Modal,
} from "@develop/fe-library"

import { RestrictedPrimaryButton } from "components/shared/RestrictedButtons"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "constants/permissions"

import { useTagColorsPalette } from "./hooks"

import { TagColorsPaletteProps } from "./TagColorsPaletteTypes"

export const TagColorsPalette = ({
  onChange = () => {},
  isColorDeletable = true,
  initialColor = null,
}: TagColorsPaletteProps) => {
  const {
    productTagColors,
    selectedTagColor,
    handleCheckboxChange,
    isOneColor,
    handleDeleteColor,
    handleColorPickerModalVisible,
    isColorPickerModalVisible,
    isColorAddDisabled,
    handleColorAdd,
    handleChangeColor,
    previewColor,
  } = useTagColorsPalette({ onChange, initialColor })

  if (!checkIsArray(productTagColors)) {
    return null
  }

  return (
    <>
      <Grid container gap="m">
        {productTagColors.map(({ id, tag_color }) => (
          <Grid key={id} item>
            <DeletableColor
              checked={selectedTagColor === tag_color}
              color={tag_color}
              isDeletable={isColorDeletable}
              isDisabled={isOneColor}
              isHiddenAfterDelete={false}
              tooltip={
                isOneColor ? l("Last color cannot be deleted") : l("Delete")
              }
              onCheckboxChange={handleCheckboxChange({ tag_color })}
              onDelete={handleDeleteColor({ id, tag_color })}
            />
          </Grid>
        ))}
        <Grid item>
          <Button
            iconOnly
            icon="icnPlus"
            variant="secondary"
            onClick={handleColorPickerModalVisible(true)}
          />
        </Grid>
      </Grid>

      <Modal
        isWithoutBodyPadding
        title={l("Select tag color")}
        visible={isColorPickerModalVisible}
        width="--modal-size-xs"
        footer={
          <Box gap="m" width="100%">
            <Button
              fullWidth
              variant="secondary"
              onClick={handleColorPickerModalVisible(false)}
            >
              {l("Cancel")}
            </Button>
            <RestrictedPrimaryButton
              fullWidth
              disabled={isColorAddDisabled}
              managePermission={permissionKeys.basMyProductsView}
              popoverMessage={restrictPopoverMessages.view}
              onClick={handleColorAdd}
            >
              {l("Apply")}
            </RestrictedPrimaryButton>
          </Box>
        }
        onClose={handleColorPickerModalVisible(false)}
      >
        <Box flexDirection="column" gap="m" padding="m">
          <ColorPicker
            previewColor={previewColor}
            onChange={handleChangeColor}
          />

          {isColorAddDisabled ? (
            <Alert
              alertType="warning"
              message={l("Tag with this colour already exist")}
            />
          ) : null}
        </Box>
      </Modal>
    </>
  )
}
