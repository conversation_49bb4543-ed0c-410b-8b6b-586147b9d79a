import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"

import gridActions from "actions/gridActions"
import productTagsActions from "actions/productTagsActions"

import { gridTotalCountSelector } from "selectors/gridSelectors"
import {
  isDeleteProductTagModalVisibleSelector,
  productTagInitialValuesSelector,
} from "selectors/productTagsSelectors"

import { useUrlParams } from "hooks"

const { displayDeleteProductTagModal, deleteProductTag, getProductTags } =
  productTagsActions
// @ts-expect-error
const { getProducts } = gridActions

export const useDeleteTagModal = () => {
  const dispatch = useDispatch()

  const [isLoading, setIsLoading] = useState(true)

  const initialValues = useSelector(productTagInitialValuesSelector)
  const isDeleteProductTagModalVisible = useSelector(
    isDeleteProductTagModalVisibleSelector,
  )
  const productTotalCount = useSelector(gridTotalCountSelector)

  const { urlParams } = useUrlParams()

  const hasProducts = productTotalCount > 0
  const hasProductsWithTags: boolean = !isLoading && hasProducts

  const handleModalCancel = (): void => {
    dispatch(displayDeleteProductTagModal(false, {}))
    setIsLoading(true)
  }

  const handleModalOk = (): void => {
    dispatch(
      deleteProductTag({
        id: initialValues?.id,
        successCallback: () => {
          dispatch(getProductTags(urlParams))
          handleModalCancel()
        },
      }),
    )
  }

  useEffect(() => {
    const hasInitialValue = initialValues?.id && isDeleteProductTagModalVisible

    if (hasInitialValue) {
      /* Copied from Repricer, will refactoring later */
      dispatch(getProducts({ tag_id: initialValues.id })).then(() => {
        setIsLoading(false)
      })
    }
  }, [isDeleteProductTagModalVisible, initialValues?.id, dispatch])

  return {
    hasProductsWithTags,
    initialValues,
    productTotalCount,
    handleModalCancel,
    handleModalOk,
    isLoading,
    isDeleteProductTagModalVisible,
  }
}
