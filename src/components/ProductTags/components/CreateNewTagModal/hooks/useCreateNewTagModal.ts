import { useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"

import productTagsActions from "actions/productTagsActions"

import { productTagColorsSelector } from "selectors/productTagColorsSelectors"
import { isCreateProductTagModalVisibleSelector } from "selectors/productTagsSelectors"

import { ErrorTypes } from "components/ProductTags/ProductTagsTypes"

import { checkIsArray } from "utils/arrayHelpers"
import { generateRandomColor } from "utils/colors"

import { Tag, TagColor } from "types"

import {
  FormFieldsKeysTypes,
  UseCreateNewTagModalProps,
} from "../CreateNewTagModalTypes"

const { getProductTags, createProductTag, displayCreateProductTagModal } =
  productTagsActions

export const useCreateNewTagModal = ({
  onSuccess = () => {},
}: UseCreateNewTagModalProps) => {
  const dispatch = useDispatch()
  const form = useForm<Tag>({
    defaultValues: {
      title: "",
    },
  })

  const isCreateProductTagModalVisible = useSelector(
    isCreateProductTagModalVisibleSelector,
  )
  const productTagColors: TagColor[] = useSelector(productTagColorsSelector)

  const { handleSubmit, setError, reset } = form

  const errorsHandler = (errors: ErrorTypes): void => {
    if (!errors) {
      return
    }

    if (checkIsArray(errors)) {
      errors.forEach(({ field, message }) => {
        setError(field as FormFieldsKeysTypes, { type: "custom", message })
      })
    }
  }

  const submitFormHandler = (data: Tag): void => {
    const randomColor = generateRandomColor(productTagColors)

    dispatch(
      createProductTag({
        payload: { ...data, color: randomColor },
        successCallback: () => {
          onSuccess(data)
          reset()
          dispatch(displayCreateProductTagModal(false, null))
          dispatch(getProductTags({}))
        },
        failureCallback: errorsHandler,
      }),
    )
  }

  const toggleModalHandler = (visible: boolean) => (): void => {
    dispatch(displayCreateProductTagModal(visible, null))
  }

  return {
    isCreateProductTagModalVisible,
    toggleModalHandler,
    handleSubmit,
    submitFormHandler,
    form,
  }
}
