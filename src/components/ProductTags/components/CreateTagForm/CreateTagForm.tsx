import React from "react"
import { Box, FormItems, Typography } from "@develop/fe-library"

import { RestrictedPrimaryButton } from "components/shared/RestrictedButtons"

import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "constants/permissions"

import { useCreateTagForm } from "./hooks"

import { TagColorsPalette } from "../TagColorsPalette"

export const CreateTagForm = () => {
  const {
    setValueHandler,
    handleSubmit,
    handleFormSubmit,
    isSubmitting,
    form,
    watchColorField,
  } = useCreateTagForm()

  const items = [
    {
      type: "component",
      component: (
        <Typography variant="--font-headline-5">{l("Create tag")}</Typography>
      ),
      gridItemProps: { always: 12 },
    },
    {
      name: "title",
      type: "text",
      inputProps: {
        label: l("Title"),
      },
      gridItemProps: { always: 12 },
    },
    {
      name: "color",
      type: "component",
      component: (
        <TagColorsPalette
          initialColor={watchColorField}
          onChange={setValueHandler("color")}
        />
      ),
      gridItemProps: { always: 12 },
    },
    {
      type: "component",
      component: (
        <RestrictedPrimaryButton
          fullWidth
          disabled={isSubmitting}
          icon="icnPlus"
          managePermission={permissionKeys.basMyProductsManage}
          popoverMessage={restrictPopoverMessages.alter}
          type="submit"
          onClick={handleSubmit(handleFormSubmit)}
        >
          {l("Create tag")}
        </RestrictedPrimaryButton>
      ),
      gridItemProps: { mSM: 12, mLG: "auto" },
    },
  ]

  return (
    <Box display="block" width="100%">
      <FormItems
        form={form}
        items={items}
        gridContainerProps={{
          gapMSM: "m",
          justify: "end",
          gapTb: "l",
        }}
      />
    </Box>
  )
}
