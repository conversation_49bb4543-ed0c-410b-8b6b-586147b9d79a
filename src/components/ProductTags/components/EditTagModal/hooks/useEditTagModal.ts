import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"

// @ts-ignore
import gridActions from "actions/gridActions"
import productTagsActions from "actions/productTagsActions"

import { gridTotalCountSelector } from "selectors/gridSelectors"
import {
  isUpdateProductTagModalVisibleSelector,
  productTagInitialValuesSelector,
} from "selectors/productTagsSelectors"

import { ErrorTypes } from "components/ProductTags/ProductTagsTypes"

import { useUrlParams } from "hooks"

import { checkIsArray } from "utils/arrayHelpers"

import { Tag } from "types"

import { FormFieldsKeysTypes } from "../EditTagModalTypes"

const { displayUpdateEditProductTagModal, updateProductTag, getProductTags } =
  productTagsActions
// @ts-expect-error
const { getProducts } = gridActions

export const useEditTagModal = () => {
  const dispatch = useDispatch()

  const [isLoading, setIsLoading] = useState(true)

  const form = useForm<Tag>({
    defaultValues: {
      title: "",
    },
  })

  const {
    reset,
    setValue,
    formState: { isSubmitting },
    handleSubmit,
    setError,
    getValues,
  } = form

  const initialColor = getValues("color")

  const isEditTagModalVisible = useSelector(
    isUpdateProductTagModalVisibleSelector,
  )
  const initialValues = useSelector(productTagInitialValuesSelector)
  const productTotalCount = useSelector(gridTotalCountSelector)

  const { urlParams } = useUrlParams()

  const hasProducts = productTotalCount > 0
  const hasProductsWithTags: boolean = !isLoading && hasProducts

  const handleModalVisibleChange = (isVisible: boolean) => (): void => {
    dispatch(displayUpdateEditProductTagModal(isVisible, {}))
  }

  const handleSetValue =
    (name: FormFieldsKeysTypes) =>
    (value: string): void => {
      setValue(name, value)
    }

  const handleErrors = (errors: ErrorTypes): void => {
    if (!errors) {
      return
    }

    if (checkIsArray(errors)) {
      errors.forEach(({ field, message }) => {
        setError(field as FormFieldsKeysTypes, { type: "custom", message })
      })
    }
  }

  const handleModalSubmit = (data: Tag): void => {
    const { color: selectedColor, ...restSubmitData } = data

    dispatch(
      updateProductTag({
        payload: {
          ...restSubmitData,
          color: selectedColor,
        },
        successCallback: () => {
          // TODO: Should remove next action in Repricer and here too. It doesn't work
          handleModalVisibleChange(false)
          dispatch(getProductTags(urlParams))
        },
        failureCallback: handleErrors,
      }),
    )
  }

  useEffect(() => {
    const hasInitialValues: boolean = initialValues?.id && isEditTagModalVisible

    if (hasInitialValues) {
      reset(initialValues)
      /* Copied from Repricer, will refactoring later */
      dispatch(getProducts({ tag_id: initialValues.id })).then(() => {
        setIsLoading(false)
      })
    }
  }, [dispatch, initialValues, initialValues?.id, isEditTagModalVisible, reset])

  useEffect(() => {
    if (isEditTagModalVisible) {
      setIsLoading(true)
    }
  }, [isEditTagModalVisible])

  return {
    handleSetValue,
    initialColor,
    handleModalVisibleChange,
    handleSubmit,
    handleModalSubmit,
    isSubmitting,
    isLoading,
    hasProductsWithTags,
    initialValues,
    productTotalCount,
    form,
    isEditTagModalVisible,
  }
}
