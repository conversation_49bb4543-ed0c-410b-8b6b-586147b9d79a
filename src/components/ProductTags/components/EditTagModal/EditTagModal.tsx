import React from "react"
import { <PERSON>, FormItems, <PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON> } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import Link from "components/shared/Link"

import l from "utils/intl"

import { useEditTagModal } from "./hooks"

import { TagColorsPalette } from "../TagColorsPalette"

export const EditTagModal = () => {
  const {
    handleSetValue,
    initialColor,
    handleModalVisibleChange,
    handleSubmit,
    handleModalSubmit,
    isSubmitting,
    isLoading,
    hasProductsWithTags,
    initialValues,
    productTotalCount,
    form,
    isEditTagModalVisible,
  } = useEditTagModal()

  if (!isEditTagModalVisible) {
    return null
  }

  const items = [
    {
      name: "title",
      type: "text",
      inputProps: {
        label: "Title",
      },
      gridItemProps: { always: 12 },
    },
    {
      type: "component",
      component: (
        <TagColorsPalette
          initialColor={initialColor}
          isColorDeletable={false}
          onChange={handleSetValue("color")}
        />
      ),
      gridItemProps: { always: 12 },
    },
  ]

  const productTagsText = hasProductsWithTags ? (
    <Box marginBottom="m" tb={{ marginBottom: "l" }} width="100%">
      <Typography color="--color-text-second" variant="--font-body-text-7">
        {l("This tag is assigned to {following_products} products.", {
          following_products: (
            <Link
              internal
              styleType="primary"
              target="_blank"
              text={productTotalCount}
              type="span"
              variant="text"
              url={`${
                ROUTES.BAS_ROUTES.PATH_BAS_PRODUCT_COSTS
              }${getUrlSearchParamsString({
                params: { tag_id: initialValues?.id },
              })}`}
            />
          ),
        })}
      </Typography>
    </Box>
  ) : null

  return (
    <Modal
      visible={isEditTagModalVisible}
      cancelButtonText={l("Cancel")}
      okButtonText={l("Save")}
      title={l("Edit tag")}
      okButtonProps={{
        disabled: isSubmitting,
      }}
      onCancel={handleModalVisibleChange(false)}
      onClose={handleModalVisibleChange(false)}
      onOk={handleSubmit(handleModalSubmit)}
    >
      {isLoading ? (
        <Box justify="center">
          <Spinner size="lg" />
        </Box>
      ) : (
        <>
          {productTagsText}

          <FormItems
            form={form}
            items={items}
            gridContainerProps={{
              gapMSM: "m",
              justify: "end",
              gapTb: "l",
            }}
          />
        </>
      )}
    </Modal>
  )
}
