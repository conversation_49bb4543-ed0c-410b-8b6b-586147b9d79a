import React, { memo } from "react"
import { Box, Grid, Icon, Tag, Typography } from "@develop/fe-library"

import { Content, Footer } from "components/Layout"
import AppHeader from "components/shared/AppHeader"
import SimpleFooter from "components/SimpleFooter"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import { CreateTagForm, DeleteTagModal, EditTagModal } from "./components"

import { useProductTags } from "./hooks"

export const ProductTags = memo(() => {
  const {
    isDataLoading,
    productTags,
    hasHistory,
    handleGoBack,
    editTagHandler,
    deleteTagHandler,
  } = useProductTags()

  if (isDataLoading) {
    return null
  }

  return (
    <>
      <AppHeader title="Product tags" />

      <Box align="center" gap="l" hasBorder={{ bottom: true }} padding="m">
        {hasHistory ? (
          <Icon name="icnChevronLeft" onClick={handleGoBack} />
        ) : null}
        <Typography variant="--font-headline-3">{l("Product tags")}</Typography>
      </Box>

      <Content>
        <Box flexDirection="column" height="100%" tb={{ flexDirection: "row" }}>
          <Box
            flexShrink={0}
            padding="m"
            width="100%"
            tb={{
              width: "50%",
              padding: "l",
              hasBorder: {
                right: true,
              },
            }}
          >
            <CreateTagForm />
          </Box>
          <Box
            flexDirection="column"
            flexShrink={0}
            gap="m"
            padding="m"
            width="100%"
            tb={{
              width: "50%",
              padding: "l",
              gap: "l",
            }}
          >
            <Typography variant="--font-headline-5">
              {l("Current tags")}
            </Typography>

            {checkIsArray(productTags) ? (
              <Grid container gap="m">
                {productTags.map(({ id, title, color }) => (
                  <Grid key={id} item>
                    <Tag
                      closeIconName="icnDeleteOutlined"
                      closeIconTooltip={l("Delete")}
                      color={color}
                      editIconName="icnEdit"
                      editIconTooltip={l("Edit")}
                      isDefault={false}
                      isHiddenAfterOnClose={false}
                      name={title}
                      size="m"
                      onCloseTag={deleteTagHandler({ id, title, color })}
                      onEditTag={editTagHandler({ id, title, color })}
                    />
                  </Grid>
                ))}
              </Grid>
            ) : null}
          </Box>
        </Box>

        <EditTagModal />
        <DeleteTagModal />
      </Content>
      <Footer>
        <SimpleFooter />
      </Footer>
    </>
  )
})
