import React from "react"
import PropTypes from "prop-types"
import { Modal } from "@develop/fe-library"

import FormattedMessage from "components/FormattedMessage"
import { FieldErrorItem } from "../FieldErrorItem"

import l from "utils/intl"

import styles from "./productImportModal.module.scss"

export const ProductImportErrorsModal = ({ errorsList, onClose, title }) => {
  const getLines = (lines) => lines?.join(", ") || ""

  return (
    <Modal
      title={<FormattedMessage id={title} />}
      visible
      bodyClassName={styles.modalBody}
      onCancel={onClose}
    >
      <ul className={styles.errorsList}>
        {!errorsList.length
          ? null
          : errorsList.map(({ count, error, field, lines }, index) => (
              <li key={index} className={styles.errorItem}>
                <FieldErrorItem
                  controls={[
                    {
                      id: 1,
                      title: `${l("Field")}:`,
                      value: field,
                    },
                    {
                      id: 2,
                      title: `${l("Error")}:`,
                      value: error,
                    },
                    {
                      id: 3,
                      title: `${l("Total number of lines")}:`,
                      value: count,
                    },
                    {
                      id: 4,
                      title: `${l("First 100 lines")}:`,
                      value: getLines(lines),
                    },
                  ]}
                />
              </li>
            ))}
      </ul>
    </Modal>
  )
}

ProductImportErrorsModal.propTypes = {
  errorsList: PropTypes.array.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
}
