import { ProductImportItemType } from "types"
import { ObjectType } from "types/global"
import { TemplateHandlerValues } from "types/ProductImport"

export type AmazonS3PresignedUrl = {
  data: {
    lifeTimeSeconds: number
    url: string
  }
}

export type HandleUploadFileParams = {
  file: File
  templateHandlerName: TemplateHandlerValues
}

export type UseProductImportHandlersType = {
  handleItemDownload: ({ file_url }: ProductImportItemType) => void
  handleItemDelete: ({ id }: ProductImportItemType) => void
  handleErrorsShow: ({ id }: ProductImportItemType) => void
  handleUploadFileClick: () => void
  handleDownloadTemplateFile: () => void

  handleUploadModalClose: () => void
  handleUploadFile: ({
    file,
    templateHandlerName,
  }: HandleUploadFileParams) => void
  handleErrorModalClose: () => void
  getDataHandler: (searchOptions: ObjectType) => void
}

export type PickedProps = Pick<
  UseProductImportHandlersType,
  | "handleUploadModalClose"
  | "handleUploadFile"
  | "handleErrorModalClose"
  | "getDataHandler"
>

export type UseProductImportReturn = PickedProps & {
  dataSource: ProductImportItemType[]
  selectFiltersOptions: any // TODO: needs to add types globally by refactoring
  searchOptions: ObjectType
  totalCount: number
  tableGridIcons: any[] // TODO: needs to add types globally by refactoring
  tableGridButtons: any[] // TODO: needs to add types globally by refactoring
  isErrorsModalVisible: boolean
  productImportErrors: ProductImportItemType[]
  uploadModalVisible: boolean
  uploadError: string
  downloadTemplateFileHandler: (
    templateHandlerName: TemplateHandlerValues,
  ) => void
  downloadTemplateError: string
  isDownloadTemplateModalVisible: boolean
  closeDownloadTemplateModalHandler: () => void
  isProductImportDataLoading: boolean
}
