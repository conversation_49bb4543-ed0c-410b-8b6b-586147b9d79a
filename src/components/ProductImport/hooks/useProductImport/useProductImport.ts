import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"

// @ts-ignore
import productImportActions from "actions/productImportActions"

import { getGridSearchOptionsSelector } from "selectors/gridSelectors"
import { translationsSelector } from "selectors/mainStateSelectors"
import {
  filtersOptionsSelector,
  isProductImportDataLoadingSelector,
  productImportItemsSelector,
  productImportSelector,
} from "selectors/productImportSelectors"

import { useS3Uploader } from "hooks/useS3Uploader"

import { DELETE_ITEM } from "utils/confirm"
import { downloadFileFromUrl } from "utils/downloadFile"

import { permissionKeys, restrictPopoverMessages } from "constants/permissions"
import { STATUS_TYPES } from "constants/productImport"

import { ProductImportItemType } from "types"
import { TemplateHandlerValues } from "types/ProductImport"

import {
  AmazonS3PresignedUrl,
  UseProductImportHandlersType,
  UseProductImportReturn,
} from "./useProductImportTypes"

const {
  deleteItem,
  displayUploadModal,
  displayDownloadTemplateModal,
  downloadTemplateFile,
  getProductImports,
  getImportDataItemById,
  uploadProductImport,
  generatePreSignedUrl,
  // @ts-ignore
} = productImportActions

export const useProductImport = (): UseProductImportReturn => {
  const dispatch = useDispatch()

  const [isErrorsModalVisible, setIsErrorsModalVisible] = useState(false)
  const [locale, setLocale] = useState(null)

  const { locale: language } = useSelector(translationsSelector)
  const dataSource: UseProductImportReturn["dataSource"] = useSelector(
    productImportItemsSelector,
  )
  const {
    productImportErrors,
    totalCount,
    uploadError,
    uploadModalVisible,
    downloadTemplateError,
    isDownloadTemplateModalVisible,
  } = useSelector(productImportSelector)
  const isProductImportDataLoading = useSelector(
    isProductImportDataLoadingSelector,
  )
  const searchOptions = useSelector(getGridSearchOptionsSelector)
  const { uploadS3Bucket } = useS3Uploader()

  const selectFiltersOptions = filtersOptionsSelector()

  const getDataHandler: UseProductImportHandlersType["getDataHandler"] = (
    searchOptions,
  ) => {
    dispatch(
      getProductImports(
        {
          searchOptions,
        },
        null,
      ),
    )
  }

  const handleItemDownload: UseProductImportHandlersType["handleItemDownload"] =
    ({ file_url }) => downloadFileFromUrl(file_url)

  const handleItemDelete: UseProductImportHandlersType["handleItemDelete"] = ({
    id,
  }) => {
    dispatch(deleteItem({ id }, () => getDataHandler(searchOptions)))
  }
  const handleUploadFile: UseProductImportHandlersType["handleUploadFile"] = ({
    file,
    templateHandlerName,
  }) => {
    const successCallbackUpload = (): void => {
      getDataHandler(searchOptions)
      dispatch(displayUploadModal(false))
    }

    const successCallbackPresigned = async (
      data: AmazonS3PresignedUrl,
    ): Promise<void> => {
      const { url } = data?.data || {}

      if (!url) {
        return
      }

      await uploadS3Bucket(file, url)

      const [file_url] = url?.split("?")

      const payload = {
        file_url,
        handler_name: templateHandlerName,
      }

      dispatch(uploadProductImport(payload, successCallbackUpload))
    }

    const objectKey = file.name.replace(/\s/g, "-")

    dispatch(
      generatePreSignedUrl(
        {
          objectKey,
          objectType: "DATA_IMPORT_BAS_PRODUCT_COST_PERIODS",
        },
        successCallbackPresigned,
      ),
    )
  }

  const handleErrorsShow: UseProductImportHandlersType["handleErrorsShow"] = ({
    id,
  }) => {
    dispatch(getImportDataItemById({ id }, () => setIsErrorsModalVisible(true)))
  }

  const handleErrorModalClose: UseProductImportHandlersType["handleErrorModalClose"] =
    () => {
      setIsErrorsModalVisible(false)
    }

  const handleUploadModalClose: UseProductImportHandlersType["handleUploadModalClose"] =
    () => {
      dispatch(displayUploadModal(false))
    }
  const handleUploadFileClick: UseProductImportHandlersType["handleUploadFileClick"] =
    () => {
      dispatch(displayUploadModal(true))
    }

  const closeDownloadTemplateModalHandler = () => {
    dispatch(displayDownloadTemplateModal(false))
  }

  const openDownloadTemplateModalHandler = () => {
    dispatch(displayDownloadTemplateModal(true))
  }

  const downloadTemplateFileHandler = (
    templateHandlerName: TemplateHandlerValues,
  ) => {
    dispatch(
      downloadTemplateFile(
        { handler_name: templateHandlerName },
        closeDownloadTemplateModalHandler,
      ),
    )
  }

  useEffect(() => {
    const isStateWithoutLocale = language.length && !locale
    const currentLanguage = language[0].toUpperCase() + language.slice(1)

    if (isStateWithoutLocale) {
      setLocale(currentLanguage)
    }
  }, [language, locale])

  const tableGridIcons = [
    {
      id: 1,
      checkAvailability: ({ status }: ProductImportItemType) =>
        status === STATUS_TYPES.new,
      onClick: handleItemDelete,
      managePermission: permissionKeys.productImportManage,
      popoverMessage: restrictPopoverMessages.alter,
      confirm: {
        template: DELETE_ITEM,
      },
      title: "Delete",
      type: "icnDeleteOutlined",
    },
    {
      id: 2,
      checkAvailability: ({ file_url }: ProductImportItemType) => !!file_url,
      onClick: handleItemDownload,
      title: "Download",
      type: "icnDownload",
    },
    {
      id: 3,
      checkAvailability: ({ count_errors }: ProductImportItemType) =>
        count_errors > 0,
      onClick: handleErrorsShow,
      title: "Show errors",
      type: "icnWarning",
    },
  ]

  const tableGridButtons = [
    {
      icon: "icnUpload",
      onClick: handleUploadFileClick,
      title: "Upload file",
    },
    {
      icon: "icnDownload",
      onClick: openDownloadTemplateModalHandler,
      title: "Download template",
    },
  ]

  return {
    dataSource,
    selectFiltersOptions,
    searchOptions,
    totalCount,
    getDataHandler,
    tableGridIcons,
    tableGridButtons,
    isErrorsModalVisible,
    productImportErrors,
    handleErrorModalClose,
    uploadModalVisible,
    uploadError,
    handleUploadModalClose,
    handleUploadFile,
    downloadTemplateError,
    isDownloadTemplateModalVisible,
    closeDownloadTemplateModalHandler,
    downloadTemplateFileHandler,
    isProductImportDataLoading,
  }
}
