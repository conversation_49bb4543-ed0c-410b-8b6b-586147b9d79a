import {
  COLUMN_INPUT_TYPE_INPUT,
  COLUMN_INPUT_TYPE_SELECT,
} from "constants/grid"

export const useProductImportColumns = () => {
  const tableColumns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 100,
    },
    {
      title: "Import type",
      dataIndex: "showType",
      key: "type",
      type: COLUMN_INPUT_TYPE_SELECT,
      sorter: true,
      width: 300,
    },
    {
      title: "File type",
      dataIndex: "handlerName",
      key: "handler_name",
      type: COLUMN_INPUT_TYPE_SELECT,
      sorter: true,
      width: 120,
    },
    {
      title: "Status",
      dataIndex: "statusLabel",
      key: "status",
      sorter: true,
      type: COLUMN_INPUT_TYPE_SELECT,
      width: 120,
    },
    {
      title: "Number of products",
      dataIndex: "count_all_items",
      key: "count_all_items",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 220,
      isNumber: true,
    },
    {
      title: "Number of rows with errors",
      dataIndex: "count_errors",
      key: "count_errors",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 220,
      isNumber: true,
    },
    {
      title: "Number of imported rows",
      dataIndex: "count_imported_items",
      key: "count_imported_items",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 220,
      isNumber: true,
    },
    {
      title: "Start date",
      dataIndex: "startDateLabel",
      key: "started_at",
      sorter: true,
      width: 130,
    },
    {
      title: "End date",
      dataIndex: "endDateLabel",
      key: "finished_at",
      sorter: true,
      width: 130,
    },
    {
      title: "Created on",
      dataIndex: "dateInsertedLabel",
      key: "created_at",
      sorter: true,
      width: 130,
    },
    {
      title: "Date updated",
      dataIndex: "dateUpdateLabel",
      key: "updated_at",
      sorter: true,
      width: 130,
    },
  ]

  return { tableColumns }
}
