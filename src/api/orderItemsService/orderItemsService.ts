import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

import { AmazonOrderV1RequestParams } from "types"

const { APP_BAS_API_HOST_VERSION } = API_VERSION

const rootPath = `/v${APP_BAS_API_HOST_VERSION}`

const getAmazonOrderV1 = (params: AmazonOrderV1RequestParams) => {
  return profitApi.get(`${rootPath}/amazon-order-v1`, { params })
}

export { getAmazonOrderV1 }
