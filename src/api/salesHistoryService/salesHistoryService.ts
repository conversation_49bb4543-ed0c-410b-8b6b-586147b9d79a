import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

import { SalesHistoryChartRequestParams } from "types/RequestParams"

import { AsyncResponse, SalesHistoryResponse } from "./salesHistoryServiceTypes"

const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}`

export const getWidgetData = (
  params: SalesHistoryChartRequestParams,
  customerId: string,
) => {
  return profitApi.get(
    `${rootPath}/widget/key-performance${getUrlSearchParamsString({
      params: { ...params, customerId },
    })}`,
  )
}

export const getChartData = (
  params: SalesHistoryChartRequestParams,
  customerId: string,
): AsyncResponse<SalesHistoryResponse> => {
  return profitApi.get(
    `${rootPath}/widget/sales-history-v-one${getUrlSearchParamsString({
      params: { ...params, customerId },
    })}`,
  )
}

export const getMinimumSalesHistoryDate = (customerId: string) => {
  return profitApi.get(
    `${rootPath}/information/minimum-statistic-date?customerId=${customerId}`,
  )
}

export const getProducts = (
  params: SalesHistoryChartRequestParams,
  customerId: string,
) => {
  return profitApi.get(
    `${rootPath}/product${getUrlSearchParamsString({
      params: { ...params, customerId },
    })}`,
  )
}

export const getWidgetStatisticsData = (
  params: SalesHistoryChartRequestParams,
) => {
  return profitApi.get(
    `${rootPath}/widget/overall-statistics${getUrlSearchParamsString({
      params,
    })}`,
  )
}

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getMinimumSalesHistoryDate,
  getProducts,
  getChartData,
  getWidgetData,
  getWidgetStatisticsData,
}
