import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}/`

export const getTransactions = (params) => {
  return profitApi.get(
    `${rootPath}transaction-v1${getUrlSearchParamsString({ params })}`,
  )
}

export const getCategoryFilters = (params) => {
  return profitApi.get(
    `${rootPath}transaction/category-filters${getUrlSearchParamsString({
      params,
    })}`,
  )
}

export default {
  getTransactions,
  getCategoryFilters,
}
