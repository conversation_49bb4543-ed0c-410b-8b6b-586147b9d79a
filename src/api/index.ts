import amazonCustomerAccountMarketPlaceService from "api/amazonCustomerAccountMarketPlaceService"
import exportTemplatesService from "api/exportTemplatesService"
import indirectCostsService from "api/indirectCostsService"
import operationalDashboardService from "api/operationalDashboardService/operationalDashboardService"
import * as ordersService from "api/ordersService"
import * as productAggregatedSalesInfoService from "api/productAggregatedSalesInfoService/productAggregatedSalesInfoService"
import productBulkEditService from "api/productBulkEditService"
import productCostService from "api/productCostService"
import productExportService from "api/productExportService"
import productImportService from "api/productImportService"
import productsService from "api/productsService"
import recurrentExportSettingsService from "api/recurrentExportSettingsService"
import recurrentImportSettingsService from "api/recurrentImportSettingsService"
import tableSettingsService from "api/tableSettingsService"
import transactionsService from "api/transactionsService"
import userSettingsService from "api/userSettingsService"

import currenciesService from "./currencyService"
import marketplaceService from "./marketplaceService"
import * as orderItemsService from "./orderItemsService"
import productTagColorService from "./productTagColorService"
import productTagsService from "./productTagsService"
import salesHistoryService from "./salesHistoryService"
import segmentsService from "./segmentsService"

export default {
  tableSettingsService,
  currenciesService,
  marketplaceService,
  salesHistoryService,
  amazonCustomerAccountMarketPlaceService,
  productsService,
  productCostService,
  productImportService,
  productBulkEditService,
  productExportService,
  exportTemplatesService,
  recurrentExportSettingsService,
  recurrentImportSettingsService,
  transactionsService,
  userSettingsService,
  ordersService,
  indirectCostsService,
  segmentsService,
  productAggregatedSalesInfoService,
  productTagColorService,
  productTagsService,
  operationalDashboardService,
  orderItemsService,
}
