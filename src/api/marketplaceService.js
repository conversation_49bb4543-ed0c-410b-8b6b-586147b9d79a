import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import config from "config"

import { deleteMethod, get, post, put } from "utils/request"

const { apiVersion } = config
const rootPath = `/v${apiVersion}/user-marketplace-group`

export const getMarketplaceGroups = (params = {}) => {
  return get(
    `${rootPath}${getUrlSearchParamsString({ params: { ...params, all: 1 } })}`,
  )
}

export const addMarketplaceGroup = (payload) => {
  return post(rootPath, payload)
}

export const updateMarketplaceGroup = (payload) => {
  return put(`${rootPath}?id=${payload.id}`, payload)
}

export const deleteMarketplaceGroup = (groupId) => {
  return deleteMethod(`${rootPath}?id=${groupId}`)
}

export default {
  getMarketplaceGroups,
  addMarketplaceGroup,
  deleteMarketplaceGroup,
  updateMarketplaceGroup,
}
