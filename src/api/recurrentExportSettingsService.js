import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}/`

export const getRecurrentExportSettings = ({ customerId }) =>
  profitApi.get(
    `${rootPath}data-export-recurrent${getUrlSearchParamsString({
      params: {
        customerId,
        all: 1,
        sort: "-id",
      },
    })}`,
  )

export const addRecurrentExportSetting = ({ customerId, payload }) =>
  profitApi.post(
    `${rootPath}data-export-recurrent${getUrlSearchParamsString({
      params: {
        customerId,
      },
    })}`,
    payload,
  )

export const updateRecurrentExportSetting = ({ customerId, id, payload }) =>
  profitApi.put(
    `${rootPath}data-export-recurrent/${id}${getUrlSearchParamsString({
      params: {
        customerId,
      },
    })}`,
    payload,
  )

export const deleteRecurrentExportSetting = ({ customerId, id }) =>
  profitApi.delete(
    `${rootPath}data-export-recurrent/${id}${getUrlSearchParamsString({
      params: {
        customerId,
      },
    })}`,
  )

export default {
  getRecurrentExportSettings,
  addRecurrentExportSetting,
  updateRecurrentExportSetting,
  deleteRecurrentExportSetting,
}
