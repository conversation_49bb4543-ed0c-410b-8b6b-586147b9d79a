import config from "config"

import { deleteMethod, get, post, put } from "utils/request"

const { apiVersion } = config
const mainUrl = `/v${apiVersion}/user-setting`

const getUserSettings = (key) => get(`${mainUrl}?key=${key}&all=1`)

const saveUserSettings = ({ key, settings }) =>
  post(mainUrl, {
    key,
    value: JSON.stringify(settings),
  })

const updateUserSettings = ({ id, settings }) =>
  put(`${mainUrl}?id=${id}`, {
    value: JSON.stringify(settings),
  })

const deleteUserSettings = (id) => deleteMethod(`${mainUrl}?id=${id}`)

export default {
  getUserSettings,
  saveUserSettings,
  updateUserSettings,
  deleteUserSettings,
}
