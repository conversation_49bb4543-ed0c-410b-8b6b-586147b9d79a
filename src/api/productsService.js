import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"
import { store } from "App"

import config from "config"

import { customerSelector } from "selectors/mainStateSelectors"

import { get, post, profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

const { apiVersion } = config
const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}/`

/**
 * Get Amazon Products
 * @param params
 * @returns {Promise<{data}|undefined>}
 */
export const getProducts = (params) =>
  profitApi.get(`${rootPath}product${getUrlSearchParamsString({ params })}`)

/**
 *
 * @param values
 * @param id
 * @returns {Promise<{data: any}|undefined>}
 */
export const updateProduct = ({ productId, values, customerId }) =>
  profitApi.put(
    `${rootPath}product/${productId}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    values,
  )

export const getAmazonProducts = ({ marketPlaceId, sellerId, value }) =>
  get(
    `/v${apiVersion}/amazon/catalog-items?amazonMarketplaceId=${marketPlaceId}&sellerId=${sellerId}&query=${value}`,
  )

export const setValidateProduct = (id, payload) => {
  const {
    customer: { id: customerId },
  } = customerSelector(store.getState())

  return post(
    `/v${apiVersion}/amazon-product/validate/c-${customerId}?id=${id}`,
    payload,
  )
}

export const getProductFilters = ({ params = {}, customerId }) => {
  return profitApi.get(
    `${rootPath}product/filters${getUrlSearchParamsString({
      params: { ...params, customerId },
    })}`,
  )
}

export default {
  getProducts,
  getProductFilters,
  updateProduct,
  getAmazonProducts,
  setValidateProduct,
}
