import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

const { APP_BAS_API_HOST_VERSION } = API_VERSION

const rootPath = `/v${APP_BAS_API_HOST_VERSION}/`

export const getDataCompletenessFactors = (customerId: number) => {
  return profitApi.get(
    `${rootPath}data-completeness/widget${getUrlSearchParamsString({
      params: { customerId },
    })}`,
  )
}

export const getDataCompletenessReferralFeeChanges = (params) => {
  return profitApi.get(
    `${rootPath}data-completeness/referral-fee-changes${getUrlSearchParamsString(
      {
        params,
      },
    )}`,
  )
}

export const getDataCompletenessFbaFulfillmentFeeChanges = (params) => {
  return profitApi.get(
    `${rootPath}data-completeness/fba-fulfillment-fee-changes${getUrlSearchParamsString(
      {
        params,
      },
    )}`,
  )
}

export const getDataCompletenessAdjustmentToFees = (params) => {
  return profitApi.get(
    `${rootPath}data-completeness/adjustment-to-fees${getUrlSearchParamsString({
      params,
    })}`,
  )
}

export default {
  getDataCompletenessFactors,
  getDataCompletenessReferralFeeChanges,
  getDataCompletenessFbaFulfillmentFeeChanges,
  getDataCompletenessAdjustmentToFees,
}
