import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}/`

export const addProductBulkEditProcess = ({
  customerId,
  filteredParams,
  payload,
}) =>
  profitApi.post(
    `${rootPath}product/bulk-edit${getUrlSearchParamsString({
      params: {
        customerId,
        ...filteredParams,
      },
    })}`,
    payload,
  )

export const stopProductBulkEditProcessById = ({ customerId, id }) =>
  profitApi.post(
    `${rootPath}data-import/stop-bulk-edit-process${getUrlSearchParamsString({
      params: {
        customerId,
      },
    })}`,
    { id },
  )

export default {
  addProductBulkEditProcess,
  stopProductBulkEditProcessById,
}
