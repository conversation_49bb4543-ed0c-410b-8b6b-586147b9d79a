import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}/`

export const getIndirectCosts = (params) => {
  return profitApi.get(
    `${rootPath}indirect-cost${getUrlSearchParamsString({ params })}`,
  )
}

export const createIndirectCost = ({ customerId, values }) => {
  return profitApi.post(
    `${rootPath}indirect-cost${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    values,
  )
}

export const updateIndirectCost = ({ customerId, costId, values }) => {
  return profitApi.put(
    `${rootPath}indirect-cost/${costId}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    values,
  )
}

export const deleteIndirectCost = ({ customerId, costId }) => {
  return profitApi.delete(
    `${rootPath}indirect-cost/${costId}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
  )
}

export const deleteIndirectCosts = ({ customerId, ids, side }) => {
  return profitApi.post(
    `${rootPath}indirect-cost/bulk-delete${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    {
      ids,
      side,
    },
  )
}

export const getIndirectCostTypes = (params) => {
  return profitApi.get(
    `${rootPath}indirect-cost-type${getUrlSearchParamsString({
      params: { ...params, all: 1 },
    })}`,
  )
}

export const createIndirectCostType = ({ customerId, values }) => {
  return profitApi.post(
    `${rootPath}indirect-cost-type${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    values,
  )
}

export const updateIndirectCostType = ({ customerId, costId, values }) => {
  return profitApi.put(
    `${rootPath}indirect-cost-type/${costId}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    values,
  )
}

export const deleteIndirectCostType = ({ customerId, costId }) => {
  return profitApi.delete(
    `${rootPath}indirect-cost-type/${costId}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
  )
}

export default {
  getIndirectCosts,
  createIndirectCost,
  updateIndirectCost,
  deleteIndirectCost,
  deleteIndirectCosts,
  getIndirectCostTypes,
  createIndirectCostType,
  updateIndirectCostType,
  deleteIndirectCostType,
}
