import { OrderType } from "types"
import { OfferType } from "types/OfferType"
import {
  OrderAmountCost,
  OrderItem,
  ProfitBreakdownItem,
  Summary,
} from "types/OrderDetails"
import { OrderStatus } from "types/OrderStatus"

export type OrderDetailsResponse = {
  currency_id: string
  is_approximate_amounts_calculation: boolean
  updated_at: string
  summary: Summary
  breakdown: ProfitBreakdownItem[]
  products: OrderItem[]
}

export type ProfitBreakdownResponseData = {
  currency_id: string
  is_approximate_amounts_calculation: boolean
  order_id: string | null
  breakdown: ProfitBreakdownItem[]
}

export type GetAmazonOrderFeesBreakdownParams = {
  customerId: number
  amazonOrderItemId: string
  currencyId: string
}

export type GetAmazonOrderFeesBreakdownResponse = {
  fileName?: string
  blob?: Blob
  data?: ProfitBreakdownResponseData
}

export type GetAmazonOrderExpensesBreakdownParams =
  GetAmazonOrderFeesBreakdownParams

export type GetAmazonOrderExpensesBreakdownResponse =
  GetAmazonOrderFeesBreakdownResponse

export type GetAmazonOrderDetailsParams = {
  customerId?: number
  amazonOrderId: string
  currencyId: string
}

export type GetAmazonOrderDetailsResponse = {
  fileName?: string
  blob?: Blob
  data?: OrderDetailsResponse
}

export type GetAmazonOrderParams = {
  customerId?: string // BE expect number
  page?: string // BE expect number
  sort?: string
  pageSize?: string // BE expect number
  all?: 1 | 0
  order_item_id?: string
  order_id?: string
  order_status?: OrderStatus[]
  seller_id?: string
  seller_sku?: string
  marketplace_id?: string | string[] | undefined // diff for BE side
  item_price?: string // BE expect number
  quantity?: string // BE expect number
  quantity_refunded?: string // BE expect number
  offer_type?: OfferType
  amazon_fees_amount?: string // BE expect number
  expenses_amount?: string // BE expect number
  revenue_amount?: string // BE expect number
  estimated_profit_amount?: string // BE expect number
  promotion_amount?: string // BE expect number
  product_id?: string // BE expect number
  product_asin?: string
  product_title?: string
  product_ean?: string
  product_upc?: string
  product_isbn?: string
  product_brand?: string
  product_type?: string
  product_manufacturer?: string
  product_parent_asin?: string
  adult_product?: 1 | 0
  product_stock_type?: string
  product_condition?: string
  currency_id?: string
  order_purchase_date?: string
}

export type GetAmazonOrderResponse = {
  fileName?: string
  blob?: Blob
  data?: OrderType[]
}

export type GetAmazonOrderStatusesParams = {
  customerId?: number
}

export type GetAmazonOrderStatusesResponse = {
  fileName?: string
  blob?: Blob
  data?: Record<OrderStatus, string>
}

export type GetAmazonOrderLastUpdateDateResponse = {
  fileName?: string
  blob?: Blob
  data?: {
    updatedAt?: string
  }
}

export type GetAmazonOrderLastUpdateDateParams = {
  customerId?: number
}

export type AmazonOrderAmountCostResponseData = {
  currency_id?: string
  items?: OrderAmountCost[]
}

export type GetAmazonOrderAmountCostResponse = {
  fileName?: string
  blob?: Blob
  data?: AmazonOrderAmountCostResponseData
}

export type GetAmazonOrderAmountCostParams = {
  customerId?: number
  amazonOrderId: string
  salesCategoryId?: string
}

export type PostAmazonOrderUpdateAmountCostResponse = {
  fileName?: string
  blob?: Blob
  data?: {
    result?: string
  }
}
export type PostAmazonOrderUpdateAmountCostParams = {
  params: {
    customerId?: string
    amazonOrderId: string
    salesCategoryId?: string
  }
  payload: {
    currency_id?: string
    items?: OrderAmountCost[]
  }
}
