import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

import {
  GetAmazonOrderAmountCostParams,
  GetAmazonOrderAmountCostResponse,
  GetAmazonOrderDetailsParams,
  GetAmazonOrderDetailsResponse,
  GetAmazonOrderExpensesBreakdownParams,
  GetAmazonOrderExpensesBreakdownResponse,
  GetAmazonOrderFeesBreakdownParams,
  GetAmazonOrderFeesBreakdownResponse,
  GetAmazonOrderLastUpdateDateParams,
  GetAmazonOrderLastUpdateDateResponse,
  GetAmazonOrderParams,
  GetAmazonOrderResponse,
  GetAmazonOrderStatusesParams,
  GetAmazonOrderStatusesResponse,
  PostAmazonOrderUpdateAmountCostParams,
  PostAmazonOrderUpdateAmountCostResponse,
} from "./ordersServiceTypes"

const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}/`

export const getOrders = (
  params: GetAmazonOrderParams,
): Promise<GetAmazonOrderResponse> => {
  return profitApi.get(
    `${rootPath}amazon-order-v1${getUrlSearchParamsString({ params })}`,
  )
}

export const getOrderStatuses = (
  params: GetAmazonOrderStatusesParams,
): Promise<GetAmazonOrderStatusesResponse> => {
  return profitApi.get(
    `${rootPath}amazon-order/statuses${getUrlSearchParamsString({ params })}`,
  )
}

export const getLastUpdateOrdersDate = (
  params: GetAmazonOrderLastUpdateDateParams,
): Promise<GetAmazonOrderLastUpdateDateResponse> => {
  return profitApi.get(
    `${rootPath}amazon-order/last-update-date${getUrlSearchParamsString({
      params,
    })}`,
  )
}

export const getAmazonOrderFeesBreakdown = (
  params: GetAmazonOrderFeesBreakdownParams,
): Promise<GetAmazonOrderFeesBreakdownResponse> => {
  return profitApi.get(
    `${rootPath}amazon-order-v1/amazon-fees-breakdown${getUrlSearchParamsString(
      {
        params,
      },
    )}`,
  )
}

export const getAmazonOrderExpensesBreakdown = (
  params: GetAmazonOrderExpensesBreakdownParams,
): Promise<GetAmazonOrderExpensesBreakdownResponse> => {
  return profitApi.get(
    `${rootPath}amazon-order-v1/expenses-breakdown${getUrlSearchParamsString({
      params,
    })}`,
  )
}

export const getAmazonOrderDetails = (
  params: GetAmazonOrderDetailsParams,
): Promise<GetAmazonOrderDetailsResponse> => {
  return profitApi.get(
    `${rootPath}amazon-order-v1/details${getUrlSearchParamsString({ params })}`,
  )
}

export const getAmazonOrderAmountCosts = (
  params: GetAmazonOrderAmountCostParams,
): Promise<GetAmazonOrderAmountCostResponse> => {
  return profitApi.get(
    `${rootPath}amazon-order/amount-cost${getUrlSearchParamsString({
      params,
    })}`,
  )
}

export const updateAmazonOrderAmountCosts = ({
  params,
  payload,
}: PostAmazonOrderUpdateAmountCostParams): Promise<PostAmazonOrderUpdateAmountCostResponse> => {
  return profitApi.post(
    `${rootPath}amazon-order/update-amount-cost${getUrlSearchParamsString({
      params,
    })}`,
    payload,
  )
}

const ordersService = {
  getOrders,
  getOrderStatuses,
  getLastUpdateOrdersDate,
  getAmazonOrderFeesBreakdown,
  getAmazonOrderExpensesBreakdown,
  getAmazonOrderDetails,
  getAmazonOrderAmountCosts,
  updateAmazonOrderAmountCosts,
}

export default ordersService
