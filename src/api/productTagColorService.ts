import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}/tag-color`

export const getProductTagColors = (params = {}) =>
  profitApi.get(`${rootPath}${getUrlSearchParamsString({ params })}`)

export const createProductTagColor = ({ payload, customerId }) =>
  profitApi.post(
    `${rootPath}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    payload,
  )

export const deleteProductTagColor = ({ id, customerId }) =>
  profitApi.delete(
    `${rootPath}/${id}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
  )

export default {
  getProductTagColors,
  createProductTagColor,
  deleteProductTagColor,
}
