import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}/`

export const getExportTemplates = (searchOptions = {}) =>
  profitApi.get(
    `${rootPath}data-export-template${getUrlSearchParamsString({
      params: searchOptions,
    })}`,
  )

export const addExportTemplate = ({ customerId, ...payload }) =>
  profitApi.post(
    `${rootPath}data-export-template${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    payload,
  )

export const deleteExportTemplate = ({ id, customerId }) =>
  profitApi.delete(
    `${rootPath}data-export-template/${id}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
  )

export const getExportTemplateById = ({ id, customerId }) =>
  profitApi.get(
    `${rootPath}data-export-template/${id}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
  )

export const updateExportTemplate = ({ id, customerId, ...payload }) =>
  profitApi.put(
    `${rootPath}data-export-template/${id}${getUrlSearchParamsString({
      params: {
        customerId,
      },
    })}`,
    payload,
  )

export const getExportTemplateFiltersFields = (params) =>
  profitApi.get(
    `${rootPath}data-export-template/field-filters${getUrlSearchParamsString({
      params,
    })}`,
  )

export const getExportTemplateOrderFiltersFields = ({
  customerId,
  handler_name,
  ...payload
}) =>
  profitApi.post(
    `${rootPath}data-export-template/field-filters${getUrlSearchParamsString({
      params: { customerId, handler_name },
    })}`,
    payload,
  )

export const getExportTemplateFieldGroup = (params) =>
  profitApi.get(
    `${rootPath}data-export-template/export-field-group${getUrlSearchParamsString(
      {
        params,
      },
    )}`,
  )

export default {
  addExportTemplate,
  deleteExportTemplate,
  getExportTemplates,
  getExportTemplateById,
  updateExportTemplate,
  getExportTemplateFiltersFields,
  getExportTemplateOrderFiltersFields,
  getExportTemplateFieldGroup,
}
