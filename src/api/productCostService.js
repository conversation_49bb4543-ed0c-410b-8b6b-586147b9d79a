import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}/`

export const getProducts = (params) =>
  profitApi.get(`${rootPath}product${getUrlSearchParamsString({ params })}`)

export const updateProduct = ({ productId, customerId, values }) =>
  profitApi.put(
    `${rootPath}product/${productId}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    values,
  )

export const getProductCostCategories = (params) => {
  return profitApi.get(
    `${rootPath}product-cost-category${getUrlSearchParamsString({ params })}`,
  )
}

export const getProductCostPeriods = (params) =>
  profitApi.get(
    `${rootPath}product-cost-period${getUrlSearchParamsString({ params })}`,
  )

export const getProductCostPeriod = ({ periodId, customerId }) =>
  profitApi.get(
    `${rootPath}product-cost-period/${periodId}${getUrlSearchParamsString({
      params: {
        customerId,
      },
    })}`,
  )

export const createNewPeriod = ({ customerId, values }) =>
  profitApi.post(
    `${rootPath}product-cost-period${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    values,
  )

export const updatePeriod = ({ customerId, periodId, values }) =>
  profitApi.put(
    `${rootPath}product-cost-period/${periodId}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    values,
  )

export const deletePeriod = ({ customerId, id }) =>
  profitApi.delete(
    `${rootPath}product-cost-period/${id}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
  )

export const updatePeriodCosts = ({ customerId, periodId, costs }) =>
  profitApi.post(
    `${rootPath}product-cost-item/bulk-update${getUrlSearchParamsString({
      params: {
        customerId,
        product_cost_period_id: periodId,
      },
    })}`,
    costs,
  )

export const getProductCostItems = (params) =>
  profitApi.get(
    `/v${
      API_VERSION.APP_BAS_API_HOST_VERSION
    }/product-cost-item${getUrlSearchParamsString({ params })}`,
  )

export const updateProductCostItem = ({ customerId, values }) =>
  profitApi.put(
    `${rootPath}product-cost-item/${values.id}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    values,
  )

export const createProductCostItem = ({ customerId, values }) =>
  profitApi.post(
    `${rootPath}product-cost-item${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    values,
  )

export const createNewCostCategory = ({ customerId, values }) =>
  profitApi.post(
    `${rootPath}product-cost-category${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    values,
  )

export const deleteCostCategory = ({ costCategoryId, customerId }) =>
  profitApi.delete(
    `${rootPath}product-cost-category/${costCategoryId}${getUrlSearchParamsString(
      { params: { customerId } },
    )}`,
  )

export const transferCostsForMarketplaces = (params) =>
  profitApi.post(
    `${rootPath}product-cost-period/apply-for-marketplaces${getUrlSearchParamsString(
      { params },
    )}`,
  )

export default {
  getProducts,
  updateProduct,
  getProductCostCategories,
  getProductCostPeriods,
  getProductCostPeriod,
  createNewPeriod,
  updatePeriod,
  deletePeriod,
  updatePeriodCosts,
  getProductCostItems,
  updateProductCostItem,
  createProductCostItem,
  createNewCostCategory,
  deleteCostCategory,
  transferCostsForMarketplaces,
}
