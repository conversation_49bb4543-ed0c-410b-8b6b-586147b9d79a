import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}`
const productFilterListPath = `${rootPath}/product-filter-list`

export const getSegments = ({ customerId }) =>
  profitApi.get(
    `${productFilterListPath}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
  )

export const createSegment = ({ customerId, ...payload }) =>
  profitApi.post(
    `${productFilterListPath}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    payload,
  )

export const updateSegment = ({ id, customerId, ...payload }) =>
  profitApi.put(
    `${productFilterListPath}/${id}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    payload,
  )

export const deleteSegment = ({ id, customerId }) =>
  profitApi.delete(
    `${productFilterListPath}/${id}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
  )

export default {
  getSegments,
  createSegment,
  updateSegment,
  deleteSegment,
}
