import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { profitApi } from "utils/request"

import { API_VERSION } from "constants/request"

const rootPath = `/v${API_VERSION.APP_BAS_API_HOST_VERSION}/tag`

export const getProductTags = (params = {}) =>
  profitApi.get(
    `${rootPath}${getUrlSearchParamsString({
      params,
    })}`,
  )

export const createProductTag = ({ payload, customerId }) =>
  profitApi.post(
    `${rootPath}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    payload,
  )

export const updateProductTag = ({ id, payload, customerId }) =>
  profitApi.put(
    `${rootPath}/${id}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
    payload,
  )

export const deleteProductTag = ({ id, customerId }) =>
  profitApi.delete(
    `${rootPath}/${id}${getUrlSearchParamsString({
      params: { customerId },
    })}`,
  )

export default {
  getProductTags,
  createProductTag,
  updateProductTag,
  deleteProductTag,
}
