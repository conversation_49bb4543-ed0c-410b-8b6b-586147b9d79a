import { ActionTypeAsync } from "actions/types"

export type SalesHistoryActionsAsyncTypeNames =
  | "getMinimumSalesHistoryDate"
  | "getProducts"
  | "getProductsTopFilter"
  | "getProductFilters"
  | "getWidgetData"
  | "getChartData"
  | "getWidgetStatisticsData"

export type SalesHistoryActionsSyncTypeNames =
  | "updateCheckedCategoriesState"
  | "clearProducts"
  | "clearProductsTopFilter"
  | "clearProductFilters"

export type SalesHistoryActionTypesKeys =
  | SalesHistoryActionsAsyncTypeNames
  | SalesHistoryActionsSyncTypeNames

export type SalesHistoryActionTypes = {
  [Key in SalesHistoryActionsAsyncTypeNames]: ActionTypeAsync
} & {
  [Key in SalesHistoryActionsSyncTypeNames]: string
}

export type SalesHistoryDefinitions = {
  [Key in SalesHistoryActionTypesKeys]: (...args: any[]) => any
}
