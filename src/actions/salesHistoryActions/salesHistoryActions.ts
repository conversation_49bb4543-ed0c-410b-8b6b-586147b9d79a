import {
  getObjectEntries,
  getObjectKeys,
  getObjectValues,
} from "@develop/fe-library/dist/utils"
import { store } from "App"

import api from "api"

import userSettingsActions from "actions/userSettingsActions"
import { withActionPrefix } from "actions/utils"

import {
  amazonMarketplacesSelector,
  customerIdSelector,
  customerSelector,
} from "selectors/mainStateSelectors"
import {
  salesHistoryCategoriesSelectionV1Selector,
  salesHistorySettingsSelector,
} from "selectors/userSettingsSelectors"

import getMainApp from "components/MainAppProvider"

import { checkIsArray } from "utils/arrayHelpers"
import { buildInitialSalesHistorySettings } from "utils/buildInitialSalesHistorySettings"
import { compareArrayKeysWithPriority } from "utils/compareArrayKeysWithPriority"
import generateActions from "utils/generateActions"
import { getFormattedProducts } from "utils/getFormattedProducts"
import { isNonEmptyObject } from "utils/objectHelpers"

import { GLOBAL_NOTIFICATION_TYPES } from "constants/globalNotifications"
import { USER_SETTINGS_KEYS } from "constants/user"

import {
  SalesHistoryCategory,
  SalesHistorySettings,
} from "components/SalesHistoryWidget/SalesHistoryWidgetTypes"

import {
  SalesHistoryActionTypes,
  SalesHistoryDefinitions,
} from "./salesHistoryActionsTypes"

const createActionType = withActionPrefix("operationalDashboard")

const {
  salesHistoryService: {
    getMinimumSalesHistoryDate,
    getProducts,
    getWidgetData,
    getChartData,
    getWidgetStatisticsData,
  },
  productsService: { getProductFilters },
} = api

const types: SalesHistoryActionTypes = {
  updateCheckedCategoriesState: createActionType(
    "updateCheckedCategoriesState",
  ),
  clearProducts: createActionType("clearProducts"),
  clearProductsTopFilter: createActionType("clearProductsTopFilter"),
  clearProductFilters: createActionType("clearProductFilters"),
  getMinimumSalesHistoryDate: createActionType(
    "getMinimumSalesHistoryDate",
    true,
  ),
  getProducts: createActionType("getProducts", true),
  getProductsTopFilter: createActionType("getProductsTopFilter", true),
  getProductFilters: createActionType("getProductFilters", true),
  getWidgetData: createActionType("getWidgetData", true),
  getChartData: createActionType("getChartData", true),
  getWidgetStatisticsData: createActionType("getWidgetStatisticsData", true),
}

const isInfoError = (error) => {
  return ["MODULE_NOT_INITIALIZED_YET", "NO_VALID_PLAN"].includes(
    error?.payload?.internal_code,
  )
}

const definitions: SalesHistoryDefinitions = {
  updateCheckedCategoriesState: (params) => params,

  getMinimumSalesHistoryDate: (successCallback) => ({
    callApi: () => {
      const state = store.getState()
      const {
        customer: { id },
      } = customerSelector(state)

      return getMinimumSalesHistoryDate(id)
    },
    successCallback,
    failureCallback: (error) => {
      const type = isInfoError(error)
        ? GLOBAL_NOTIFICATION_TYPES.INFO
        : GLOBAL_NOTIFICATION_TYPES.ERROR
      const message = error?.payload?.message

      const mainApp = getMainApp()

      mainApp?.actions?.showGlobalNotification?.({
        type,
        message,
      })
    },
    doNotShowError: true,
  }),

  getProducts: (
    params = {},
    successCallback = () => {},
    failureCallback = () => {},
  ) => ({
    callApi: async () => {
      const state = store.getState()
      const {
        customer: { id },
      } = customerSelector(state)
      const { amazonMarketplaces } = amazonMarketplacesSelector(state)

      const response = await getProducts(params, id)

      const { data: products = {} } = response

      return {
        data: {
          products: getFormattedProducts({
            products: products?.data,
            amazonMarketplaces,
          }),
        },
      }
    },
    successCallback,
    failureCallback,
  }),
  clearProducts: () => {},

  getProductsTopFilter: (
    params = {},
    successCallback = () => {},
    failureCallback = () => {},
  ) => ({
    callApi: async () => {
      const state = store.getState()
      const {
        customer: { id },
      } = customerSelector(state)
      const { amazonMarketplaces } = amazonMarketplacesSelector(state)

      const response = await getProducts(params, id)

      const { data: products = {} } = response

      return {
        data: {
          products: getFormattedProducts({
            products: products?.data,
            amazonMarketplaces,
          }),
        },
      }
    },
    successCallback,
    failureCallback,
  }),
  clearProductsTopFilter: () => {},

  getProductFilters: (params = {}, successCallback, failureCallback) => ({
    callApi: async () => {
      const state = store.getState()
      const {
        customer: { id },
      } = customerSelector(state)

      // Type of raw ProductFilters is { filter: { [key: string]: { key: string; value: string } } }
      const productFilters = await getProductFilters({ params, customerId: id })

      const productFiltersArray = getObjectEntries(productFilters.data)

      // Encode coma sign to prevent unexpected splittings
      // "|" is used instead as a separator in agreement with the backend side
      const productFiltersEncoded = checkIsArray(productFiltersArray)
        ? productFiltersArray.reduce((memo, [filterKey, filterValue]) => {
            const filterValues = getObjectEntries(filterValue).reduce(
              (memo, [key, value]) => {
                const { key: valueKey, value: valueValue } = value

                const keyEncoded = valueKey.replace(/,/g, "|")

                const encodedValue = {
                  key: keyEncoded,
                  value: valueValue,
                }

                return {
                  ...memo,
                  [key]: encodedValue,
                }
              },
              {},
            )

            return {
              ...memo,
              [filterKey]: filterValues,
            }
          }, {})
        : {}

      return productFiltersEncoded
    },
    successCallback,
    failureCallback,
  }),

  clearProductFilters: () => {},

  getWidgetData: (params = {}, successCallback, failureCallback, dispatch) => ({
    callApi: () => {
      const state = store.getState()
      const {
        customer: { id },
      } = customerSelector(state)

      return getWidgetData(params, id)
    },
    doNotShowError: true,
    successCallback,
    failureCallback,
  }),

  // TODO: refactor this function
  getChartData: (params = {}, successCallback, failureCallback, dispatch) => ({
    callApi: async () => {
      const state = store.getState()
      const {
        customer: { id },
      } = customerSelector(state)
      const salesHistorySettings: SalesHistorySettings =
        salesHistorySettingsSelector(state)
      const salesHistoryCategoriesSelectionV1: SalesHistorySettings =
        salesHistoryCategoriesSelectionV1Selector(state)

      const hasSettings =
        !!salesHistorySettings?.id &&
        !!salesHistorySettings?.value &&
        isNonEmptyObject(salesHistorySettings.value)

      const chartData = await getChartData(params, id)

      const salesCategories: SalesHistoryCategory[] = chartData?.data
        ?.salesCategories
        ? getObjectValues(chartData.data.salesCategories)
        : []

      const { childrenCategoriesKeys, initialSettings } =
        buildInitialSalesHistorySettings({
          salesHistorySettings,
          salesCategories,
        })

      const salesHistorySettingsKeys: string[] = getObjectKeys(
        salesHistorySettings?.value || {},
      )

      const isSettingsNotEqual: boolean = compareArrayKeysWithPriority({
        keys1: salesHistorySettingsKeys,
        keys2: childrenCategoriesKeys,
      })

      const shouldUserSettingsUpdate: boolean =
        hasSettings && isSettingsNotEqual
      const shouldUserSettingsSave: boolean =
        !hasSettings && !!chartData?.data?.dataSeries

      // if user hasn't user settings save settings
      if (shouldUserSettingsSave) {
        dispatch(
          userSettingsActions.saveUserSettings(
            {
              key: USER_SETTINGS_KEYS.salesHistorySettings,
              settings: initialSettings.value,
            },
            null,
            null,
          ),
        )
      }

      // if user sales history settings hasn't category that presented in the chart data update user settings with new category
      if (shouldUserSettingsUpdate) {
        dispatch(
          userSettingsActions.updateUserSettings(
            {
              settings: initialSettings.value,
              id: salesHistorySettings.id,
            },
            null,
            null,
          ),
        )
      }

      const hasSettingsSelection: boolean =
        !!salesHistoryCategoriesSelectionV1?.id &&
        !!salesHistoryCategoriesSelectionV1?.value &&
        isNonEmptyObject(salesHistoryCategoriesSelectionV1.value)

      const salesHistoryCategoriesSelectionV1Keys: string[] = getObjectKeys(
        salesHistoryCategoriesSelectionV1?.value || {},
      )

      const isSettingsSelectionNotEqual: boolean = compareArrayKeysWithPriority(
        {
          keys1: salesHistoryCategoriesSelectionV1Keys,
          keys2: childrenCategoriesKeys,
        },
      )

      const shouldUserSettingsSelectionUpdate: boolean =
        hasSettingsSelection && isSettingsSelectionNotEqual

      if (shouldUserSettingsSelectionUpdate) {
        dispatch(
          userSettingsActions.updateUserSettings(
            {
              settings: initialSettings.value,
              id: salesHistoryCategoriesSelectionV1.id,
            },
            null,
            null,
          ),
        )

        dispatch(
          salesHistoryActions.updateCheckedCategoriesState(
            initialSettings?.value,
          ),
        )
      }

      const { defaultSettings, columns } = initialSettings

      dispatch(
        userSettingsActions.updateUserSettingsState({
          key: USER_SETTINGS_KEYS.salesHistorySettings,
          additionalSettings: {
            defaultSettings,
            columns,
          },
        }),
      )

      // If the user does not have a category state selected, update the category state with initial values
      if (!salesHistoryCategoriesSelectionV1?.value) {
        dispatch(
          userSettingsActions.saveUserSettings(
            {
              key: USER_SETTINGS_KEYS.salesHistoryCategoriesSelectionV1,
              settings: initialSettings.value,
            },
            null,
            null,
          ),
        )

        dispatch(
          salesHistoryActions.updateCheckedCategoriesState(
            initialSettings?.value,
          ),
        )
      }

      return chartData
    },
    doNotShowSpin: true,
    doNotShowError: true,
    successCallback,
    failureCallback,
  }),

  getWidgetStatisticsData: (params = {}, dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return getWidgetStatisticsData({ customerId, ...params })
    },
    doNotShowSpin: true,
  }),
}

const salesHistoryActions = generateActions(
  definitions,
  types,
) as SalesHistoryDefinitions

export { salesHistoryActions, types }
