import { withActionPrefix } from "actions/utils"

import generateActions from "utils/generateActions"

import { DateRangePickerSelected } from "types/DatePicker"

import {
  KPIWidgetsDefinitions,
  KPIWidgetsTypes,
} from "./kpiWidgetsActionsTypes"

const createActionType = withActionPrefix("kpiWidgets")

export const types: KPIWidgetsTypes = {
  setIsInitiated: createActionType("setIsInitiated"),
  setKPIWidgetsSettings: createActionType("setKPIWidgetsSettings"),
  setDateRangeByIndex: createActionType("setDateRangeByIndex"),
}

const definitions: KPIWidgetsDefinitions = {
  setIsInitiated: (payload: boolean) => payload,
  setDateRangeByIndex: ({ index, settings } = {}) => ({
    index,
    settings,
  }),
  setKPIWidgetsSettings: (payload: DateRangePickerSelected[]) => payload,
}

const kpiWidgetsActions = generateActions(
  definitions,
  types,
) as KPIWidgetsDefinitions

export { kpiWidgetsActions }
