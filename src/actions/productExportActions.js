import api from "api"

import { customerIdSelector } from "selectors/mainStateSelectors"

import { downloadFile } from "utils/downloadFile"
import generateActions from "utils/generateActions"

import { TEMPLATE_HANDLER_NAMES } from "constants/exportTemplates"

import gridActions from "./gridActions"

const actionPrefix = "productExport"

const {
  productExportService: {
    addProductExport,
    deleteProductExport,
    downloadTemplateFile,
    getProductExports,
  },
} = api

const { changeSearchOptions: updateGridSearchOptions } = gridActions

export const types = {
  changeSearchOptions: `${actionPrefix}/change_search_options`,
  displayCreateExportModal: `${actionPrefix}/display_modal`,
  addProductExport: [
    `${actionPrefix}/add_product_export_request`,
    `${actionPrefix}/add_product_export_success`,
    `${actionPrefix}/add_product_export_failure`,
  ],
  deleteItem: [
    `${actionPrefix}/delete_request`,
    `${actionPrefix}/delete_success`,
    `${actionPrefix}/delete_failure`,
  ],
  downloadTemplateFile: [
    `${actionPrefix}/download_request`,
    `${actionPrefix}/download_success`,
    `${actionPrefix}/download_failure`,
  ],
  getProductExports: [
    `${actionPrefix}/get_request`,
    `${actionPrefix}/get_success`,
    `${actionPrefix}/get_failure`,
  ],
}

const definitions = {
  addProductExport: (
    payload,
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return addProductExport({
        customerId,
        ...payload,
      })
    },
    successCallback,
    failureCallback,
  }),
  changeSearchOptions: (searchOptions) => searchOptions,
  displayCreateExportModal: (modalVisible = false) => ({
    modalVisible,
  }),
  deleteItem: ({ id }, successCallback, dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return deleteProductExport({ customerId, id })
    },
    successCallback,
  }),
  downloadTemplateFile: (payload = {}, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)
      const { blob: file, fileName } = await downloadTemplateFile({
        customerId,
        handler_name: TEMPLATE_HANDLER_NAMES.PRODUCT_COST_PERIODS,
      })

      downloadFile(fileName, file)

      return { data: {} }
    },
  }),
  getProductExports: (
    { searchOptions = {} },
    callback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      dispatch(updateGridSearchOptions(searchOptions))

      return getProductExports({
        sort: "-created_at",
        customerId,
        ...searchOptions,
      })
    },
    callback,
  }),
}

export default generateActions(definitions, types)
