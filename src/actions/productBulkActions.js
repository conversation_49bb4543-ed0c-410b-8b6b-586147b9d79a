import {
  getObjectKeys,
  getUrlSearchParams,
} from "@develop/fe-library/dist/utils"

import api from "api"

import gridActions from "actions/gridActions"

import { customerIdSelector } from "selectors/mainStateSelectors"

import generateActions from "utils/generateActions"
import { removeNullAndUndefined } from "utils/objectHelpers"

import {
  ALLOWED_KEYS,
  BULK_CHANGES_KEY,
  BULK_SELECTION_STATUSES,
} from "constants/bulkEdit"

const {
  productBulkEditService: {
    addProductBulkEditProcess,
    stopProductBulkEditProcessById,
  },
} = api

const { getProducts } = gridActions

const TYPE_PREFIX = "bulkEdit"

export const types = {
  showProductBulkEditForm: `${TYPE_PREFIX}/show_bulk_edit_form`,
  setProductBulkEditChanges: `${TYPE_PREFIX}/set_bulk_changes`,
  addProductBulkEditProcess: [
    `${TYPE_PREFIX}/add_request`,
    `${TYPE_PREFIX}/add_success`,
    `${TYPE_PREFIX}/add_failure`,
  ],
  stopProductBulkEditProcessById: [
    `${TYPE_PREFIX}/stop_by_id_request`,
    `${TYPE_PREFIX}/stop_by_id_success`,
    `${TYPE_PREFIX}/stop_by_id_failure`,
  ],
  clearProductBulkEditProcess: `${TYPE_PREFIX}/clear`,
}

let actions

const definitions = {
  showProductBulkEditForm: (isVisible = true) => isVisible,
  addProductBulkEditProcess: (
    params,
    payload,
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const filteredParams = getObjectKeys(params).reduce((acc, key) => {
        if (ALLOWED_KEYS.includes(key)) {
          return { ...acc, [key]: params[key] }
        }

        return acc
      }, {})

      const changesPayload = {
        status: payload.status
          ? {
              _status: BULK_SELECTION_STATUSES.edit,
              _title: "Product status",
            }
          : null,
        actionReasonId: payload.actionReasonId
          ? {
              _status: BULK_SELECTION_STATUSES.edit,
              _title: "Product reason for status change",
            }
          : null,
      }

      dispatch({
        type: types.setProductBulkEditChanges,
        payload: {
          [BULK_CHANGES_KEY]: removeNullAndUndefined(changesPayload),
        },
      })

      return addProductBulkEditProcess({
        payload,
        filteredParams,
        customerId,
      })
    },
    successCallback,
    failureCallback,
  }),
  stopProductBulkEditProcessById: ({ id }, dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return stopProductBulkEditProcessById({ customerId, id })
    },
  }),
  clearProductBulkEditProcess: (dispatch) => {
    const urlSearchParams = getUrlSearchParams({
      locationSearch: document.location.search,
    })

    return dispatch(getProducts(urlSearchParams))
  },
}

actions = generateActions(definitions, types)

export default actions
