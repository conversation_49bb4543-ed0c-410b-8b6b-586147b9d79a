import {
  createNewCostCategory,
  createNewPeriod,
  createProductCostItem,
  deleteCostCategory,
  deletePeriod,
  getProductCostCategories,
  getProductCostPeriod,
  getProductCostPeriods,
  getProducts,
  transferCostsForMarketplaces,
  updatePeriod,
  updatePeriodCosts,
  updateProduct,
  updateProductCostItem,
} from "api/productCostService"

import {
  amazonCustomerAccountsSelector,
  customerIdSelector,
} from "selectors/mainStateSelectors"
import {
  categoriesSelector,
  formTypeSelector,
  productSelector,
  selectedMarketplaceIdSelector,
  selectedMarketplaceSelector,
  selectedPeriodIdSelector,
} from "selectors/productCostSelectors"

import getMainApp from "components/MainAppProvider"

import { checkIsArray } from "utils/arrayHelpers"
import generateActions from "utils/generateActions"

import { DEFAULT_CURRENCY_CODE } from "constants/currencies"
import { FORM_TYPES, SALES_CATEGORY_IDS } from "constants/productCost"

const PRODUCT_PREFIX = "product_cost"

const toggleSpinner = (isVisible: boolean): void => {
  const mainApp = getMainApp()

  mainApp?.actions?.showSpiner?.(null, isVisible, 1000000)
}

export const types = {
  startEditProduct: `${PRODUCT_PREFIX}/start_edit_product`,
  endEditProduct: `${PRODUCT_PREFIX}/end_edit_product`,
  changeFormType: `${PRODUCT_PREFIX}/change_form_type`,
  changeMarketplaceTab: `${PRODUCT_PREFIX}/change_marketplace_tab`,
  startEditPeriod: `${PRODUCT_PREFIX}/start_edit_period`,
  endEditPeriod: `${PRODUCT_PREFIX}/end_edit_period`,
  openCreatePeriodModal: `${PRODUCT_PREFIX}/toggle_create_period_modal`,
  closeCreatePeriodModal: `${PRODUCT_PREFIX}/close_create_period_modal`,
  setIsFormEditing: `${PRODUCT_PREFIX}/set_is_form_editing`,
  openDisclaimerModal: `${PRODUCT_PREFIX}/open_disclaimer_modal`,
  closeDisclaimerModal: `${PRODUCT_PREFIX}/close_disclaimer_modal`,
  openTransferCostsModal: `${PRODUCT_PREFIX}/open_transfer_costs_modal`,
  closeTransferCostsModal: `${PRODUCT_PREFIX}/close_transfer_costs_modal`,
  selectShippingCostTab: `${PRODUCT_PREFIX}/select_shipping_cost_tab`,
  getCategories: [
    `${PRODUCT_PREFIX}/get_categories_request`,
    `${PRODUCT_PREFIX}/get_categories_success`,
    `${PRODUCT_PREFIX}/get_categories_failure`,
  ],
  createCategory: [
    `${PRODUCT_PREFIX}/create_category_request`,
    `${PRODUCT_PREFIX}/create_category_success`,
    `${PRODUCT_PREFIX}/create_category_failure`,
  ],
  deleteCategory: [
    `${PRODUCT_PREFIX}/delete_category_request`,
    `${PRODUCT_PREFIX}/delete_category_success`,
    `${PRODUCT_PREFIX}/delete_category_failure`,
  ],
  getProduct: [
    `${PRODUCT_PREFIX}/get_product_request`,
    `${PRODUCT_PREFIX}/get_product_success`,
    `${PRODUCT_PREFIX}/get_product_failure`,
  ],
  getProducts: [
    `${PRODUCT_PREFIX}/get_products_request`,
    `${PRODUCT_PREFIX}/get_products_success`,
    `${PRODUCT_PREFIX}/get_products_failure`,
  ],
  setPeriodsLoading: `${PRODUCT_PREFIX}/set_periods_loading`,
  getPeriods: [
    `${PRODUCT_PREFIX}/get_periods_request`,
    `${PRODUCT_PREFIX}/get_periods_success`,
    `${PRODUCT_PREFIX}/get_periods_failure`,
  ],
  getPeriod: [
    `${PRODUCT_PREFIX}/get_period_request`,
    `${PRODUCT_PREFIX}/get_period_success`,
    `${PRODUCT_PREFIX}/get_period_failure`,
  ],
  createPeriod: [
    `${PRODUCT_PREFIX}/create_period_request`,
    `${PRODUCT_PREFIX}/create_period_success`,
    `${PRODUCT_PREFIX}/create_period_failure`,
  ],
  updatePeriod: [
    `${PRODUCT_PREFIX}/update_period_request`,
    `${PRODUCT_PREFIX}/update_period_success`,
    `${PRODUCT_PREFIX}/update_period_failure`,
  ],
  deletePeriod: [
    `${PRODUCT_PREFIX}/delete_period_request`,
    `${PRODUCT_PREFIX}/delete_period_success`,
    `${PRODUCT_PREFIX}/delete_period_failure`,
  ],
  updateProductCosts: [
    `${PRODUCT_PREFIX}/update_product_costs_request`,
    `${PRODUCT_PREFIX}/update_product_costs_success`,
    `${PRODUCT_PREFIX}/update_product_costs_failure`,
  ],
  createProductCost: [
    `${PRODUCT_PREFIX}/create_product_cost_request`,
    `${PRODUCT_PREFIX}/create_product_cost_success`,
    `${PRODUCT_PREFIX}/create_product_cost_failure`,
  ],
  updateProductCost: [
    `${PRODUCT_PREFIX}/update_product_cost_request`,
    `${PRODUCT_PREFIX}/update_product_cost_success`,
    `${PRODUCT_PREFIX}/update_product_cost_failure`,
  ],
  transferCostsForMarketplaces: [
    `${PRODUCT_PREFIX}/transfer_costs_for_marketplaces_request`,
    `${PRODUCT_PREFIX}/transfer_costs_for_marketplaces_success`,
    `${PRODUCT_PREFIX}/transfer_costs_for_marketplaces_failure`,
  ],
  synchronizeWithRepricer: [
    `${PRODUCT_PREFIX}/synchronize_with_repricer_request`,
    `${PRODUCT_PREFIX}/synchronize_with_repricer_success`,
    `${PRODUCT_PREFIX}/synchronize_with_repricer_failure`,
  ],
  toggleLinkOnProduct: [
    `${PRODUCT_PREFIX}/toggle_link_on_product_request`,
    `${PRODUCT_PREFIX}/toggle_link_on_product_success`,
    `${PRODUCT_PREFIX}/toggle_link_on_product_failure`,
  ],
}

let actions

const definitions = {
  startEditProduct: ({ formType, product, order }, dispatch, getState) => {
    dispatch({
      type: types.getCategories,
      payload: definitions.getCategories(formType, dispatch, getState),
    })

    dispatch({
      type: types.getProducts,
      payload: definitions.getProducts(
        {
          formType,
          sku: product.sku,
          selectedMarketplaceId: product.marketplace_id,
        },
        dispatch,
        getState,
      ),
    })

    const shouldSelectOrderShippingCostTab: boolean =
      formType === FORM_TYPES.shipping_cost && !!order

    if (shouldSelectOrderShippingCostTab) {
      dispatch({
        type: types.selectShippingCostTab,
        payload: "order",
      })
    }

    return {
      formType,
      product,
      order,
    }
  },
  endEditProduct: () => ({}),
  changeFormType: (formType, dispatch, getState) => {
    const state = getState()
    const selectedMarketplaceId = selectedMarketplaceIdSelector(state)
    const { sku, marketplace_id } = productSelector(state) || {}

    dispatch({
      type: types.getCategories,
      payload: definitions.getCategories(formType, dispatch, getState),
    })

    dispatch({
      type: types.getProducts,
      payload: definitions.getProducts(
        {
          formType,
          sku: sku,
          selectedMarketplaceId: marketplace_id,
        },
        dispatch,
        getState,
      ),
    })

    dispatch({
      type: types.changeMarketplaceTab,
      payload: definitions.changeMarketplaceTab(
        {
          formType,
          marketplaceId: selectedMarketplaceId,
        },
        dispatch,
        getState,
      ),
    })

    if (formType === FORM_TYPES.shipping_cost) {
      dispatch({
        type: types.selectShippingCostTab,
        payload: "product",
      })
    }

    return formType
  },
  changeMarketplaceTab: ({ formType, marketplaceId }, dispatch, getState) => {
    if (formType !== FORM_TYPES.synchronize_with_repricer) {
      dispatch({
        type: types.setPeriodsLoading,
        payload: true,
      })
      dispatch({
        type: types.getPeriods,
        payload: definitions.getPeriods(
          { formType, marketplaceId },
          dispatch,
          getState,
        ),
      })
    }

    return marketplaceId
  },
  startEditPeriod: (periodId) => {
    return periodId
  },
  endEditPeriod: () => ({}),
  openCreatePeriodModal: () => ({}),
  closeCreatePeriodModal: () => ({}),
  setIsFormEditing: (isFormEditing) => isFormEditing,
  openDisclaimerModal: () => ({}),
  closeDisclaimerModal: () => ({}),
  openTransferCostsModal: () => ({}),
  closeTransferCostsModal: () => ({}),
  selectShippingCostTab: (tabKey) => tabKey,
  getCategories: (formType, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const productCostCategories = await getProductCostCategories({
        customerId,
        sales_category_id: SALES_CATEGORY_IDS[formType],
      })

      if (!productCostCategories?.data?.data) {
        return []
      }

      return productCostCategories?.data?.data
    },
  }),
  createCategory: (values, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const category = await createNewCostCategory({
        values: {
          ...values,
          source: "",
          is_default: false,
        },
        customerId,
      })

      if (!category?.data) {
        return null
      }

      return category?.data
    },
  }),
  deleteCategory: (id, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      await deleteCostCategory({
        costCategoryId: id,
        customerId,
      })

      return id
    },
  }),
  getProduct: (id, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()

      const customerId = customerIdSelector(state)

      const products = await getProducts({
        customerId,
        id,
      })

      if (!products?.data?.data?.[0]) {
        return null
      }

      return products?.data?.data?.[0]
    },
  }),
  getProducts: (
    { formType, sku, selectedMarketplaceId },
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()

      const customerId = customerIdSelector(state)

      const { amazonCustomerAccounts } = amazonCustomerAccountsSelector(state)
      const [firstAmazonAccount] =
        amazonCustomerAccounts?.filter(({ active }) => !!active) || []

      const homeMarketplaceId = firstAmazonAccount?.home_marketplace_id

      const products = await getProducts({ customerId, exact_sku: sku })

      const marketplaces = checkIsArray(products?.data?.data)
        ? products?.data?.data?.reduce((acc, product) => {
            return {
              ...acc,
              [product.marketplace_id]: product,
            }
          }, {})
        : {}

      dispatch({
        type: types.changeMarketplaceTab,
        payload: definitions.changeMarketplaceTab(
          {
            formType,
            marketplaceId: selectedMarketplaceId,
          },
          dispatch,
          getState,
        ),
      })

      return {
        marketplaces,
        homeMarketplaceId,
        selectedMarketplaceId,
      }
    },
  }),
  setPeriodsLoading: (isLoading) => isLoading,
  getPeriods: ({ formType, marketplaceId }, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)
      const { sku } = productSelector(state) || {}

      const periods = await getProductCostPeriods({
        sort: "-date_start",
        customerId,
        seller_sku: sku,
        marketplace_id: marketplaceId,
        sales_category_id: SALES_CATEGORY_IDS[formType],
      })

      if (!checkIsArray(periods?.data?.data)) {
        return []
      }

      return periods.data.data
    },
  }),
  getPeriod: (id, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return await getProductCostPeriod({ customerId, periodId: id })
    },
  }),
  createPeriod: ({ date_start }, successCallback, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)
      const formType = formTypeSelector(state)
      const { marketplace_id, sku, seller_id } =
        selectedMarketplaceSelector(state) || {}

      toggleSpinner(true)

      await createNewPeriod({
        customerId,
        values: {
          marketplace_id,
          seller_sku: sku,
          seller_id,
          sales_category_id: SALES_CATEGORY_IDS[formType],
          date_start,
        },
      })

      await dispatch({
        type: types.getPeriods,
        payload: definitions.getPeriods(
          {
            formType,
            marketplaceId: marketplace_id,
          },
          dispatch,
          getState,
        ),
      })

      toggleSpinner(false)

      return {}
    },
    successCallback,
  }),
  updatePeriod: ({ id, date_start }, successCallback, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)
      const formType = formTypeSelector(state)
      const { marketplace_id } = selectedMarketplaceSelector(state) || {}

      await updatePeriod({
        customerId,
        periodId: id,
        values: {
          date_start,
          sales_category_id: SALES_CATEGORY_IDS[formType],
        },
      })

      await dispatch({
        type: types.getPeriods,
        payload: definitions.getPeriods(
          {
            formType,
            marketplaceId: marketplace_id,
          },
          dispatch,
          getState,
        ),
      })

      return {}
    },
    successCallback,
  }),
  deletePeriod: (id, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)
      const formType = formTypeSelector(state)
      const marketplaceId = selectedMarketplaceIdSelector(state)

      toggleSpinner(true)

      await deletePeriod({
        customerId,
        id,
      })

      await dispatch({
        type: types.getPeriods,
        payload: definitions.getPeriods(
          {
            formType,
            marketplaceId,
          },
          dispatch,
          getState,
        ),
      })

      toggleSpinner(false)

      return {}
    },
  }),
  createProductCost: (
    { amount_total },
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()

      const customerId = customerIdSelector(state)
      const formType = formTypeSelector(state)
      const { currency_code } = selectedMarketplaceSelector(state) || {}
      const categories = categoriesSelector(state)
      const periodId = selectedPeriodIdSelector(state)

      const costsCategory = categories.find((category) => {
        return category.sales_category_id === SALES_CATEGORY_IDS[formType]
      })

      toggleSpinner(true)

      await createProductCostItem({
        values: {
          amount_total,
          product_cost_period_id: periodId,
          product_cost_category_id: costsCategory?.id,
          units: 1,
          currency_id: currency_code || DEFAULT_CURRENCY_CODE,
          marketplace_currency_rate: 1,
        },
        customerId,
      })

      await dispatch({
        type: types.getPeriod,
        payload: definitions.getPeriod(periodId, dispatch, getState),
      })

      toggleSpinner(false)

      return {}
    },
    successCallback,
    failureCallback: (errors) => {
      toggleSpinner(false)
      failureCallback(errors)
    },
  }),
  updateProductCosts: (
    { payload: { id, items }, successCallback, failureCallback },
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)
      const periodId = selectedPeriodIdSelector(state)

      toggleSpinner(true)

      await updatePeriodCosts({
        costs: items,
        customerId,
        periodId: id,
      })

      await dispatch({
        type: types.getPeriod,
        payload: definitions.getPeriod(periodId, dispatch, getState),
      })

      dispatch({
        type: types.setIsFormEditing,
        payload: false,
      })

      toggleSpinner(false)

      return {}
    },
    successCallback,
    failureCallback: (errors) => {
      toggleSpinner(false)
      failureCallback(errors)
    },
  }),
  updateProductCost: (
    values,
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)
      const periodId = selectedPeriodIdSelector(state)

      toggleSpinner(true)

      await updateProductCostItem({
        customerId,
        values,
      })

      await dispatch({
        type: types.getPeriod,
        payload: definitions.getPeriod(periodId, dispatch, getState),
      })

      toggleSpinner(false)

      return {}
    },
    successCallback,
    failureCallback: (errors) => {
      toggleSpinner(false)
      failureCallback(errors)
    },
  }),
  transferCostsForMarketplaces: (
    { target_marketplace_ids },
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)
      const formType = formTypeSelector(state)
      const { marketplace_id, sku, seller_id } =
        selectedMarketplaceSelector(state) || {}

      await transferCostsForMarketplaces({
        customerId,
        marketplace_id,
        seller_id,
        seller_sku: sku,
        target_marketplace_ids,
        sales_category_id: SALES_CATEGORY_IDS[formType],
      })

      await dispatch({
        type: types.getProducts,
        payload: definitions.getProducts(
          {
            formType,
            sku,
            selectedMarketplaceId: marketplace_id,
          },
          dispatch,
          getState,
        ),
      })

      dispatch({
        type: types.closeTransferCostsModal,
      })

      return {}
    },
  }),
  synchronizeWithRepricer: (
    { id, is_enabled_sync_with_repricer },
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const payload = {
        productId: id,
        customerId,
        values: {
          is_enabled_sync_with_repricer,
        },
      }

      await updateProduct(payload)

      await dispatch({
        type: types.getProduct,
        payload: definitions.getProduct(id, dispatch, getState),
      })

      return {}
    },
  }),
  toggleLinkOnProduct: (
    { id, is_enabled_sync_with_global_marketplace },
    dispatch,
    getState,
  ) => {
    const state = getState()
    const customerId = customerIdSelector(state)

    return {
      callApi: async () => {
        const payload = {
          productId: id,
          customerId,
          values: {
            is_enabled_sync_with_global_marketplace,
          },
        }

        await updateProduct(payload)

        await dispatch({
          type: types.getProduct,
          payload: definitions.getProduct(id, dispatch, getState),
        })

        return {}
      },
    }
  },
}

actions = generateActions(definitions, types)

export default actions
