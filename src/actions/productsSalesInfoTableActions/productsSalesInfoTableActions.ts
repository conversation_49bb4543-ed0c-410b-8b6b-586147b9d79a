import api from "api"

import { ProductsSalesInfoTableInitialData } from "initialState/productsSalesInfoTable"

import { withActionPrefix } from "actions/utils"

import { customerIdSelector } from "selectors/mainStateSelectors"

import generateActions from "utils/generateActions"

import { GridGeneral, ProductAggregatedSalesInfo } from "types"

import {
  ProductsSalesInfoTableDefinitions,
  ProductsSalesInfoTableTypes,
} from "./productsSalesInfoTableActionsTypes"

const {
  productAggregatedSalesInfoService: { getProductAggregatedSalesInfo },
} = api

const createActionType = withActionPrefix("productsSalesInfoTable")

export const types: ProductsSalesInfoTableTypes = {
  changeSearchOptions: createActionType("changeSearchOptions"),
  getProductsAggregatedSalesInfo: createActionType(
    "getProductsAggregatedSalesInfo",
    true,
  ),
  clearProductsSalesInfoTableData: createActionType(
    "clearProductsSalesInfoTableData",
  ),
}

let productsSalesInfoTableActions: ProductsSalesInfoTableDefinitions

const definitions: ProductsSalesInfoTableDefinitions = {
  changeSearchOptions: (searchOptions) => searchOptions,
  getProductsAggregatedSalesInfo: (
    { params = {}, successCallback, failureCallback },
    dispatch,
    getState,
  ) => ({
    callApi: async (): Promise<GridGeneral<ProductAggregatedSalesInfo[]>> => {
      const state = getState?.()
      const customerId = customerIdSelector(state)

      const response = await getProductAggregatedSalesInfo({
        params,
        customerId,
      })

      return (
        Array.isArray(response?.data?.data)
          ? response
          : { data: ProductsSalesInfoTableInitialData }
      ) as GridGeneral<ProductAggregatedSalesInfo[]>
    },
    successCallback,
    failureCallback,
  }),
  clearProductsSalesInfoTableData: () => {},
}

productsSalesInfoTableActions = generateActions(
  definitions,
  types,
) as ProductsSalesInfoTableDefinitions

export { productsSalesInfoTableActions }
