import { ProductAggregatedSalesInfoRequestParams } from "types"

import {
  ActionAbstractDefinitionType,
  ActionAsyncDefinitionType,
  ActionsTypes,
} from "actions/types"

export type ProductsSalesInfoTableActionsAsyncTypeNames =
  "getProductsAggregatedSalesInfo"

export type ProductsSalesInfoTableActionsSyncTypeNames =
  | "changeSearchOptions"
  | "clearProductsSalesInfoTableData"

export type ProductsSalesInfoTableTypes = ActionsTypes<
  ProductsSalesInfoTableActionsAsyncTypeNames,
  ProductsSalesInfoTableActionsSyncTypeNames
>

export type ProductsSalesInfoTableDefinitions = {
  getProductsAggregatedSalesInfo: ActionAsyncDefinitionType<ProductAggregatedSalesInfoRequestParams>
  changeSearchOptions: ActionAbstractDefinitionType
  clearProductsSalesInfoTableData: ActionAbstractDefinitionType
}
