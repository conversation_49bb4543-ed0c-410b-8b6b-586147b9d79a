import api from "api"

import { customerSelector } from "selectors/mainStateSelectors"

import generateActions from "utils/generateActions"

const actionPrefix = "recurrentExportSettings"

const {
  recurrentExportSettingsService: {
    getRecurrentExportSettings,
    addRecurrentExportSetting,
    updateRecurrentExportSetting,
    deleteRecurrentExportSetting,
  },
} = api

export const types = {
  getRecurrentExportSettings: [
    `${actionPrefix}/get_request`,
    `${actionPrefix}/get_success`,
    `${actionPrefix}/get_failure`,
  ],
  addRecurrentExportSetting: [
    `${actionPrefix}/add_request`,
    `${actionPrefix}/add_success`,
    `${actionPrefix}/add_failure`,
  ],
  updateRecurrentExportSetting: [
    `${actionPrefix}/update_request`,
    `${actionPrefix}/update_success`,
    `${actionPrefix}/update_failure`,
  ],
  changeStatusForRecurrentExportSetting: [
    `${actionPrefix}/change_status_request`,
    `${actionPrefix}/change_status_success`,
    `${actionPrefix}/change_status_failure`,
  ],
  deleteRecurrentExportSetting: [
    `${actionPrefix}/delete_request`,
    `${actionPrefix}/delete_success`,
    `${actionPrefix}/delete_failure`,
  ],
  displayRecurrentExportSettingModal: `${actionPrefix}/display_modal`,
  showRecurrentExportSettingConfirmationModal: `${actionPrefix}/show_confirmation_modal`,
}

const definitions = {
  getRecurrentExportSettings: (successCallback, dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const {
        customer: { id: customerId },
      } = customerSelector(state)

      return getRecurrentExportSettings({ customerId })
    },
    successCallback,
  }),
  addRecurrentExportSetting: (
    payload,
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const {
        customer: { id: customerId },
      } = customerSelector(state)

      return addRecurrentExportSetting({
        customerId,
        payload,
      })
    },
    successCallback: (data) => {
      dispatch({
        type: types.getRecurrentExportSettings,
        payload: {
          ...definitions.getRecurrentExportSettings(
            () => successCallback(data),
            dispatch,
            getState,
          ),
        },
      })
    },
    failureCallback,
  }),
  updateRecurrentExportSetting: (
    payload,
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const {
        customer: { id: customerId },
      } = customerSelector(state)

      return updateRecurrentExportSetting({
        customerId,
        id: payload.id,
        payload: {
          ...payload,
          is_enabled: !payload.is_enabled ? 0 : 1,
        },
      })
    },
    successCallback,
    failureCallback,
  }),
  changeStatusForRecurrentExportSetting: (
    payload,
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const {
        customer: { id: customerId },
      } = customerSelector(state)

      return updateRecurrentExportSetting({
        customerId,
        id: payload.id,
        payload: {
          ...payload,
          is_enabled: !payload.is_enabled ? 1 : 0,
        },
      })
    },
    successCallback,
    failureCallback,
  }),
  deleteRecurrentExportSetting: (id, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const {
        customer: { id: customerId },
      } = customerSelector(state)

      await deleteRecurrentExportSetting({ customerId, id })

      return {
        data: id,
      }
    },
  }),
  displayRecurrentExportSettingModal: (modalVisible = false) => ({
    modalVisible,
  }),
  showRecurrentExportSettingConfirmationModal: (
    confirmationModalVisible = false,
  ) => ({
    confirmationModalVisible,
  }),
}

export default generateActions(definitions, types)
