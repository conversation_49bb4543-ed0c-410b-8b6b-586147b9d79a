import api from "api"

import { customerIdSelector } from "selectors/mainStateSelectors"

import setConfirm from "utils/confirm"
import generateActions from "utils/generateActions"
import l from "utils/intl"

const {
  productTagsService: {
    getProductTags,
    createProductTag,
    updateProductTag,
    deleteProductTag,
  },
} = api

const actionPrefix = "productTags"

export const types = {
  displayCreateProductTagModal: `${actionPrefix}/display_create_product_tag_modal`,
  displayUpdateEditProductTagModal: `${actionPrefix}/display_update_product_tag_modal`,
  displayDeleteProductTagModal: `${actionPrefix}/display_delete_product_tag_modal`,
  getProductTags: [
    `${actionPrefix}/get_all_product_tags_request`,
    `${actionPrefix}/get_all_product_tags_success`,
    `${actionPrefix}/get_all_product_tags_failure`,
  ],
  createProductTag: [
    `${actionPrefix}/create_product_tag_request`,
    `${actionPrefix}/create_product_tag_success`,
    `${actionPrefix}/create_product_tag_failure`,
  ],
  deleteProductTag: [
    `${actionPrefix}/delete_product_tag_request`,
    `${actionPrefix}/delete_product_tag_success`,
    `${actionPrefix}/delete_product_tag_failure`,
  ],
  updateProductTag: [
    `${actionPrefix}/update_product_tag_request`,
    `${actionPrefix}/update_product_tag_success`,
    `${actionPrefix}/update_product_tag_failure`,
  ],
}

const definitions = {
  displayCreateProductTagModal: (
    isCreateProductTagModalVisible = false,
    initialValues = {},
  ) => ({
    isCreateProductTagModalVisible,
    initialValues,
  }),
  displayUpdateEditProductTagModal: (
    isUpdateProductTagModalVisible = false,
    initialValues,
  ) => ({
    isUpdateProductTagModalVisible,
    initialValues,
  }),
  displayDeleteProductTagModal: (
    isDeleteProductTagModalVisible = false,
    initialValues,
  ) => ({
    isDeleteProductTagModalVisible,
    initialValues,
  }),
  getProductTags: (params = {}, dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return getProductTags({ customerId, all: 1, ...params })
    },
  }),
  createProductTag: (
    { payload, successCallback, failureCallback },
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return createProductTag({ customerId, payload })
    },
    successCallback,
    failureCallback,
  }),
  updateProductTag: (
    { payload, successCallback, failureCallback },
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return updateProductTag({ customerId, id: payload.id, payload })
    },
    successCallback,
    failureCallback,
  }),
  deleteProductTag: ({ id, successCallback }, dispatch, getState) => ({
    callApi: async () => {
      try {
        const state = getState()
        const customerId = customerIdSelector(state)

        await deleteProductTag({ customerId, id })
      } catch ({ status, payload }) {
        const { message } = payload || {}

        if (status === 405) {
          setConfirm({
            title: l("Delete unavailable"),
            message,
            onOk: () => null,
            cancelButtonProps: { style: { display: "none" } },
            okText: l("Close"),
          })
        }
      }

      return {
        data: {},
      }
    },
    successCallback,
  }),
}

let actions

actions = generateActions(definitions, types)

export default actions
