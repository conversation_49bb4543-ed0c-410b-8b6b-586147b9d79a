import api from "api"

import { withActionPrefix } from "actions/utils"

import { customerIdSelector } from "selectors/mainStateSelectors"

import generateActions from "utils/generateActions"

import {
  OperationalDashboardDefinitions,
  OperationalDashboardTypes,
} from "./operationalDashboardActionsTypes"

const createActionType = withActionPrefix("operationalDashboard")

const {
  operationalDashboardService: {
    getDataCompletenessFactors,
    getDataCompletenessReferralFeeChanges,
    getDataCompletenessFbaFulfillmentFeeChanges,
    getDataCompletenessAdjustmentToFees,
  },
} = api

export const types: OperationalDashboardTypes = {
  getDataCompletenessFactors: createActionType(
    "getDataCompletenessFactors",
    true,
  ),
  getDataCompletenessReferralFeeChanges: createActionType(
    "getDataCompletenessReferralFeeChanges",
    true,
  ),
  getDataCompletenessFbaFulfillmentFeeChanges: createActionType(
    "getDataCompletenessFbaFulfillmentFeeChanges",
    true,
  ),
  getDataCompletenessAdjustmentToFees: createActionType(
    "getDataCompletenessAdjustmentToFees",
    true,
  ),
  openDataReassemblyModal: createActionType("openDataReassemblyModal"),
  openHistoricalDataLoadModal: createActionType("openHistoricalDataLoadModal"),
  openReferralFeeChangesDrawer: createActionType(
    "openReferralFeeChangesDrawer",
  ),
  openFbaFulfillmentFeeChangesDrawer: createActionType(
    "openFbaFulfillmentFeeChangesDrawer",
  ),
  openAdjustmentToFeesDrawer: createActionType("openAdjustmentToFeesDrawer"),
  setAdditionalDataModal: createActionType("setAdditionalDataModal"),
  clearOperationalDashboardData: createActionType(
    "clearOperationalDashboardData",
  ),
  clearOperationalDashboardDrawersData: createActionType(
    "clearOperationalDashboardDrawersData",
  ),
}

const definitions: OperationalDashboardDefinitions = {
  getDataCompletenessFactors: (dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const response = await getDataCompletenessFactors(customerId)

      return response
    },
  }),
  getDataCompletenessReferralFeeChanges: (
    { currentPage },
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const response = await getDataCompletenessReferralFeeChanges({
        customerId,
        pageSize: 25,
        page: currentPage,
      })

      return response
    },
  }),
  getDataCompletenessFbaFulfillmentFeeChanges: (
    { currentPage },
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const response = await getDataCompletenessFbaFulfillmentFeeChanges({
        customerId,
        pageSize: 25,
        page: currentPage,
      })

      return response
    },
  }),
  getDataCompletenessAdjustmentToFees: (
    { currentPage },
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const response = await getDataCompletenessAdjustmentToFees({
        customerId,
        pageSize: 25,
        page: currentPage,
      })

      return response
    },
  }),
  openDataReassemblyModal: (isVisibleDataReassemblyModal: boolean) => {
    return isVisibleDataReassemblyModal
  },
  openHistoricalDataLoadModal: (isVisibleHistoricalDataLoadModal: boolean) => {
    return isVisibleHistoricalDataLoadModal
  },
  openReferralFeeChangesDrawer: (
    isVisibleReferralFeeChangesDrawer: boolean,
  ) => {
    return isVisibleReferralFeeChangesDrawer
  },
  openFbaFulfillmentFeeChangesDrawer: (
    isVisibleFbaFulfillmentFeeChangesDrawer: boolean,
  ) => {
    return isVisibleFbaFulfillmentFeeChangesDrawer
  },
  openAdjustmentToFeesDrawer: (isVisibleAdjustmentToFeesDrawer: boolean) => {
    return isVisibleAdjustmentToFeesDrawer
  },
  setAdditionalDataModal: (additionalDataModal) => {
    return additionalDataModal
  },
  clearOperationalDashboardData: () => {},
  clearOperationalDashboardDrawersData: () => {},
}

const operationalDashboardActions = generateActions(
  definitions,
  types,
) as OperationalDashboardDefinitions

export { operationalDashboardActions }
