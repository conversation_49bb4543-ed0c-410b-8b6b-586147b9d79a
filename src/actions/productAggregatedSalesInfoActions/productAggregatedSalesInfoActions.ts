import api from "api"

import { ProductsSalesInfoTableInitialData } from "initialState/productsSalesInfoTable"

import { withActionPrefix } from "actions/utils"

import { customerIdSelector } from "selectors/mainStateSelectors"

import generateActions from "utils/generateActions"
import {
  buildTableSettingsActionDefinitions,
  buildTableSettingsActionTypes,
} from "utils/tableSettingsHelpers"

import { PRODUCTS_AGGREGATED_SALES_INFO_TABLE_SETTINGS_KEY } from "constants/productAggregatedSalesInfo"

import { GridGeneral, ProductAggregatedSalesInfo } from "types"

import {
  ProductAggregatedSalesInfoDefinitions,
  ProductAggregatedSalesInfoTypes,
} from "./ProductAggregatedSalesInfoActionsTypes"

const {
  productAggregatedSalesInfoService: { getProductAggregatedSalesInfo },
} = api

export const PREFIX = "productAggregatedSalesInfo"

const createActionType = withActionPrefix(PREFIX)

// Use of ActionsTypes causes a strange error when not including sync types.
// TODO: check and fix.
// @ts-expect-error
export const types: ProductAggregatedSalesInfoTypes = {
  getProductAggregatedSalesInfo: createActionType(
    "getProductAggregatedSalesInfo",
    true,
  ),
  clear: createActionType("clear"),
  ...buildTableSettingsActionTypes(PREFIX),
}

const definitions: ProductAggregatedSalesInfoDefinitions = {
  getProductAggregatedSalesInfo: (
    { params = {}, successCallback = () => {}, failureCallback = () => {} },
    dispatch,
    getState,
  ) => ({
    callApi: async (): Promise<GridGeneral<ProductAggregatedSalesInfo[]>> => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const response = await getProductAggregatedSalesInfo({
        // TODO: change getProductAggregatedSalesInfo types when old table is removed
        // @ts-expect-error
        params,
        customerId,
      })

      return (
        Array.isArray(response?.data?.data)
          ? response
          : { data: ProductsSalesInfoTableInitialData }
      ) as GridGeneral<Array<ProductAggregatedSalesInfo>>
    },
    successCallback,
    failureCallback,
  }),
  ...buildTableSettingsActionDefinitions({
    storeKey: PREFIX,
    tableSettingsKey: PRODUCTS_AGGREGATED_SALES_INFO_TABLE_SETTINGS_KEY,
  }),
}

const productAggregatedSalesInfoActions = generateActions(
  definitions,
  types,
) as ProductAggregatedSalesInfoDefinitions

export { productAggregatedSalesInfoActions }
