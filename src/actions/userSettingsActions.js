import api from "api"
import generateActions from "utils/generateActions"

import { mainFilterPageSettingsSelector } from "selectors/userSettingsSelectors"
import { isCustomerUserSelector } from "selectors/userSelectors"
import { USER_SETTINGS_KEYS } from "constants/user"

const actionPrefix = "user_setting"
const { userSettingsService } = api

const {
  getUserSettings,
  saveUserSettings,
  updateUserSettings,
  deleteUserSettings,
} = userSettingsService

export const types = {
  setConfirmationModalVisible: `${actionPrefix}/set_confirmation_modal`,
  getUserSettings: [
    `${actionPrefix}/get_user_settings_request`,
    `${actionPrefix}/get_user_settings_success`,
    `${actionPrefix}/get_user_settings_failure`,
  ],
  saveUserSettings: [
    `${actionPrefix}/save_user_settings_request`,
    `${actionPrefix}/save_user_settings_success`,
    `${actionPrefix}/save_user_settings_failure`,
  ],
  updateUserSettings: [
    `${actionPrefix}/update_user_settings_request`,
    `${actionPrefix}/update_user_settings_success`,
    `${actionPrefix}/update_user_settings_failure`,
  ],
  deleteUserSettings: [
    `${actionPrefix}/delete_user_setting_request`,
    `${actionPrefix}/delete_user_setting_success`,
    `${actionPrefix}/delete_user_setting_failure`,
  ],
  updateUserSettingsState: `${actionPrefix}/update_user_settings_state`,
  updateMainFilterPageSettings: `${actionPrefix}/update_filter_page_settings`,
}

/**
 * The actions object.
 *
 * @type {Object}
 * @property {Function} getUserSettings
 * @property {Function} saveUserSettings
 * @property {Function} updateUserSettings
 * @property {Function} deleteUserSettings
 *
 * @property {Function} setConfirmationModalVisible
 * @property {Function} updateUserSettingsState
 * @property {Function} updateMainFilterPageSettings
 */
let actions

const definitions = {
  setConfirmationModalVisible: (params) => params,
  getUserSettings: (
    { key, defaultSettings },
    successCallback,
    failureCallback,
  ) => ({
    callApi: async () => {
      const settings = (await getUserSettings(key)).data[0] || {}

      return {
        data: {
          key,
          settings: {
            id: settings.id,
            value: settings.value
              ? JSON.parse(settings.value)
              : defaultSettings,
          },
        },
      }
    },
    successCallback,
    failureCallback,
  }),
  saveUserSettings: (
    { key, settings } = {},
    successCallback,
    failureCallback,
  ) => ({
    callApi: () => saveUserSettings({ key, settings }),
    successCallback,
    failureCallback,
  }),
  updateUserSettings: (
    { id, settings } = {},
    successCallback,
    failureCallback,
  ) => ({
    callApi: () => updateUserSettings({ id, settings }),
    successCallback,
    failureCallback,
  }),
  deleteUserSettings: (id) => ({
    callApi: () => deleteUserSettings(id),
  }),
  updateUserSettingsState: (params) => params,
  updateMainFilterPageSettings: ({ params, page }, dispatch, getState) => {
    const state = getState()
    const mainFilterPageSettings = mainFilterPageSettingsSelector(state)
    const isCustomerUser = isCustomerUserSelector(state)

    if (!isCustomerUser) {
      return {}
    }

    let settings = {}

    if (mainFilterPageSettings?.id) {
      settings = {
        ...mainFilterPageSettings.value,
        [page]: {
          ...mainFilterPageSettings.value[page],
          ...params,
        },
      }

      dispatch(
        actions.updateUserSettings(
          {
            id: mainFilterPageSettings?.id,
            settings,
          },
          null,
          null,
        ),
      )
    } else {
      settings = {
        [page]: params,
      }

      dispatch(
        actions.saveUserSettings(
          {
            key: USER_SETTINGS_KEYS.mainFilterPageSettings,
            settings,
          },
          null,
          null,
        ),
      )
    }

    return settings
  },
}

actions = generateActions(definitions, types)

export default actions
