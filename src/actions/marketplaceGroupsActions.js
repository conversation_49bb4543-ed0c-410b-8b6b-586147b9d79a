import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import api from "api"

import generateActions from "utils/generateActions"

import getMainApp from "components/MainAppProvider"

import { isEmpty } from "lodash"

const { marketplaceService } = api
const actionPrefix = "userMarketplaceGroup"

export const types = {
  changeSearchOptions: `${actionPrefix}/change_search_options`,
  setEditGroupsInitialPage: `${actionPrefix}/set_initial_page`,

  getAllMarketplaceGroups: [
    `${actionPrefix}/get_request`,
    `${actionPrefix}/get_success`,
    `${actionPrefix}/get_failure`,
  ],

  add: [
    `${actionPrefix}/add_request`,
    `${actionPrefix}/add_success`,
    `${actionPrefix}/add_failure`,
  ],

  update: [
    `${actionPrefix}/update_request`,
    `${actionPrefix}/update_success`,
    `${actionPrefix}/update_failure`,
  ],

  deleteGroup: [
    `${actionPrefix}/delete_request`,
    `${actionPrefix}/delete_success`,
    `${actionPrefix}/delete_failure`,
  ],
}

let actions

const definitions = {
  changeSearchOptions: (searchOptions) => searchOptions,
  setEditGroupsInitialPage: (url) => url,

  getAllMarketplaceGroups: (searchOptions = {}, dispatch) => ({
    callApi: () => {
      dispatch(actions.changeSearchOptions(searchOptions))

      if (!isEmpty(searchOptions)) {
        const { actions: mainAppActions } = getMainApp()

        mainAppActions?.pushUrl?.(
          getUrlSearchParamsString({ params: searchOptions }),
        )
      }

      const { currentPage, page, pageSize, ...restParams } = searchOptions

      return marketplaceService.getMarketplaceGroups(restParams)
    },
  }),

  add: (payload = {}, successCallback, failureCallback) => ({
    callApi: () => {
      return marketplaceService.addMarketplaceGroup(payload)
    },
    successCallback,
    failureCallback,
  }),

  update: (payload = {}, successCallback, failureCallback) => ({
    callApi: () => {
      return marketplaceService.updateMarketplaceGroup(payload)
    },
    successCallback,
    failureCallback,
  }),

  deleteGroup: (payload = {}, successCallback, failureCallback) => ({
    callApi: () => {
      return new Promise(async (resolve, reject) => {
        try {
          await marketplaceService.deleteMarketplaceGroup(payload.id)

          resolve({ data: { id: payload.id } })
        } catch (e) {
          reject(e)
        }
      })
    },
    successCallback,
    failureCallback,
  }),
}

actions = generateActions(definitions, types)

export default actions
