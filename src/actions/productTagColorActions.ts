import api from "api"

import { customerIdSelector } from "selectors/mainStateSelectors"

import setConfirm from "utils/confirm"
import generateActions from "utils/generateActions"
import l from "utils/intl"

const {
  productTagColorService: {
    getProductTagColors,
    createProductTagColor,
    deleteProductTagColor,
  },
} = api

const actionPrefix = "productTagColors"

export const types = {
  getProductTagColors: [
    `${actionPrefix}/get_product_tag_colors_request`,
    `${actionPrefix}/get_product_tag_colors_success`,
    `${actionPrefix}/get_product_tag_colors_failure`,
  ],
  createProductTagColor: [
    `${actionPrefix}/create_product_tag_color_request`,
    `${actionPrefix}/create_product_tag_color_success`,
    `${actionPrefix}/create_product_tag_color_failure`,
  ],
  deleteProductTagColor: [
    `${actionPrefix}/delete_product_tag_color_request`,
    `${actionPrefix}/delete_product_tag_color_success`,
    `${actionPrefix}/delete_product_tag_color_failure`,
  ],
}

const definitions = {
  getProductTagColors: (params = {}, dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return getProductTagColors({ customerId, all: 1, ...params })
    },
  }),

  createProductTagColor: (
    { payload, successCallback, failureCallback },
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return createProductTagColor({ payload, customerId })
    },
    successCallback,
    failureCallback,
  }),
  deleteProductTagColor: (
    { id, successCallback, failureCallback },
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      try {
        const state = getState()
        const customerId = customerIdSelector(state)

        await deleteProductTagColor({ id, customerId })
      } catch ({ status, payload }) {
        const { message } = payload || {}

        if (status === 405) {
          setConfirm({
            title: l("Delete unavailable"),
            message,
            onOk: () => null,
            cancelButtonProps: { style: { display: "none" } },
            okText: l("Close"),
          })
        }
      }

      return {
        data: {},
      }
    },
    successCallback,
    failureCallback,
  }),
}

let actions

actions = generateActions(definitions, types)

export default actions
