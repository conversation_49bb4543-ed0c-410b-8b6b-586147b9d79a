import api from 'api'
import generateActions from 'utils/generateActions'

const { currenciesService: { getCurrencies } } = api

const actionPrefix = 'currencies'

export const types = {
  getAll: [
    `${actionPrefix}/get_all_request`,
    `${actionPrefix}/get_all_success`,
    `${actionPrefix}/get_all_failure`,
  ],
}

const definitions = {
  getAll: () => ({
    callApi: () => getCurrencies({ all: 1 }),
  }),
}

export default generateActions(definitions, types)
