import api from "api"

import { WidgetsInitialData } from "initialState"

import { withActionPrefix } from "actions/utils"

import { customerIdSelector } from "selectors/mainStateSelectors"

import generateActions from "utils/generateActions"

import { GridGeneral, ProductAggregatedSalesInfo } from "types/Models"

import { WidgetsDefinitions, WidgetsTypes } from "./widgetsActionsTypes"

const createActionType = withActionPrefix("widgets")

const {
  productAggregatedSalesInfoService: {
    getProductAggregatedSalesInfo,
    postProductAggregatedSalesInfoExport,
  },
} = api

export const types: WidgetsTypes = {
  getProductAggregatedSalesInfo: createActionType(
    "getProductAggregatedSalesInfo",
    true,
  ),
  getProductAggregatedSalesInfoModal: createActionType(
    "getProductAggregatedSalesInfoModal",
    true,
  ),
  clearProductAggregatedSalesInfoModal: createActionType(
    "clearProductAggregatedSalesInfoModal",
  ),
  postProductAggregatedSalesInfoExport: createActionType(
    "postProductAggregatedSalesInfoExport",
    true,
  ),
  clearProductAggregatedSalesInfoExport: createActionType(
    "clearProductAggregatedSalesInfoExport",
  ),
}

const definitions: WidgetsDefinitions = {
  getProductAggregatedSalesInfo: (
    { params, successCallback, failureCallback },
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState?.()
      const customerId = customerIdSelector(state)

      const response = await getProductAggregatedSalesInfo({
        customerId,
        params,
      })

      return (
        Array.isArray(response?.data?.data)
          ? response
          : { data: WidgetsInitialData }
      ) as GridGeneral<ProductAggregatedSalesInfo[]>
    },
    successCallback,
    failureCallback,
  }),
  getProductAggregatedSalesInfoModal: (
    { params, successCallback, failureCallback },
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const response = await getProductAggregatedSalesInfo({
        customerId,
        params,
      })

      return (
        Array.isArray(response?.data?.data)
          ? response
          : { data: WidgetsInitialData }
      ) as GridGeneral<ProductAggregatedSalesInfo[]>
    },
    successCallback,
    failureCallback,
  }),
  clearProductAggregatedSalesInfoModal: () => {},
  postProductAggregatedSalesInfoExport: (
    { params, successCallback, failureCallback },
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const response = await postProductAggregatedSalesInfoExport({
        customerId,
        params,
      })

      return response?.data?.id || null
    },
    successCallback,
    failureCallback,
  }),
  clearProductAggregatedSalesInfoExport: () => {},
}

const widgetsActions = generateActions(definitions, types) as WidgetsDefinitions

export { widgetsActions }
