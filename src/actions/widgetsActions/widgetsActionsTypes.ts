import { ProductAggregatedSalesInfoRequestParams } from "types/RequestParams"

import {
  ActionAbstractDefinitionType,
  ActionAsyncDefinitionType,
  ActionsTypes,
} from "actions/types"

export type WidgetsActionsAsyncTypeNames =
  | "getProductAggregatedSalesInfo"
  | "getProductAggregatedSalesInfoModal"
  | "postProductAggregatedSalesInfoExport"

export type WidgetsActionsSyncTypeNames =
  | "clearProductAggregatedSalesInfoModal"
  | "clearProductAggregatedSalesInfoExport"

export type WidgetsTypes = ActionsTypes<
  WidgetsActionsAsyncTypeNames,
  WidgetsActionsSyncTypeNames
>

export type WidgetsDefinitions = {
  getProductAggregatedSalesInfo: ActionAsyncDefinitionType<ProductAggregatedSalesInfoRequestParams>
  getProductAggregatedSalesInfoModal: ActionAsyncDefinitionType<ProductAggregatedSalesInfoRequestParams>
  clearProductAggregatedSalesInfoModal: ActionAbstractDefinitionType<[]>
  postProductAggregatedSalesInfoExport: ActionAsyncDefinitionType<ProductAggregatedSalesInfoRequestParams>
  clearProductAggregatedSalesInfoExport: ActionAbstractDefinitionType<[]>
}
