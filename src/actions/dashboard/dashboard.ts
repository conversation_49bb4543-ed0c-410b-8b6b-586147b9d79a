import { withActionPrefix } from "actions/utils"

import generateActions from "utils/generateActions"

import { DefaultValuesStatus } from "types/Dashboard"

import { DashboardDefinitions, DashboardTypes } from "./dashboardTypes"

const createActionType = withActionPrefix("dashboard")

export const types: DashboardTypes = {
  setDefaultValuesStatus: createActionType("setDefaultValuesStatus"),
}

const definitions: DashboardDefinitions = {
  setDefaultValuesStatus: (defaultValuesStatus: DefaultValuesStatus) =>
    defaultValuesStatus,
}

const dashboardActions = generateActions(
  definitions,
  types,
) as DashboardDefinitions

export { dashboardActions }
