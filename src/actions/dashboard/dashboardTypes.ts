import { ActionAbstractDefinitionType, ActionsTypes } from "actions/types"

export type DashboardActionsAsyncTypeNames = never

export type DashboardActionsSyncTypeNames = "setDefaultValuesStatus"

export type DashboardTypes = ActionsTypes<
  DashboardActionsAsyncTypeNames,
  DashboardActionsSyncTypeNames
>

export type DashboardDefinitions = {
  setDefaultValuesStatus: ActionAbstractDefinitionType
}
