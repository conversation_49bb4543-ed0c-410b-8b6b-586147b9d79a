import {
  getObjectKeys,
  getUrlSearchParams,
} from "@develop/fe-library/dist/utils"

import api from "api"

import gridActions from "actions/gridActions"

import { customerIdSelector } from "selectors/mainStateSelectors"

import generateActions from "utils/generateActions"

const actionPrefix = "indirectCosts"

const {
  indirectCostsService: {
    getIndirectCosts,
    deleteIndirectCost,
    deleteIndirectCosts,
    createIndirectCost,
    getIndirectCostTypes,
    updateIndirectCost,
    createIndirectCostType,
    updateIndirectCostType,
    deleteIndirectCostType,
  },
} = api

export const types = {
  setCostModalVisibility: `${actionPrefix}/set_cost_modal_visibility`,
  updateUrlParams: `${actionPrefix}/update_url_params`,
  changeSearchOptions: `${actionPrefix}/change_search_options`,
  updateIndirectCostsGrid: [
    `${actionPrefix}/update_indirect_costs_grid_request`,
    `${actionPrefix}/update_indirect_costs_grid_success`,
    `${actionPrefix}/update_indirect_costs_grid_failure`,
  ],
  getIndirectCosts: [
    `${actionPrefix}/get_indirect_costs_request`,
    `${actionPrefix}/get_indirect_costs_success`,
    `${actionPrefix}/get_indirect_costs_failure`,
  ],
  createIndirectCost: [
    `${actionPrefix}/create_indirect_cost_request`,
    `${actionPrefix}/create_indirect_cost_success`,
    `${actionPrefix}/create_indirect_cost_failure`,
  ],
  updateIndirectCost: [
    `${actionPrefix}/update_indirect_cost_request`,
    `${actionPrefix}/update_indirect_cost_success`,
    `${actionPrefix}/update_indirect_cost_failure`,
  ],
  deleteIndirectCost: [
    `${actionPrefix}/delete_indirect_cost_request`,
    `${actionPrefix}/delete_indirect_cost_success`,
    `${actionPrefix}/delete_indirect_cost_failure`,
  ],
  deleteIndirectCosts: [
    `${actionPrefix}/delete_indirect_costs_request`,
    `${actionPrefix}/delete_indirect_costs_success`,
    `${actionPrefix}/delete_indirect_costs_failure`,
  ],
  getIndirectCostTypes: [
    `${actionPrefix}/get_indirect_cost_types_request`,
    `${actionPrefix}/get_indirect_cost_types_success`,
    `${actionPrefix}/get_indirect_cost_types_failure`,
  ],
  createIndirectCostType: [
    `${actionPrefix}/create_indirect_cost_type_request`,
    `${actionPrefix}/create_indirect_cost_type_success`,
    `${actionPrefix}/create_indirect_cost_type_failure`,
  ],
  updateIndirectCostType: [
    `${actionPrefix}/update_indirect_cost_type_request`,
    `${actionPrefix}/update_indirect_cost_type_success`,
    `${actionPrefix}/update_indirect_cost_type_failure`,
  ],
  deleteIndirectCostType: [
    `${actionPrefix}/delete_indirect_cost_type_request`,
    `${actionPrefix}/delete_indirect_cost_type_success`,
    `${actionPrefix}/delete_indirect_cost_type_failure`,
  ],
}

let actions

const definitions = {
  setCostModalVisibility: (params) => params,
  updateUrlParams: (params) => params,
  changeSearchOptions: (searchOptions) => searchOptions,
  getIndirectCosts: ({ searchOptions = {} }, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      dispatch(actions.changeSearchOptions(searchOptions))

      const indirectCosts = await getIndirectCosts({
        customerId,
        ...searchOptions,
      })

      if (indirectCosts?.data) {
        dispatch(gridActions.setGridData(indirectCosts?.data))
      }

      return indirectCosts
    },
  }),
  updateIndirectCostsGrid: (params = {}, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)
      const hasParams = params && !!getObjectKeys(params).length
      const searchParams = hasParams
        ? params
        : getUrlSearchParams({
            locationSearch: document.location.search,
          })

      const indirectCosts = await getIndirectCosts({
        customerId,
        ...searchParams,
      })

      if (indirectCosts?.data) {
        dispatch(gridActions.setGridData(indirectCosts?.data))
      }

      return indirectCosts
    },
  }),
  deleteIndirectCost: (costId, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      await deleteIndirectCost({
        customerId,
        costId,
      })

      dispatch(actions.updateIndirectCostsGrid(null))
    },
  }),
  deleteIndirectCosts: (
    { ids = [], isSelectedTotal = false } = {},
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      await deleteIndirectCosts({
        customerId,
        ids,
        side: isSelectedTotal ? "all" : "selected",
      })

      dispatch(actions.updateIndirectCostsGrid(null))
    },
  }),
  updateIndirectCost: (
    params,
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)
      const { id: costId, ...values } = params

      const updatedProduct = await updateIndirectCost({
        customerId,
        costId,
        values,
      })

      if (updatedProduct?.data) {
        dispatch(gridActions.updateGridRow(updatedProduct?.data))
      }

      return updatedProduct
    },
    successCallback,
    failureCallback,
  }),
  createIndirectCost: (
    params = {},
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const newProduct = await createIndirectCost({
        customerId,
        values: params,
      })

      dispatch(actions.updateIndirectCostsGrid(null))

      return newProduct
    },
    successCallback,
    failureCallback,
  }),
  getIndirectCostTypes: (dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return getIndirectCostTypes({
        customerId,
      })
    },
  }),
  createIndirectCostType: (
    values = {},
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return createIndirectCostType({
        customerId,
        values,
      })
    },
    successCallback: () => {
      dispatch(actions.getIndirectCostTypes())

      if (successCallback) {
        successCallback()
      }
    },
    failureCallback,
  }),
  updateIndirectCostType: (params = {}, dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)
      const { id: costId, ...values } = params

      return updateIndirectCostType({
        customerId,
        costId,
        values,
      })
    },
  }),
  deleteIndirectCostType: (costId, successCallback, dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return deleteIndirectCostType({
        customerId,
        costId,
      })
    },
    successCallback: () => {
      dispatch(actions.getIndirectCostTypes())

      if (successCallback) {
        successCallback()
      }
    },
  }),
}

actions = generateActions(definitions, types)

export default actions
