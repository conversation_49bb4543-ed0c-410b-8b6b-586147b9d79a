import api from "api"

import { customerIdSelector } from "selectors/mainStateSelectors"

import generateActions from "utils/generateActions"

const actionPrefix = "recurrentImportSettings"

const {
  recurrentImportSettingsService: {
    getRecurrentImportSettings,
    addRecurrentImportSetting,
    updateRecurrentImportSetting,
    deleteRecurrentImportSetting,
  },
} = api

export const types = {
  getRecurrentImportSettings: [
    `${actionPrefix}/get_request`,
    `${actionPrefix}/get_success`,
    `${actionPrefix}/get_failure`,
  ],
  addRecurrentImportSetting: [
    `${actionPrefix}/add_request`,
    `${actionPrefix}/add_success`,
    `${actionPrefix}/add_failure`,
  ],
  updateRecurrentImportSetting: [
    `${actionPrefix}/update_request`,
    `${actionPrefix}/update_success`,
    `${actionPrefix}/update_failure`,
  ],
  changeStatusForRecurrentImportSetting: [
    `${actionPrefix}/change_status_request`,
    `${actionPrefix}/change_status_success`,
    `${actionPrefix}/change_status_failure`,
  ],
  deleteRecurrentImportSetting: [
    `${actionPrefix}/delete_request`,
    `${actionPrefix}/delete_success`,
    `${actionPrefix}/delete_failure`,
  ],
  displayRecurrentImportSettingModal: `${actionPrefix}/display_modal`,
  showRecurrentImportSettingConfirmationModal: `${actionPrefix}/show_confirmation_modal`,
}

const definitions = {
  getRecurrentImportSettings: (successCallback, dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return getRecurrentImportSettings({ customerId })
    },
    successCallback,
  }),
  addRecurrentImportSetting: (
    payload,
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return addRecurrentImportSetting({
        customerId,
        payload: {
          ...payload,
        },
      })
    },
    successCallback: (data) => {
      dispatch({
        type: types.getRecurrentImportSettings,
        payload: {
          ...definitions.getRecurrentImportSettings(
            () => successCallback(data),
            dispatch,
            getState,
          ),
        },
      })
    },
    failureCallback,
  }),
  updateRecurrentImportSetting: (
    payload,
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return updateRecurrentImportSetting({
        customerId,
        id: payload.id,
        payload: {
          ...payload,
          is_enabled: !payload.is_enabled ? 0 : 1,
        },
      })
    },
    successCallback,
    failureCallback,
  }),
  changeStatusForRecurrentImportSetting: (
    payload,
    successCallback,
    failureCallback,
    dispatch,
    getState,
  ) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return updateRecurrentImportSetting({
        customerId,
        id: payload.id,
        payload: {
          ...payload,
          is_enabled: !payload.is_enabled ? 1 : 0,
        },
      })
    },
    successCallback,
    failureCallback,
  }),
  deleteRecurrentImportSetting: (id, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      await deleteRecurrentImportSetting({ customerId, id })

      return {
        data: id,
      }
    },
  }),
  displayRecurrentImportSettingModal: (modalVisible = false) => ({
    modalVisible,
  }),
  showRecurrentImportSettingConfirmationModal: (
    confirmationModalVisible = false,
  ) => ({
    confirmationModalVisible,
  }),
}

export default generateActions(definitions, types)
