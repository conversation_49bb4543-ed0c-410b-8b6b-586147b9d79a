type EditFieldFormProps = {
  formType: string | null
  isVisible: boolean
  productId: any
}

declare namespace GridActions {
  function showEditFieldForm(props: EditFieldFormProps): void
  function onUpdateProduct(
    id: any,
    values: object,
    callback: () => void,
    errorCallback: () => void,
  ): void
  function pushUrl<T extends Record<string, any>>(url: T): void
}

export default { ...GridActions }
