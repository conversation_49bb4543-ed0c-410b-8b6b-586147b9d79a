import api from "api"
import { getOrderStatuses } from "api/ordersService"

import { OrderItemsTableInitialData } from "initialState/orderItemsTable"

import { withActionPrefix } from "actions/utils"

import { customerIdSelector } from "selectors/mainStateSelectors"

import generateActions from "utils/generateActions"

import { GridGeneral } from "types"
import { AmazonOrderExtendedViewItem } from "types/Models/AmazonOrderExtendedViewItem"

import {
  OrderItemsTableDefinitions,
  OrderItemsTableTypes,
} from "./orderItemsTableActionsTypes"

const {
  ordersService: { getOrders },
} = api

const createActionType = withActionPrefix("orderItemsTable")

export const types: OrderItemsTableTypes = {
  getAmazonOrders: createActionType("getAmazonOrders", true),
  getAmazonOrderStatuses: createActionType("getAmazonOrderStatuses", true),
  clearAmazonOrdersData: createActionType("clearAmazonOrdersData"),
  clearAmazonOrderStatuses: createActionType("clearAmazonOrderStatuses"),
  changeSearchOptions: createActionType("changeSearchOptions"),
}

let orderItemsTableActions: OrderItemsTableDefinitions

const definitions: OrderItemsTableDefinitions = {
  getAmazonOrders: (
    { params = {}, successCallback = () => {}, failureCallback = () => {} },
    dispatch,
    getState,
  ) => ({
    callApi: async (): Promise<GridGeneral<AmazonOrderExtendedViewItem[]>> => {
      const state = getState()
      const customerId = customerIdSelector(state)

      // TODO: For V1 just replace getOrders with getAmazonOrderV1 from orderItemsService
      const response = await getOrders({
        ...params,
        customerId,
      })

      return (
        Array.isArray(response?.data?.data)
          ? response
          : { data: OrderItemsTableInitialData }
      ) as GridGeneral<AmazonOrderExtendedViewItem[]>
    },
    successCallback,
    failureCallback,
  }),
  getAmazonOrderStatuses: (
    { successCallback = () => {}, failureCallback = () => {} },
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      return getOrderStatuses({ customerId })
    },
    successCallback,
    failureCallback,
  }),
  clearAmazonOrdersData: () => {},
  clearAmazonOrderStatuses: () => {},
  changeSearchOptions: (searchOptions) => searchOptions,
}

orderItemsTableActions = generateActions(
  definitions,
  types,
) as OrderItemsTableDefinitions

export { orderItemsTableActions }
