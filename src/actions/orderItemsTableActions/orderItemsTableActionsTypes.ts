import { AmazonOrderRequestType } from "types"

import {
  ActionAbstractDefinitionType,
  ActionAsyncDefinitionType,
  ActionsTypes,
} from "actions/types"

export type OrderItemsTableActionsAsyncTypeNames =
  | "getAmazonOrders"
  | "getAmazonOrderStatuses"

export type OrderItemsTableActionsSyncTypeNames =
  | "changeSearchOptions"
  | "clearAmazonOrdersData"
  | "clearAmazonOrderStatuses"

export type OrderItemsTableTypes = ActionsTypes<
  OrderItemsTableActionsAsyncTypeNames,
  OrderItemsTableActionsSyncTypeNames
>

export type OrderItemsTableDefinitions = {
  getAmazonOrders: ActionAsyncDefinitionType<AmazonOrderRequestType>
  getAmazonOrderStatuses: ActionAsyncDefinitionType<AmazonOrderRequestType>
  clearAmazonOrdersData: ActionAbstractDefinitionType
  clearAmazonOrderStatuses: ActionAbstractDefinitionType
  changeSearchOptions: ActionAbstractDefinitionType
}
