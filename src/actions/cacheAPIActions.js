import { push, replace } from "connected-react-router"
import { ROUTES } from "@develop/fe-library/dist/routes"

import generateActions from "utils/generateActions.js"

import { routerSelector } from "selectors/mainStateSelectors"

import getMainApp from "components/MainAppProvider"

const actionPrefix = "cacheAPI"

export const types = {
  cacheAction: `${actionPrefix}/cache_action`,
  purgeCache: [
    `${actionPrefix}/purge_cache_request`,
    `${actionPrefix}/purge_cache_success`,
    `${actionPrefix}/purge_cache_failure`,
  ],
  purgeEndpointCache: `${actionPrefix}/purge_endpoint_cache`,
}

const definitions = {
  cacheAction: (payload, type) => ({ payload, type }),
  purgeCache: (reload, callback, dispatch, getState) => ({
    callApi: async () => ({
      data: {},
    }),
    callback: () => {
      callback && setTimeout(() => callback(), 1000)

      if (reload) {
        const {
          location: { hash, pathname },
        } = routerSelector()

        const { actions: mainAppActions } = getMainApp()
        dispatch(
          mainAppActions?.getCurrentUser?.(() => {
            dispatch(push(ROUTES.GENERAL_ROUTES.PATH_EMPTY))
            dispatch(replace(`${pathname}${hash}`))
          }),
        )
      }
    },
  }),
  purgeEndpointCache: (endpoint) => ({ endpoint }),
}

export default generateActions(definitions, types)
