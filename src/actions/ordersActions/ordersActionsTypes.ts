import { Dispatch } from "redux"

import { OrdersUrlParams } from "types"
import { RootState } from "types/store/store"

import {
  ActionAbstractDefinitionType,
  ActionAsyncDefinitionType,
  ActionTypeAsync,
  SuccessCallbackType,
} from "actions/types"
import {
  GetAmazonOrderDetailsResponse,
  GetAmazonOrderExpensesBreakdownResponse,
  GetAmazonOrderFeesBreakdownResponse,
  GetAmazonOrderLastUpdateDateResponse,
  GetAmazonOrderStatusesResponse,
} from "api/ordersService/ordersServiceTypes"

export type GetOrdersParams = {
  searchOptions: OrdersUrlParams
}

export type GetOrdersCallbackParams = {
  searchOptions: OrdersUrlParams
  successCallback: SuccessCallbackType
  dispatch: Dispatch
  getState: RootState
}

export type DisplayModalParams = {
  isModalVisible: boolean
  modalName?: string | null
  currentOrderItemId?: string | null
  currentOrderId?: string | null
  currentOrderCurrencyId?: string | null
}

export type AmountCostsPayload = {
  [key: string]: any
}

export type OrdersActionsAsyncTypeNames =
  | "getOrders"
  | "getOrderStatuses"
  | "getLastUpdateOrdersDate"
  | "getAmazonOrderFeesBreakdown"
  | "getAmazonOrderExpensesBreakdown"
  | "getAmazonOrderDetails"
  | "getAmazonOrderAmountCosts"
  | "updateAmazonOrderAmountCosts"

export type OrdersActionsSyncTypeNames =
  | "displayModal"
  | "updateUrlParams"
  | "changeSearchOptions"

export type OrdersActionTypesKeys =
  | OrdersActionsAsyncTypeNames
  | OrdersActionsSyncTypeNames

export type OrdersActionTypes = {
  [Key in OrdersActionsAsyncTypeNames]: ActionTypeAsync
} & {
  [Key in OrdersActionsSyncTypeNames]: string
}

type GetAmazonOrderAmountCostsParams = {
  amazonOrderId: string
}

type UpdateAmazonOrderAmountCostsParams = {
  amazonOrderId: string
  payload: AmountCostsPayload
}

export type OrdersDefinitions = {
  displayModal: ActionAbstractDefinitionType<DisplayModalParams>
  updateUrlParams: ActionAbstractDefinitionType<OrdersUrlParams>
  changeSearchOptions: ActionAbstractDefinitionType<OrdersUrlParams>
  getLastUpdateOrdersDate: ActionAsyncDefinitionType<GetAmazonOrderLastUpdateDateResponse>
  getOrderStatuses: ActionAsyncDefinitionType<GetAmazonOrderStatusesResponse>
  getOrders: ActionAsyncDefinitionType<GetOrdersParams>
  getAmazonOrderFeesBreakdown: ActionAsyncDefinitionType<GetAmazonOrderFeesBreakdownResponse>
  getAmazonOrderExpensesBreakdown: ActionAsyncDefinitionType<GetAmazonOrderExpensesBreakdownResponse>
  getAmazonOrderDetails: ActionAsyncDefinitionType<GetAmazonOrderDetailsResponse>
  getAmazonOrderAmountCosts: ActionAsyncDefinitionType<GetAmazonOrderAmountCostsParams>
  updateAmazonOrderAmountCosts: ActionAsyncDefinitionType<UpdateAmazonOrderAmountCostsParams>
}
