import api from "api"

import { customerIdSelector } from "selectors/mainStateSelectors"

import { downloadFile } from "utils/downloadFile"
import generateActions from "utils/generateActions"
import l from "utils/intl"

import { TEMPLATE_HANDLER_NAMES } from "constants/exportTemplates"

import gridActions from "./gridActions"

const actionPrefix = "productImport"

const {
  productImportService: {
    deleteProductImport,
    downloadTemplateFile,
    getProductImport,
    getProductImports,
    uploadProductImport,
    generatePreSignedUrl,
  },
} = api

const { changeSearchOptions: updateGridSearchOptions } = gridActions

export const types = {
  setProcessingImportId: `${actionPrefix}/set_processing_import_id`,
  changeSearchOptions: `${actionPrefix}/change_search_options`,
  closeErrorsModal: `${actionPrefix}/close_errors_modal`,
  deleteItem: [
    `${actionPrefix}/delete_request`,
    `${actionPrefix}/delete_success`,
    `${actionPrefix}/delete_failure`,
  ],
  displayUploadModal: `${actionPrefix}/display_upload_modal`,
  displayDownloadTemplateModal: `${actionPrefix}/display_download_template_modal`,
  downloadTemplateFile: [
    `${actionPrefix}/download_request`,
    `${actionPrefix}/download_success`,
    `${actionPrefix}/download_failure`,
  ],
  getProductImports: [
    `${actionPrefix}/get_request`,
    `${actionPrefix}/get_success`,
    `${actionPrefix}/get_failure`,
  ],
  getImportDataItemById: [
    `${actionPrefix}/get_by_id_request`,
    `${actionPrefix}/get_by_id_success`,
    `${actionPrefix}/get_by_id_failure`,
  ],
  uploadProductImport: [
    `${actionPrefix}/upload_request`,
    `${actionPrefix}/upload_success`,
    `${actionPrefix}/upload_failure`,
  ],
  getAllImports: [
    `${actionPrefix}/get_all_imports_request`,
    `${actionPrefix}/get_all_imports_success`,
    `${actionPrefix}/get_all_imports_failure`,
  ],
  generatePreSignedUrl: [
    `${actionPrefix}/generate_presigned_url_request`,
    `${actionPrefix}/generate_presigned_url_success`,
    `${actionPrefix}/generate_presigned_url_failure`,
  ],
}

let actions

const definitions = {
  getProductImports: (
    { searchOptions = {} },
    successCallback = () => {},
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      dispatch(updateGridSearchOptions(searchOptions))

      const data = await getProductImports({
        sort: "-created_at",
        customerId,
        ...searchOptions,
      })

      return data
    },
    successCallback,
  }),
  deleteItem: ({ id }, successCallback, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      await deleteProductImport({ id, customerId })
    },
    successCallback,
  }),
  changeSearchOptions: (searchOptions) => searchOptions,
  displayUploadModal: (uploadModalVisible = false) => ({
    uploadModalVisible,
  }),
  displayDownloadTemplateModal: (isDownloadTemplateModalVisible = false) => ({
    isDownloadTemplateModalVisible,
  }),
  downloadTemplateFile: (
    payload = {},
    successCallback,
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      try {
        const { blob: file, fileName } = await downloadTemplateFile({
          customerId,
          ...payload,
        })

        downloadFile(fileName, file)

        return { data: {} }
        // copied from the same file from other actions. like generatePreSignedUrl
      } catch ({ status, payload }) {
        if (status === 422) {
          const error = {
            status,
            payload: {
              message: payload?.[0]?.message,
            },
          }

          throw error
        }

        throw (
          payload ?? {
            payload: {
              message: l("Something went wrong"),
            },
          }
        )
      }
    },
    successCallback,
  }),
  getAllImports: (params = {}, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const data = await getProductImports({
        customerId,
        handler_name: TEMPLATE_HANDLER_NAMES.PRODUCT_COST_PERIODS,
      })

      return data
    },
    doNotShowSpin: true,
  }),
  getImportDataItemById: ({ id }, successCallback, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const { data } = await getProductImport({ id, customerId })

      return data
    },
    successCallback,
  }),
  uploadProductImport: (formData, successCallback, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      try {
        const data = await uploadProductImport({
          data: formData,
          customerId,
        })

        return data
      } catch ({ status, payload }) {
        if (status === 422) {
          const error = {
            status,
            payload: {
              message: payload[0].message,
            },
          }

          throw error
        }

        throw (
          payload ?? {
            payload: {
              message: l("Something went wrong"),
            },
          }
        )
      }
    },
    successCallback: (data) => {
      dispatch(actions.setProcessingImportId(data))
      successCallback && successCallback()
    },
  }),
  setProcessingImportId: ({ id }) => ({ id }),
  generatePreSignedUrl: ({ objectKey, objectType }, successCallback) => ({
    callApi: async () => {
      try {
        const response = await generatePreSignedUrl({ objectKey, objectType })

        if (response.data) {
          return response
        }

        return {
          data: {},
        }
      } catch ({ status, payload }) {
        if (status === 422) {
          const error = {
            status,
            payload: {
              message: payload[0].message,
            },
          }

          throw error
        }

        throw (
          payload ?? {
            payload: {
              message: l("Something went wrong"),
            },
          }
        )
      }
    },
    successCallback,
  }),
}

actions = generateActions(definitions, types)

export default actions
