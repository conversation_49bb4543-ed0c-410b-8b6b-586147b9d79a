import api from "api"

import { customerIdSelector } from "selectors/mainStateSelectors"
import { marketplaceGroupsSelector } from "selectors/marketplaceSelectors"

import { formatSellerIds } from "utils/formatSellerIdsQuery"
import generateActions from "utils/generateActions"
import { getFormattedRequestDateRange } from "utils/transactions"

import { SALES_CATEGORY_STRATEGIES } from "constants/general"

const actionPrefix = "transactions"

const {
  transactionsService: { getTransactions, getCategoryFilters },
} = api

export const types = {
  updateUrlParams: `${actionPrefix}/update_url_params`,
  changeSearchOptions: `${actionPrefix}/change_search_options`,
  clearSearchOptions: `${actionPrefix}/clear_search_options`,
  displayModal: `${actionPrefix}/display_modal`,
  showConfirmationModal: `${actionPrefix}/show_confirmation_modal`,
  getTransactions: [
    `${actionPrefix}/get_transactions_request`,
    `${actionPrefix}/get_transactions_success`,
    `${actionPrefix}/get_transactions_failure`,
  ],
  getCategoryFilters: [
    `${actionPrefix}/get_category_filters_request`,
    `${actionPrefix}/get_category_filters_success`,
    `${actionPrefix}/get_category_filters_failure`,
  ],
}

let actions

const definitions = {
  updateUrlParams: (params) => params,
  changeSearchOptions: (searchOptions) => searchOptions,
  clearSearchOptions: () => {
    return {}
  },
  displayModal: (modalVisible = false, initialValues) => ({
    modalVisible,
    initialValues,
  }),
  showConfirmationModal: (confirmationModalVisible = false, initialValues) => ({
    confirmationModalVisible,
    initialValues,
  }),
  getCategoryFilters: (params = {}, dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      const {
        sales_category_depth_1,
        sales_category_depth_2,
        transaction_type,
      } = params

      return getCategoryFilters({
        customerId,
        sales_category_depth_1,
        sales_category_depth_2,
        transaction_type,
        sales_category_strategy: SALES_CATEGORY_STRATEGIES.custom,
      })
    },
  }),
  getTransactions: (
    { searchOptions = {} },
    successCallback,
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const state = getState()
      const customerId = customerIdSelector(state)
      const groups = marketplaceGroupsSelector(state)
      const {
        from,
        to,
        groupId,
        sellerId,
        marketplaces,
        marketplace_id,
        transaction_date,
        posted_date,
        ...requestOptions
      } = searchOptions

      dispatch(actions.changeSearchOptions(searchOptions))

      const transactionDate = getFormattedRequestDateRange({
        rangeDateString: transaction_date,
        dateFrom: from,
        dateTo: to,
        convertToUTC: true,
      })

      const postedDate = posted_date
        ? {
            posted_date: getFormattedRequestDateRange({
              rangeDateString: posted_date,
              dateFrom: from,
              dateTo: to,
              convertToUTC: true,
            }),
          }
        : {}

      const marketplace_seller_ids = JSON.stringify(
        formatSellerIds(searchOptions, groups),
      )

      let marketplacesList

      const isBothMarketplaces = marketplaces && marketplace_id

      if (isBothMarketplaces) {
        marketplacesList = marketplaces.split(",").includes(marketplace_id)
          ? marketplace_id
          : "inconsistentMarketplaces"
      } else {
        marketplacesList = marketplace_id || marketplaces
      }

      const response = await getTransactions({
        ...requestOptions,
        ...postedDate,
        customerId,
        transaction_date: transactionDate,
        marketplace_id: marketplacesList,
        marketplace_seller_ids,
      })

      return response
    },
    successCallback,
  }),
}

actions = generateActions(definitions, types)

export default actions
