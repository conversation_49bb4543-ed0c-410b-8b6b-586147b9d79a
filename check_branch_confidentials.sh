#!/bin/bash

#
# Скрипт для проверки текущей ветки на наличие конфиденциальной информации
# Использует ту же логику, что и hook_block_confidentials.sh
#

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🔍 Проверка текущей ветки на конфиденциальную информацию...${NC}"

# Получаем текущую ветку
current_branch=$(git branch --show-current)
echo -e "Текущая ветка: ${YELLOW}${current_branch}${NC}"

# Получаем базовую ветку для мерджа
base_branch="BAS-2328-epic"

# Проверяем, что базовая ветка существует
if ! git show-ref --verify --quiet refs/remotes/origin/${base_branch}; then
    echo -e "${RED}❌ Не найдена базовая ветка ${base_branch}${NC}"
    exit 1
fi

# Обновляем информацию о remote ветках
echo -e "${YELLOW}📡 Обновление информации о remote ветках...${NC}"
git fetch origin ${base_branch}:${base_branch} 2>/dev/null || git fetch origin

echo -e "Базовая ветка: ${YELLOW}${base_branch}${NC}"

# Проверяем, что базовая ветка доступна локально
if ! git show-ref --verify --quiet refs/heads/${base_branch}; then
    echo -e "${YELLOW}⚠️  Локальная ветка ${base_branch} не найдена, используем origin/${base_branch}${NC}"
    base_ref="origin/${base_branch}"
else
    base_ref="${base_branch}"
fi

# Получаем список коммитов для проверки (коммиты в текущей ветке, которых нет в базовой)
commits=$(git rev-list ${base_ref}..HEAD)

if [ -z "$commits" ]; then
    echo -e "${GREEN}✅ Нет новых коммитов для проверки${NC}"
    exit 0
fi

echo -e "Найдено коммитов для проверки: $(echo "$commits" | wc -l)"

# Те же regex паттерны, что и в оригинальном хуке
regex_list=(
  # block any private key file
  '(\-){5}BEGIN\s?(RSA|OPENSSH|DSA|EC|PGP)?\s?PRIVATE KEY\s?(BLOCK)?(\-){5}.*'
  # block AWS API Keys
  'AKIA[0-9A-Z]{16}'
  # block AWS Secret Access Key (avoiding Git SHA1 false positives)
#   '(^|[^A-Za-z0-9/+=])[A-Za-z0-9/+=]{40}([^A-Za-z0-9/+=]|$)'
  # block confidential content
  'CONFIDENTIAL'
)

# Объединяем regex
separator="|"
regex="$( printf "${separator}%s" "${regex_list[@]}" )"
regex="${regex:${#separator}}"

# Счетчик найденных проблем
found=0
issues=()

echo -e "\n${YELLOW}🔍 Проверка коммитов...${NC}"

# Проверяем каждый коммит
for sha1 in ${commits}; do
    echo -n "Проверка коммита ${sha1:0:8}... "
    
    # Ищем совпадения в diff
    match=$(git diff-tree -r -p --no-color --no-commit-id --diff-filter=d ${sha1} | grep -nE "(${regex})")
    
    if [ -n "$match" ]; then
        # Дополнительная проверка для AWS Secret Access Key
        filtered_match=$(echo "${match}" | grep -vE '(^|[^A-Za-z0-9/+=])[0-9a-fA-F]{40}([^A-Za-z0-9/+=]|$)')
        
        if [ -n "$filtered_match" ]; then
            echo -e "${RED}❌ НАЙДЕНА ПРОБЛЕМА${NC}"
            found=$((found + 1))
            
            # Сохраняем информацию о проблеме
            commit_msg=$(git log --format="%s" -n 1 ${sha1})
            issues+=("Коммит: ${sha1:0:8} - ${commit_msg}")
            issues+=("${filtered_match}")
            issues+=("---")
        else
            echo -e "${GREEN}✅${NC}"
        fi
    else
        echo -e "${GREEN}✅${NC}"
    fi
done

echo ""

# Выводим результаты
if [ ${found} -gt 0 ]; then
    echo -e "${RED}❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ (${found})${NC}"
    echo -e "${RED}Ваша ветка будет заблокирована pre-receive хуком!${NC}"
    echo ""
    echo -e "${YELLOW}Детали проблем:${NC}"
    for issue in "${issues[@]}"; do
        echo -e "${RED}${issue}${NC}"
    done
    echo ""
    echo -e "${YELLOW}💡 Рекомендации:${NC}"
    echo "1. Удалите конфиденциальную информацию из кода"
    echo "2. Используйте переменные окружения для секретов"
    echo "3. Добавьте секреты в .gitignore"
    echo "4. Если нужно, используйте git rebase -i для изменения истории"
    exit 1
else
    echo -e "${GREEN}✅ ВСЕ ПРОВЕРКИ ПРОЙДЕНЫ${NC}"
    echo -e "${GREEN}Ваша ветка готова к push!${NC}"
    exit 0
fi
