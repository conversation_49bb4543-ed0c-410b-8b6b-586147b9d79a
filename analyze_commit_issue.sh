#!/bin/bash

# Скрипт для детального анализа проблемного коммита

commit_sha="a5521e32"

echo "🔍 Анализ коммита ${commit_sha}..."
echo ""

# Те же regex паттерны, что и в оригинальном хуке
regex_list=(
  # block any private key file
  '(\-){5}BEGIN\s?(RSA|OPENSSH|DSA|EC|PGP)?\s?PRIVATE KEY\s?(BLOCK)?(\-){5}.*'
  # block AWS API Keys
  'AKIA[0-9A-Z]{16}'
  # block AWS Secret Access Key (avoiding Git SHA1 false positives)
  '(^|[^A-Za-z0-9/+=])[A-Za-z0-9/+=]{40}([^A-Za-z0-9/+=]|$)'
  # block confidential content
  'CONFIDENTIAL'
)

echo "🔍 Проверяем каждый паттерн отдельно:"
echo ""

for i in "${!regex_list[@]}"; do
    pattern="${regex_list[$i]}"
    echo "Паттерн $((i+1)): ${pattern}"
    
    match=$(git diff-tree -r -p --no-color --no-commit-id --diff-filter=d ${commit_sha} | grep -nE "${pattern}")
    
    if [ -n "$match" ]; then
        echo "❌ НАЙДЕНО СОВПАДЕНИЕ:"
        echo "$match"
        echo ""
        
        # Показываем больше контекста для этого паттерна
        echo "📄 Контекст (±5 строк):"
        git diff-tree -r -p --no-color --no-commit-id --diff-filter=d ${commit_sha} | grep -nE "${pattern}" -A 5 -B 5
        echo ""
        echo "---"
    else
        echo "✅ Совпадений не найдено"
    fi
    echo ""
done

echo ""
echo "🔍 Полный diff коммита:"
git show ${commit_sha}
